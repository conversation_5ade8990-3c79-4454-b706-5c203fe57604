<?php
$servername = "localhost";
$username = "root";
$password = "891e956c1bec2f40";
$dbmaster = "1_ripit";  // Nama database master
$dbclient = "master";    // Nama database client

// Buat koneksi ke database master
$conn_master = new mysqli($servername, $username, $password, $dbmaster);

// Cek koneksi ke database master
if ($conn_master->connect_error) {
    die("Connection to master failed: " . $conn_master->connect_error);
} else {
    echo "Connected to master successfully\n";
}

// Buat koneksi ke database client
$conn_client = new mysqli($servername, $username, $password, $dbclient);

// Cek koneksi ke database client
if ($conn_client->connect_error) {
    die("Connection to client failed: " . $conn_client->connect_error);
} else {
    echo "Connected to client successfully\n";
}

// Ambil daftar tabel dari database master
$sql = "SHOW TABLES";
$result = $conn_master->query($sql);

if ($result->num_rows > 0) {
    while ($row = $result->fetch_array()) {
        $table = $row[0];
        
        // Drop table jika sudah ada di database client
        $conn_client->query("DROP TABLE IF EXISTS $dbclient.$table");

        // Duplicate struktur tabel
        $conn_client->query("CREATE TABLE IF NOT EXISTS $dbclient.$table LIKE $dbmaster.$table");

        // Cek apakah tabel tersebut adalah salah satu yang perlu data (provinsi, kota, kecamatan)
        if (in_array($table, ['app_origin_provinsis', 'app_origin_cities', 'app_origin_kecamatans', 'wa_providers'])) {
            // Copy data dari master ke client
            $conn_client->query("INSERT INTO $dbclient.$table SELECT * FROM $dbmaster.$table");
            echo "Table $table structure and data duplicated successfully\n";
        } else {
            // Hanya struktur saja, tidak ada data yang disalin
            echo "Table $table structure duplicated successfully (no data copied)\n";
        }
    }
} else {
    echo "No tables found in master database.";
}

$conn_master->close();
$conn_client->close();
?>
