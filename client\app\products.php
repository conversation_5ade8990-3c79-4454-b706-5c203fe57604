<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class products extends Model
{
    protected $table = 'products';

    // id 	code 	type 	token 	name 	description 	price 	price_discount 	discount 	price_reseller 	price_maklon 	weight 	weight_type 	max 	user_id 	company_id 	created_at 	updated_at 	status
    protected $fillable = [
        'name',
        'description',
        'price',
        'price_discount',
        'discount',
        'price_reseller',
        'price_maklon',
        'weight',
        'weight_type',
        'max',
        'user_id',
        'company_id',
        'status'
    ];
}
