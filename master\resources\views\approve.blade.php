<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Persetujuan</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .modal-content {
            font-size: 16px;
        }
        .modal-body p {
            font-size: 14px;
        }
        .table th, .table td {
            font-size: 14px;
            vertical-align: middle;
        }
        .form-control-plaintext {
            border: none;
            background: transparent;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Modal -->
    <div class="modal fade" id="approveModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{ $title }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>{{ $message }}</p>
                    
                    @if (!empty($result))
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <th style="width: 30%;">Project Key</th>
                                    <td>
                                        <input type="text" class="form-control-plaintext" value="{{ $result['project_key'] }}" readonly>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width: 30%;">Project Name</th>
                                    <td>
                                        <input type="text" class="form-control-plaintext" value="{{ $result['project_name'] }}" readonly>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width: 30%;">Endpoint</th>
                                    <td>
                                        <input type="text" class="form-control-plaintext" value="{{ $result['endpoint'] }}" readonly>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    @endif
                </div>
                <div class="modal-footer">
                    <a href="https://dash.ripit.id/auth/login" class="btn btn-primary">Login</a>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#approveModal').modal('show');
        });
    </script>
</body>
</html>
