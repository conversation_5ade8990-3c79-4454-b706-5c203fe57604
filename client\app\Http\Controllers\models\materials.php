<?php
namespace App\Http\Controllers\models;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\materials as tblMaterials;
use App\Http\Controllers\config\index as Config;
use App\po_requests as tblPoRequests;
use App\po_items as tblPoItems;

class materials extends Controller
{
    //
    public function sbb($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblMaterials::count(),
            'length'        =>  9
        ]);

        $addnew                 =   new tblMaterials;
        $addnew->id             =   $newid;
        $addnew->token          =   md5($newid);
        $addnew->code           =   trim($request->kode);
        $addnew->type           =   trim($request->type_selected);
        $addnew->categories     =   trim($request->categori_selected);
        $addnew->search         =   trim($request->name) . ";" . trim($request->kode);
        $addnew->name           =   trim($request->name);
        $addnew->units          =   trim($request->unit_selected);
        $addnew->stock          =   $Config->numbers(trim($request->stock));
        $addnew->limitstock     =   $Config->numbers(trim($request->limit_stock));
        $addnew->buffer         =   trim($request->buffer_stock);
        $addnew->safety         =   trim($request->safety_stock);
        $addnew->description    =   trim($request->description);
        $addnew->suplier        =   trim($request->suplier_selected);
        $addnew->user_id        =   trim($request->user_id);
        $addnew->status     =   1;
        $addnew->save();
    }


    //
    public function rpobb($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblPoRequests::count(),
            'length'        =>  9
        ]);

        $countmonth = tblPoRequests::where([
            ['created_at', 'like', '%' . date('Y-m', time()) . '%']
        ])->count();

        $token = md5($newid);

        $addnew                 =   new tblPoRequests;
        $addnew->id             =   $newid;
        $addnew->type           =   trim($request->type);
        $addnew->token          =   $token;
        $addnew->code           =   'RPO/' . date("d/m/Y", time()) . '/' . $Config->numberFZero([$countmonth,3]);
        $addnew->user_id        =   trim($request->user_id);
        $addnew->progress       =   0;
        $addnew->status         =   1;
        $addnew->save();

        $data = [
            "id"        =>  $newid,
            "token"     =>  $token
        ];

        return $data;
    }

    //ITEMS REQUEST PO BB
    public function itemsrpobb($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblPoItems::count(),
            'length'        =>  14
        ]);

        //
        $addnew                 =   new tblPoItems;
        $addnew->id             =   $newid;
        $addnew->product_id     =   $request['product_id'];
        $addnew->req_poid       =   $request['rpoid'];
        $addnew->po_id          =   0;
        $addnew->suplier_id     =   $request['suplier_id'];
        $addnew->price          =   0;
        $addnew->quantity       =   0;
        $addnew->total          =   0;
        $addnew->quantity_fix   =   0;
        $addnew->total_fix      =   0;
        $addnew->progress       =   0;
        $addnew->user_progress  =   0;
        $addnew->date_progress  =   '';
        $addnew->inventory_status   =   0;
        $addnew->inventory_date     =   '';
        $addnew->status             =   1;
        $addnew->save();

    }

    
}