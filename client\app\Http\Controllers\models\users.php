<?php
namespace App\Http\Controllers\models;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\user_registers as tblUserRegisters;
use App\users as tblUsers;
use App\email_senders as tblEmailSenders;
use App\user_configs as tblUserConfigs;
use Illuminate\Support\Facades\Hash;

class users extends Controller
{
    public function create($request)
    {
        $Config = new Config;

        //create new users
        $users = $this->new($request);

        //add user config
        $dataconfig = [
            'user_id'       =>  $users['id'],
            'request'       =>  $request
        ];

        //add user configs
        $adduserconfig = $this->userconfig($dataconfig);

        //add data registers
        $dataregisters = [
            'user_id'       =>  $users['id'],
            'info'          =>  $request['info'],
            'type'          =>  $request['type']
        ];

        // add user registers
        $registers = $this->registers($dataregisters);
    
        
        $datanotif = [
            'user'      =>  [
                'id'            =>  $users['id'],
                'email'         =>  $request['email'],
                'name'          =>  $request['name'],
                'company_id'    =>  $request['company_id'],
                'register_id'   =>  $registers['id'],
                'register_token'=>  $registers['token']
            ],
            'apps'      =>  [
                'root'          =>  $Config->rootapps($request['level'])
            ]
        ];

        // add notif sender or email sender
        $notif = $this->notifsender($datanotif);
    }


    //new users
    public function new($request)
    {
        $Config = new Config;

        //
        $newidusers = tblUsers::count();
        $newidusers++;
        $newidusers = '9' . sprintf('%010s', $newidusers++);

        //
        $users = new tblUsers;
        $users->id          =   $newidusers;
        $users->token       =   $Config->createTokenMD5($newidusers);
        $users->search      =   trim($request['name']) .';'.trim($request['email']).';'.(int)trim($request['phone']);
        $users->name        =   trim($request['name']);
        $users->email       =   trim($request['email']);
        $users->password    =   trim($request['password']) == '' ? '' : Hash::make(trim($request['password']));
        $users->username    =   trim($request['username']);
        $users->company_id  =   trim($request['company_id']);
        $users->level       =   trim($request['level']);
        $users->sub_level   =   trim($request['sub_level']);
        $users->gender      =   trim($request['gender']);
        $users->phone       =   trim((int)$request['phone']);
        $users->phone_code  =   trim($request['phone_code']);
        $users->registers   =   0;
        $users->status      =   1;
        $users->save();

        $data = [
            'id'        =>  $newidusers,
            'email'     =>  $request['email'],
            'name'      =>  $request['name']
        ];

        return $data;

    }


    public function userconfig($request)
    {
        //config
        $Config = new Config;

        //request
        $user_id = $request['user_id'];
        $request = $request['request'];

        //
        $newidconfig = tblUserConfigs::count();
        $newidconfig++;
        $newidconfig = $newidconfig++;

        if( $request['level'] != 0)
        {
            $dataaside = [
                'user_id'   =>  $user_id,
                'level'     =>  $request['level'],
                'sublevel'  =>  $request['sub_level']
            ];


            $aside = new \App\Http\Controllers\config\aside;
            $aside = $aside->createaside($dataaside);
            $aside_menu = json_encode($aside);

        }
        else
        {
            $aside_menu = '';
        }

        //create new id
        $newidconfig = $Config->createnewid([
            'value'         =>  $newidconfig,
            'length'        =>  11
        ]);

        $newaddconfig = new tblUserConfigs;
        $newaddconfig->id       =   $newidconfig;
        $newaddconfig->type     =   $request['level'];
        $newaddconfig->user_id  =   $user_id;
        $newaddconfig->company_id       =   $request['company_id'];
        $newaddconfig->homepage         =   '/dashboard';
        $newaddconfig->aside_id         =   1;
        $newaddconfig->aside_menu       =   $aside_menu;
        $newaddconfig->admin_id         =   $request['admin_id'];
        $newaddconfig->terms            =   0;
        $newaddconfig->terms_date       =   "";
        $newaddconfig->status           =   1;
        $newaddconfig->save();

    }

    //insert registers
    public function registers($request)
    {
        //
        $Config = new Config;

        //
        $upregisters = tblUserRegisters::where([
            'user_id'       =>  $request['user_id']
        ])->update([
            'status'        =>  0
        ]);

        //
        $newidregisters = tblUserRegisters::count();
        $newidregisters++;
        $newidregisters = $newidregisters++;

        //create new code
        $newcode = $Config->createuniqnum([
            'value'         =>  $newidregisters,
            'length'        =>  4
        ]);
        
        //create new id
        $newidregisters = $Config->createnewid([
            'value'         =>  $newidregisters,
            'length'        =>  11
        ]);

        //
        $geoip = $request['info'] === '' ? '' : json_decode($request['info'], true)['geoip'];
        $uagent = $request['info'] === '' ? '' : json_decode($request['info'],true)['uagent'];

        //
        $token = $Config->createTokenMD5($newidregisters);

        //
        $registers = new tblUserRegisters;
        $registers->id              =   $newidregisters;
        $registers->type            =   $request['type'];
        $registers->user_id         =   $request['user_id'];
        $registers->token           =   $token;
        $registers->code            =   $newcode;
        $registers->ip_address      =   $request['info'] === '' ? '' : $geoip['ip'];
        $registers->device          =   $request['info'] === '' ? '' : $uagent['device'];
        $registers->info            =   $request['info'] === '' ? '' : $request['info'];
        $registers->status          =   1;
        $registers->save();

        $data = [
            'id'        =>  $newidregisters,
            'token'     =>  $token
        ];

        return $data;
    }

    public function notifsender($request)
    {
        $Config = new Config;

        //
        $infoautosender = [
            'user'          =>  [
                'id'                =>  $request['user']['id'],
                'email'             =>  $request['user']['email'],
                'name'              =>  $request['user']['name']
            ],
            'apps'          =>  [
                'name'              =>  $Config->apps()['company']['name'], //$Config->apps()[$request['apps']['root']]['name'],
                'url'               =>  $Config->apps()['URL']['APP'],
                'url_help'          =>  $Config->apps()['URL']['HELP'],
                'url_logo'          =>  $Config->apps()['company']['url_logo'],
                'url_link'          =>  $Config->apps()['URL']['APP'] . '/account/verification?token=' . $request['user']['register_token']
            ]
        ];

        //get template with id
        $gettemplate = new \App\Http\Controllers\template\email\index;
        $gettemplate = $gettemplate->main(['id'=>'10001']);
        
        //
        $content = $gettemplate['content'];
        $content = str_replace('{url_home}', $infoautosender['apps']['url'], $content);
        $content = str_replace('{apps_name}', $infoautosender['apps']['name'], $content);
        $content = str_replace('{name}', $request['user']['name'], $content);
        $content = str_replace('{url}', $infoautosender['apps']['url_link'], $content );
        $content = str_replace('{url_help}', $infoautosender['apps']['url_help'], $content);
        $content = str_replace('{url_logo}', $infoautosender['apps']['url_logo'], $content);

        $template = [
            'header'    =>  [
                'title'     =>  $gettemplate['title'],
                'subject'   =>  $gettemplate['subject']
            ],
            'content'   =>  $content
        ];

        // get id email sender in tbl email_senders
        $getsender = tblEmailSenders::where([
            // 'user_id'       =>  $request['user']['company_id'],
            'host'          =>  env('APP_SEND_MAIL'),
            'status'        =>  1
        ])->first();
        
        //
        $dataautosender = [
            'user_id'           =>  $request['user']['id'],
            'type'              =>  1, //1. access
            'sub_type'          =>  1, //1. verif account,
            'sender_type'       =>  1, //1. send by email
            'sender_id'         =>  $getsender->id,
            'template'          =>  $template,
            'infosender'        =>  $infoautosender,
        ];
        
        $addnewautosender = new \App\Http\Controllers\models\autosenders;
        $addnewautosender = $addnewautosender->email($dataautosender);

        return $dataautosender;
    }


    //EMPLOYE ACCOUNT
    public function createEmploye($request)
    {
        $Config = new Config;

        //ADD USER
        $addUser = $this->new($request);

        //ADD REGISTER
        $dataReg = [
            'user_id'       =>  $addUser['id'],
            'info'          =>  '',
            'type'          =>  1
        ];
        $addReg = $this->registers($dataReg);

        //create user config
        $dataConfig = [
            'user_id'       =>  $addUser['id'],
            'request'       =>  $request
        ];

        $this->userconfig($dataConfig);

        //NOTIF EMAIL SENDER
        $dataSender = [
            'user'      =>  [
                'id'        =>  $addUser['id'],
                'name'      =>  $addUser['name'],
                'email'     =>  $addUser['email'],
                'register_id'   =>  $addReg['id']
            ],
            'apps'      =>  [
                'root'          =>  $Config->rootapps('9')
            ]
        ];

        $notif = $this->notifsender($dataSender);

        return $addUser;
    }

}