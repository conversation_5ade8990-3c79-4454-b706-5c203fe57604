<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory;
    protected $table = 'profile'; // Tentukan nama tabel yang sesuai

     protected $fillable = [
        'company_name',
        'nama_lengkap',
        'jenis_kelamin',
        'alamat_lengkap',
        'phone',
        'phone_code',
        'image',
        'provinsi',
        'kota',
        'kecamatan',
        'kode_pos'
        
        // tambahkan kolom lainnya sesuai kebutuhan
    ];

    public function user()
    {
      return $this->belongsTo(User::class);
    }
    
    
}
