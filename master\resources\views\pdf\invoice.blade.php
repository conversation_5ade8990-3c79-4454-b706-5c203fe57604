<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: sans-serif;
            font-size: 14px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            height: 80px;
            margin-bottom: 10px;
        }

        .invoice-box {
            border: 1px solid #eee;
            padding: 20px;
        }

        .details {
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 8px;
            border: 1px solid #ddd;
        }

        th {
            background-color: #f7f7f7;
        }

        .total {
            text-align: right;
            font-weight: bold;
        }
    </style>
</head>
<body>

<div class="header">
    @if ($logo)
        <img src="{{ $logo }}" class="logo" alt="Logo">
    @endif
    <h2>{{ $title }}</h2>
    <p>{{ $date }}</p>
</div>

<div class="invoice-box">
    <div class="details">
        <strong><PERSON><PERSON>an <PERSON>a:</strong><br>
        {{ $customer['name'] }}<br>
        {{ $customer['phone'] }}<br>
        {{ $customer['email'] }}<br>
        {{ $customer['address'] }}
    </div>

    <table>
        <thead>
            <tr>
                <th>Item</th>
                <th>Qty</th>
                <th>Harga</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            @php $grandTotal = 0; @endphp
            @foreach ($items as $item)
                @php
                    $total = $item['qty'] * $item['price'];
                    $grandTotal += $total;
                @endphp
                <tr>
                    <td>{{ $item['name'] }}</td>
                    <td>{{ $item['qty'] }}</td>
                    <td>Rp{{ number_format($item['price'], 0, ',', '.') }}</td>
                    <td>Rp{{ number_format($total, 0, ',', '.') }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3" class="total">Total</td>
                <td>Rp{{ number_format($grandTotal, 0, ',', '.') }}</td>
            </tr>
        </tfoot>
    </table>
</div>

</body>
</html>
