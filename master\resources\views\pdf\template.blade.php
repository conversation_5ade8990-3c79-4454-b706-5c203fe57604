<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        /* Import Google Font */
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

        body {
            font-family: 'Roboto', Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.5;
        }
        .container {
            width: 90%;
            margin: auto;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        .header img {
            width: 200px;
            margin-bottom: 10px;
        }
        .header h1 {
            font-size: 28px;
            margin: 0;
            color: #222;
            font-weight: 700;
        }
        .invoice-details, .items, .summary {
            margin-bottom: 20px;
        }
        h2 {
            font-size: 20px;
            color: #444;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        table th, table td {
            text-align: left;
            padding: 10px;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        table th {
            background-color: #f9f9f9;
            font-weight: 500;
        }
        .summary-table th, .summary-table td {
            text-align: right;
        }
        .summary-table th {
            width: 70%;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #777;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="https://api.ripit.id/storage/RIPITLOGO.jpg" alt="Ripit.ID Logo">
            <h1>Invoice</h1>
            <p style="color: #666; font-size: 14px;">Your trusted partner in SAAS management solutions</p>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <h2>Invoice Details</h2>
            <table>
                <tr>
                    <th>Billed To</th>
                    <th>Invoice Information</th>
                </tr>
                <tr>
                    <td>
                        <strong>{{ $result['billed_to']['name'] }}</strong><br>
                        {{ $result['billed_to']['address'] }}<br>
                        {{ $result['billed_to']['city_country'] }}<br>
                        Phone: {{ $result['billed_to']['phone'] }}
                    </td>
                    <td>
                        Invoice Number: <strong>{{ $result['invoice_details']['invoice_number'] }}</strong><br>
                        Subject: {{ $result['invoice_details']['subject'] }}<br>
                        Invoice Date: {{ $result['invoice_details']['invoice_date'] }}<br>
                        Due Date: {{ $result['invoice_details']['due_date'] }}
                    </td>
                </tr>
            </table>
        </div>

        <!-- Items -->
        <div class="items">
            <h2>Items</h2>
            <p style="font-size: 14px; color: #666;">Below are the details of your selected services:</p>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item</th>
                        <th>Quantity</th>
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($result['items'] as $index => $item)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $item['name'] }}</td>
                        <td>{{ $item['qty'] }}</td>
                        <td>{{ $item['price'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Summary -->
        <div class="summary">
            <h2>Summary</h2>
            <p style="font-size: 14px; color: #666;">Here's the summary of your payment:</p>
            <table class="summary-table">
                <tr>
                    <th>Subtotal</th>
                    <td>{{ $result['summary']['subtotal'] }}</td>
                </tr>
                <tr>
                    <th>PPN 12%</th>
                    <td>{{ $result['summary']['ppn_12'] }}</td>
                </tr>
                <tr>
                    <th>Admin Fee</th>
                    <td>{{ $result['summary']['admin_fee'] }}</td>
                </tr>
                <tr>
                    <th>Total</th>
                    <td><strong>{{ $result['summary']['total'] }}</strong></td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Thank you for choosing <strong>Ripit.ID</strong> – The ultimate solution for all your SAAS management needs.</p>
            <p>We appreciate your trust and look forward to serving you again.</p>
            <p>&copy; 2025 Ripit.ID. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
