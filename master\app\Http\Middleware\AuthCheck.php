<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AuthCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
      
    $token = $request->bearerToken();

    if ($request->header('Accept') === 'application/json' && $token) {
        // Hash the token
        $tokenHash = hash("sha256", $token, true);

        // Query to check the hashed token against the database
        $data = DB::table('user_tokens')->where('token', $tokenHash)->first();

        if ($data) {
            // Token is valid, proceed with the request
            return $next($request);
        } else {
            // Token is not found in the database
            return response()->json(['error' => 'Invalid token'], 401);
        }
    } else {
        // Token is missing or invalid
        return response()->json(['error' => 'Unauthorized'], 401);
    }
   
    }
}
