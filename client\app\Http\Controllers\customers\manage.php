<?php

namespace App\Http\Controllers\customers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\Http\Controllers\access\manage as Refresh;
use App\customers as tblCustomers;
use App\Http\Controllers\account\index as Account;
use Exception;
use Illuminate\Support\Facades\DB;

class manage extends Controller
{
    //
    public function add(Request $request)
    {
        if (trim($request->type) == 'add') {
            $data = $this->new($request);
        } elseif (trim($request->type) == 'edit') {
            $data = $this->sedit($request);
        }
        return $data;
    }

    public function new($request)
    {
        //default config
        $Config = new Config;


        $CekAccount = new Account;
        $account = $CekAccount->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);

        $cekphone = tblCustomers::where([
            'phone'         =>  trim($request->phone),
            'company_id'    =>  $account['config']['company_id'],
            'status'        =>  1
        ])->count();
        // return response()->json($cekphone, 200);

        if ($cekphone > 0) {

            $error = [
                'message'       =>  'No Whatsapp telah terdaftar',
                'focus'         =>  'phone'
            ];
            $status = 500;
        } else {
            $cekemail = tblCustomers::where([
                'email'         =>  trim($request->email),
                'company_id'    =>  $account['config']['company_id'],
                'status'        =>  1
            ])->count();

            if ($request->email != '' && $cekemail > 0) {
                $error = [
                    'message'       =>  'Email telah terdaftar',
                    'focus'         =>  'email'
                ];
                $status = 500;
            } else {
                $status = 200;
            }
        }



        //request
        $field = [
            'name'          =>  trim($request->name),
            'gender'        =>  trim($request->gender),
            'phone'         =>  trim($request->phone),
            'email'         =>  trim($request->email),
            'type'          =>  1,
            'progress'      =>  1,
            'taging'        =>  '',
            'phone_code'    =>  '62',
            'search'         =>  trim($request->name) . ',' . trim($request->phone),
            'source'        =>  trim($request->source),
            'user_id'       =>  $account['id'],
            'company_id'    =>  $account['config']['company_id']
        ];

        try {

            if ($status == 200) {
                $add = new \App\Http\Controllers\models\customers;
                $addcustomers = $add->new($field);
            }
            $data = [
                'success'       =>  $status === 200 ? 'Data berhasil disimpan' : '',
                'data'          =>  $status === 200 ? $addcustomers : '',
                'code'          =>  $status === 200 ? '1' : '0',
                'status'        =>  $status,
                'error'         =>  $status === 200 ? '' : $error
            ];

            return response()->json($data, $status);
        } catch (Exception $e) {
            //
            $data = [
                'message'         =>  'Error: ' . $e
            ];

            return response()->json($data, 500);
        }
    }

    //edit
    public function sedit($request)
    {
        //
        $CekAccount = new Account;
        $account = $CekAccount->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);

        // check jika terdapat no hp yg sama pada akun yg sama
        $cekphone = tblCustomers::where([
            ['id', '<>', trim($request->customer_id)],
            ['company_id', '=', $account['config']['company_id']],
            ['phone',   '=',    trim($request->phone)]
        ])->count();

        if ($cekphone > 0) {
            $data = [
                'success'       =>  '',
                'error'         =>  [
                    'message'        =>  'Nomor Whatsapp sudah terdaftar sebalumnya',
                    'focus'         =>  'phone'
                ]
            ];

            return response()->json($data, 500);
        }

        if (trim($request->email) != '') {
            $cekemail = tblCustomers::where([
                ['id', '<>', trim($request->customer_id)],
                ['company_id', '=', $account['config']['company_id']],
                ['email',   '=',    trim($request->email)]
            ])
                ->count();

            if ($cekemail > 0) {
                $data = [
                    'success'       =>  '',
                    'error'         =>  [
                        'message'        =>  'Alamat email sudah terdaftar sebalumnya',
                        'focus'         =>  'email'
                    ]
                ];

                return response()->json($data, 500);
            }
        }


        //check email

        $up = DB::table('customers')
            ->where([
                'id'        =>  trim($request->customer_id)
            ])
            ->update([
                'name'      =>  trim($request->name),
                'gender'    =>  trim($request->gender),
                'phone'     =>  trim($request->phone),
                'email'     =>  trim($request->email),
                'source'    =>  trim($request->source)
            ]);

        return response()->json([
            'success'       =>  'Data berhasil diperbaharui',
            'status'        =>  '200 OK',
            'code'          =>  '1'
        ], 200);
    }

    public function changeprogress(Request $request)
    {
        $Config = new Config;

        //
        $CekAccount = new Account;
        $account = $CekAccount->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);

        //
        $customer_id = trim($request->customer_id);
        $last = trim($request->lastprogress);
        $new = trim($request->progress);

        //cek
        $lastcek = tblCustomers::from('customers as c')
            ->select(
                'cp.name'
            )
            ->leftJoin('customer_progresses as cp', function ($join) {
                $join->on('cp.id', '=', 'c.progress');
            })
            ->where([
                'c.id'    =>  $customer_id
            ])->first();

        //
        $update = tblCustomers::where([
            'id'        =>  $customer_id
        ])->update([
            'progress'      =>  $new
        ]);

        if ($update) {
            $newcek = tblCustomers::from('customers as c')
                ->select(
                    'cp.name'
                )
                ->leftJoin('customer_progresses as cp', function ($join) {
                    $join->on('cp.id', '=', 'c.progress');
                })
                ->where([
                    'c.id'    =>  $customer_id
                ])->first();

            //update
            $datalog = [
                'customer_id'       =>  $customer_id,
                'user_id'           =>  $account['id'],
                'title'             =>  'Mengubah Progres',
                'body'              =>  'Mengubah progres dari ' . $lastcek->name . ' ke ' . $newcek->name,
                'type'              =>  2,
                'sub_type'          =>  1
            ];

            $addLogs = new \App\Http\Controllers\log\customers\manage;
            $addLogs = $addLogs->update($datalog);

            $message = '';
            $status = 200;
        } else {

            $message = 'Maaf, proses gagal disimpan, silahkan refresh kembali halaman ini.';
            $status = 500;
        }
        //
        $data = [
            'message'       =>  $message
        ];


        return response()->json($data, $status);
    }

    public function changetaging(Request $request)
    {
        $Config = new Config;

        //
        $CekAccount = new Account;
        $account = $CekAccount->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);

        //
        $customer_id = trim($request->customer_id);
        $last = trim($request->lasttaging);
        $new = '[' . trim($request->taging) . ']';

        $update = tblCustomers::where([
            'id'        =>  $customer_id
        ])->update([
            'taging'      =>  $new
        ]);

        if ($update) {

            $datalog = [
                'customer_id'       =>  $customer_id,
                'user_id'           =>  $account['id'],
                'title'             =>  'Mengubah Taging',
                'body'              =>  'Mengubah taging',
                'type'              =>  2,
                'sub_type'          =>  2
            ];

            $addLogs = new \App\Http\Controllers\log\customers\manage;
            $addLogs = $addLogs->update($datalog);

            $message = '';
            $status = 200;
        } else {

            $message = 'Maaf, proses gagal disimpan, silahkan refresh kembali halaman ini.';
            $status = 500;
        }
        //
        $data = [
            'message'       =>  $message,
            // 'refresh'       =>  $Refresh
        ];


        return response()->json($data, $status);
    }

    public function addnote(Request $request)
    {
        $Config = new Config;

        //
        $account = new \App\Http\Controllers\account\index;
        $account = $account->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);


        $customer_id = trim($request->customer_id);
        $note = trim($request->note);


        $datanote = [
            'customer_id'       =>  $customer_id,
            'note'              =>  $note,
            'user_id'           =>  $account['id'],
            'company_id'        =>  $account['config']['company_id']
        ];

        $addnote = new \App\Http\Controllers\models\customers;
        $addnote = $addnote->addnote($datanote);

        if ($addnote == 200) {
            $datalog = [
                'customer_id'       =>  $customer_id,
                'user_id'           =>  $account['id'],
                'title'             =>  'Menambah Catatan',
                'body'              =>  'Menambah catatan',
                'type'              =>  2,
                'sub_type'          =>  3
            ];

            $addLogs = new \App\Http\Controllers\log\customers\manage;
            $addLogs = $addLogs->update($datalog);

            $message = '';
            $status = 200;
        } else {

            $message = 'Maaf, proses gagal disimpan, silahkan refresh kembali halaman ini.';
            $status = 500;
        }

        //
        $data = [
            'message'       =>  $message,
            // 'refresh'       =>  $Refresh
        ];


        return response()->json($data, $status);
    }



    public function detail(Request $request)
    {
        $Config = new Config;

        //
        $token = trim($request->token);

        $getdata = tblCustomers::from('customers as c')
            ->select(
                'c.id',
                'c.name',
                'c.gender',
                'c.phone',
                'c.email',
                'c.taging',
                'c.user_id as admin_id',
                'c.created_at',
                'u.name as admin_name',
                'cp.name as progress_name',
                'cp.color as progress_color'
            )
            ->leftJoin('users as u', function ($join) {
                $join->on('u.id', '=', 'c.user_id');
            })
            ->leftJoin('customer_progresses as cp', function ($join) {
                $join->on('cp.id', '=', 'c.progress');
            })
            ->where([
                'c.token'       =>  $token
            ])
            ->first();

        if ($getdata <> null) {

            $cadmin = explode(' ', $getdata->admin_name);
            $gettag = $getdata->taging === '' ? '' : DB::table('customer_tags')->whereIn('id', json_decode($getdata->taging))->get();


            //get log
            $log = DB::table('customer_logs')
                ->where([
                    'customer_id'       =>  $getdata->id
                ])
                ->orderBy('id', 'asc')
                ->skip(0)
                ->take(5)
                ->get();

            foreach ($log as $row) {
                $field = json_decode($row->text);

                $logs[] = [
                    'title'     =>  $field->title,
                    'body'      =>  $field->body,
                    'date'      =>  $Config->timeago($row->created_at)
                ];
            }



            // get orders
            $getorder = DB::table('orders as o')
                ->select(
                    'o.id',
                    'o.invoice',
                    'o.field',
                    'oc.paid_date'
                )
                ->leftJoin('order_checkouts as oc', function ($join) {
                    $join->on('oc.order_id', '=', 'o.id');
                })
                ->where([
                    'o.customer_id'     =>  $getdata->id,
                    'o.paid'            =>  1,
                    'o.status'          =>  1
                ])
                ->orderBy('id', 'desc')
                ->skip(0)
                ->take(5)
                ->get();

            if (count($getorder) > 0) {
                foreach ($getorder as $row) {
                    $field = json_decode($row->field);

                    $orders[] = [
                        'id'        =>  $row->id,
                        'invoice'   =>  $row->invoice,
                        'product'   =>  $field->product,
                        'date'      =>  $Config->timeago($row->paid_date)
                    ];
                }
            } else {
                $orders = '';
            }

            $response = [
                'id'        =>  $getdata->id,
                'name'      =>  $getdata->name,
                'gender'    =>  $getdata->gender === 1 ? 'male' : 'female',
                'phone'     =>  $getdata->phone,
                'email'     =>  $getdata->email,
                'progress_color'    =>  $getdata->progress_color,
                'progress'  =>  $getdata->progress_name,
                'taging'        =>  $gettag,
                'log'           =>  $logs,
                'orders'        =>  $orders,
                'admin_id'  =>  $getdata->admin_id,
                'admin'     =>  ucwords(strtolower($cadmin[0])),
                'date'   =>  date('d/m/Y', strtotime($getdata->created_at))
            ];

            $data = [
                'message'       =>  '',
                'response'      =>  $response
            ];

            return response()->json($data, 200);
        } else {
            return response()->json([
                'message'       =>  'Data tidak ditemukan'
            ], 200);
        }
    }


    public function vshortedit(Request $request)
    {
        $token = trim($request->token);

        $getdata = tblCustomers::where([
            'token'     =>  $token
        ])
            ->first();

        $data = [
            'message'       =>  '',
            'response'      =>  [
                'id'            =>  $getdata->id,
                'name'          =>  $getdata->name,
                'gender'        =>  $getdata->gender,
                'phone'         =>  $getdata->phone,
                'email'         =>  $getdata->email,
                'source'        =>  $getdata->source
            ]
        ];

        return response()->json($data, 200);
    }
}
