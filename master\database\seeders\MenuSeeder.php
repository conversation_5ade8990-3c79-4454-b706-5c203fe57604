<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
               // Menambahkan menu utama
        DB::table('menus')->insert([
            ['name' => 'Dashboard', 'link' => '/dashboard', 'status' => 1],
            ['name' => 'Orders', 'link' => '/orders', 'status' => 1],
            ['name' => 'Product', 'link' => '/product', 'status' => 1],
            ['name' => 'Report', 'link' => '/report', 'status' => 1],
            ['name' => 'Customers', 'link' => '/customers', 'status' => 1],
            ['name' => 'CS-Broadcast', 'link' => '/cs/whatsapp', 'status' => 1],
            ['name' => 'Customer Service', 'link' => '/cs/whatsapp', 'status' => 1],
            ['name' => 'Business', 'link' => '/business', 'status' => 1],
            ['name' => 'Integration', 'link' => '/integration', 'status' => 1],
            ['name' => 'Gudang', 'link' => '/project/gudang', 'status' => 1],
            ['name' => 'Discount', 'link' => '/discount', 'status' => 1],
            ['name' => 'MasterApi', 'link' => 'https://api.ripit.id/api/auth/project', 'status' => 1],
            ['name' => 'Manage Project', 'link' => 'https://api.ripit.id/api/auth/project/table', 'status' => 1],
            ['name' => 'TIM', 'link' => 'https://api.ripit.id/api/auth/pengguna', 'status' => 1],
        ]);
        
        
        // Sub-menu untuk Dashboard
        DB::table('sub_menus')->insert([
            ['menu_id' => 1, 'name' => 'Get Product', 'link' => '/dashboard/analysis/getProduct', 'status' => 1],
            ['menu_id' => 1, 'name' => 'Get Timeframe', 'link' => '/dashboard/analysis/getTimeframe', 'status' => 1],
            ['menu_id' => 1, 'name' => 'Get Province', 'link' => '/dashboard/analysis/getProvince', 'status' => 1],
            ['menu_id' => 1, 'name' => 'Analysis', 'link' => '/dashboard/analysis?timeframe=&startDate=&endDate=&product=All&province=All', 'status' => 1],
        ]);

        // Sub-menu untuk Orders
        DB::table('sub_menus')->insert([
            ['menu_id' => 2, 'name' => 'Product Table', 'link' => '/product/table?src=&sort=asc&status=1&pg=1&type=0', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Table', 'link' => '/orders/table?pg=&sortdate=&start_date=&end_date=&status=-1', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Create', 'link' => '/orders/create', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Orders Verified', 'link' => '/orders/table?pg=&sortdate=&start_date=&end_date=&status=1', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Orders Unverified', 'link' => '/orders/table?pg=&sortdate=&start_date=&end_date=&status=0', 'status' => 1],
            ['menu_id' => 2, 'name' => 'New Order (Widget)', 'link' => '/orders/widget/new', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Update Price (Widget)', 'link' => '/orders/widget/updateprice', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Update Qty (Widget)', 'link' => '/orders/widget/updateqty', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Add Item (Widget)', 'link' => '/orders/widget/additem', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Checkout', 'link' => '/orders/checkout', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Submit', 'link' => '/orders/submit', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Courier List (Widget)', 'link' => '/courier/list/widget', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Courier Cost', 'link' => '/courier/cost?otype=&origin=&destination=&weight=&courier=&order_id=', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Set Courier Cost', 'link' => '/orders/courier/setcost', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Payment Methods (List New)', 'link' => '/orders/metodepayment/list-new?courier_id=&type=&comp_id=&oid=', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Payment Methods', 'link' => '/orders/metodepayment', 'status' => 1],
            ['menu_id' => 2, 'name' => 'View Detail', 'link' => '/orders/vdetail?id=', 'status' => 1],
            ['menu_id' => 2, 'name' => 'Approve Order', 'link' => '/orders/approve', 'status' => 1],
        ]);
        //product
        DB::table('sub_menus')->insert([
    ['menu_id' => 3, 'name' => 'Create New Product', 'link' => '/product/create?type=new', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Add Product Variant', 'link' => '/product/variant/add?id=', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Get Product Variant', 'link' => '/product/variant/get?id=', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Edit Product', 'link' => '/product/create?type=edit', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Show Product', 'link' => '/product/show', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Delete Product', 'link' => '/product/create?type=delete', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Product Table', 'link' => '/product/table?src=&sort=asc&status=1&pg=1&type=0', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 3, 'name' => 'Get Gudang', 'link' => '/product/gudang', 'status' => 1, 'parent_id' => null],
    
    // Sub-menu untuk Product Bulking
    ['menu_id' => 3, 'name' => 'Product Bulking', 'link' => '/product/bulking', 'status' => 1, 'parent_id' => null],
    
    // Sub-sub-menu untuk Product Bulking
    ['menu_id' => 3, 'name' => 'Import Product', 'link' => '/product/import', 'status' => 1, 'parent_id' => 8],
    ['menu_id' => 3, 'name' => 'Product Mapping', 'link' => '/product/mapping', 'status' => 1, 'parent_id' => 8],
    ['menu_id' => 3, 'name' => 'Preview Product', 'link' => '/product/preview', 'status' => 1, 'parent_id' => 8],
    ['menu_id' => 3, 'name' => 'Store Product', 'link' => '/product/store', 'status' => 1, 'parent_id' => 8],
]);
        // Sub-menu untuk Report
        DB::table('sub_menus')->insert([
    ['menu_id' => 4, 'name' => 'Advertiser Report List', 'link' => '/report/list?platform=', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 4, 'name' => 'Dashboard Product Analysis', 'link' => '/dashboard/analysis/getProduct', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 4, 'name' => 'Advertising Accounts List', 'link' => '/advertising_accounts/list', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 4, 'name' => 'Get Advertising Account ID', 'link' => '/advertising_accounts/getID', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 4, 'name' => 'Create Report', 'link' => '/report/create', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 4, 'name' => 'Update Report', 'link' => '/report/update', 'status' => 1, 'parent_id' => null],
    ['menu_id' => 4, 'name' => 'Delete Report', 'link' => '/report/delete', 'status' => 1, 'parent_id' => null],
    
    // Sub-menu untuk Report Metriks
    ['menu_id' => 4, 'name' => 'Report Metriks', 'link' => '/report/matrix', 'status' => 1, 'parent_id' => null],
    
    // Sub-sub-menu untuk Report Metriks
    ['menu_id' => 4, 'name' => 'Table', 'link' => '/report/matrix/table', 'status' => 1, 'parent_id' => 13],
    ['menu_id' => 4, 'name' => 'Get Platform', 'link' => '/report/matrix/getPlatform', 'status' => 1, 'parent_id' => 13],
    ['menu_id' => 4, 'name' => 'Get Metric', 'link' => '/report/matrix/getMetric', 'status' => 1, 'parent_id' => 13],
    ['menu_id' => 4, 'name' => 'Create', 'link' => '/report/matrix/create', 'status' => 1, 'parent_id' => 13],
    ['menu_id' => 4, 'name' => 'Get ID', 'link' => '/report/matrix/getid', 'status' => 1, 'parent_id' => 13],
    ['menu_id' => 4, 'name' => 'Update', 'link' => '/report/matrix/update', 'status' => 1, 'parent_id' => 13],
    ['menu_id' => 4, 'name' => 'Delete', 'link' => '/report/matrix/delete', 'status' => 1, 'parent_id' => 13],
]);

        // Sub-menu untuk Customers
        DB::table('sub_menus')->insert([
            ['menu_id' => 5, 'name' => 'Export Customers Table', 'link' => '/customers/table/export', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Import Customers Table', 'link' => '/customers/table/import', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Get Segment', 'link' => '/customers/segment', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Filter Segment', 'link' => '/customers/segment-filter?pg=1&sort=desc&segment_name=', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Get Lokasi', 'link' => '/customers/lokasi', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Filter Lokasi', 'link' => '/customers/lokasi-filter?pg=1&sort=desc&kecamatan_name=', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Add Customer', 'link' => '/customers/manage/add?type=add', 'status' => 1],
            ['menu_id' => 5, 'name' => 'View/Edit Customer', 'link' => '/customers/vs-edit', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Edit Customer', 'link' => '/customers/manage/add?type=edit', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Delete Customer', 'link' => '/customers/delete', 'status' => 1],
            ['menu_id' => 5, 'name' => 'Customer Detail', 'link' => '/customers/detail', 'status' => 1],
        ]);
        
        // Sub-menu untuk Customers Bulking
        DB::table('sub_menus')->insert([
            ['menu_id' => 5, 'name' => 'Import', 'link' => '/customers/import', 'status' => 1, 'parent_id' => 11],
            ['menu_id' => 5, 'name' => 'Mapping', 'link' => '/customers/mapping', 'status' => 1, 'parent_id' => 11],
            ['menu_id' => 5, 'name' => 'Preview', 'link' => '/customers/preview', 'status' => 1, 'parent_id' => 11],
            ['menu_id' => 5, 'name' => 'Store', 'link' => '/customers/store', 'status' => 1, 'parent_id' => 11],
        ]);
        
        // Sub-menu untuk Cohort Analysis
        DB::table('sub_menus')->insert([
            ['menu_id' => 5, 'name' => 'Cohort Analysis (Date Range)', 'link' => '/dashboard/cohort-analysis?start=&end=', 'status' => 1, 'parent_id' => 12],
            ['menu_id' => 5, 'name' => 'Cohort Analysis (Product)', 'link' => '/dashboard/cohort-analysis?start=&end=&product=', 'status' => 1, 'parent_id' => 12],
        ]);
        
        // Sub-menu untuk CS-Broadcast
        DB::table('sub_menus')->insert([
            ['menu_id' => 6, 'name' => 'Get Phone', 'link' => '/cs/whatsapp?act=ripit_cs_getPhone', 'status' => 1],
            ['menu_id' => 6, 'name' => 'Get Tags', 'link' => '/cs/whatsapp?act=ripit_cs_getTags', 'status' => 1],
            ['menu_id' => 6, 'name' => 'Get Segment', 'link' => '/cs/whatsapp?act=ripit_cs_getSegment', 'status' => 1],
            ['menu_id' => 6, 'name' => 'Broadcast Add', 'link' => '/cs/whatsapp?filter_type=&filter_name=&act=ripit_bc_add', 'status' => 1],
            ['menu_id' => 6, 'name' => 'Broadcast Get', 'link' => '/cs/whatsapp?act=ripit_bc_get', 'status' => 1],
            ['menu_id' => 6, 'name' => 'Broadcast edit', 'link' => '/cs/whatsapp?act=ripit_bc_edit', 'status' => 1],
            ['menu_id' => 6, 'name' => 'Broadcast update', 'link' => '/cs/whatsapp?act=ripit_bc_update', 'status' => 1],
        ]);

        // Sub-menu untuk Customer Service
        DB::table('sub_menus')->insert([
            ['menu_id' => 6, 'name' => 'CS Setting', 'link' => '/cs/whatsapp?act=ripit_cs_setting', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'Add CS', 'link' => '/cs/whatsapp?act=ripit_cs_add', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'CS Status', 'link' => '/cs/whatsapp?act=ripit_cs_status', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'Delete CS', 'link' => '/cs/whatsapp?act=ripit_cs_delete', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'CS Pairing', 'link' => '/cs/whatsapp?act=ripit_cs_pairing', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'CS Reload', 'link' => '/cs/whatsapp?act=ripit_cs_reload', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'Get CS', 'link' => '/cs/whatsapp?act=ripit_cs_get', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'Get CS Detail', 'link' => '/cs/whatsapp?act=ripit_cs_get_detail', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 6, 'name' => 'Edit CS', 'link' => '/cs/whatsapp?act=ripit_cs_edit', 'status' => 1, 'parent_id' => null],
        ]);
        
        // Sub-menu untuk Business
        DB::table('sub_menus')->insert([
            ['menu_id' => 7, 'name' => 'Edit Business', 'link' => '/business/edit', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 7, 'name' => 'Save Business', 'link' => '/business/save', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 7, 'name' => 'Update VAT', 'link' => '/business/update-vat', 'status' => 1, 'parent_id' => null],
        ]);

        // Sub-menu untuk Integration
        DB::table('sub_menus')->insert([
            ['menu_id' => 8, 'name' => 'Get Public Token', 'link' => '/project/get-public-token', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 8, 'name' => 'Generate Token', 'link' => '/project/generate-token', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 8, 'name' => 'Send Orders', 'link' => '/project/send-orders', 'status' => 1, 'parent_id' => null],
        ]);
        
        // Sub-menu untuk Gudang
        DB::table('sub_menus')->insert([
            ['menu_id' => 9, 'name' => 'Create Gudang', 'link' => '/project/gudang/create', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 9, 'name' => 'Get Gudang ID', 'link' => '/project/gudang/getID', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 9, 'name' => 'Update Gudang', 'link' => '/project/gudang/update', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 9, 'name' => 'Delete Gudang', 'link' => '/project/gudang/delete', 'status' => 1, 'parent_id' => null],
        ]);
        // Sub-menu untuk Discount
        DB::table('sub_menus')->insert([
            ['menu_id' => 10, 'name' => 'Discount List', 'link' => '/discount/list', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 10, 'name' => 'Discount Status', 'link' => '/discount/status', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 10, 'name' => 'Create Discount', 'link' => '/discount/create', 'status' => 1, 'parent_id' => null],
        ]);
        
        // Sub-menu untuk MasterApi
        DB::table('sub_menus')->insert([
            ['menu_id' => 11, 'name' => 'Get Provinsi', 'link' => 'https://api.ripit.id/api/auth/project/getProvinsi', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 11, 'name' => 'Get City', 'link' => 'https://api.ripit.id/api/auth/project/getCity', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 11, 'name' => 'Get Kecamatan', 'link' => 'https://api.ripit.id/api/auth/project/getKecamatan', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 11, 'name' => 'Update Address', 'link' => 'https://api.ripit.id/api/auth/project/updateAddress', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 11, 'name' => 'Create Project', 'link' => 'https://api.ripit.id/api/auth/project', 'status' => 1, 'parent_id' => null],
        ]);
        
                // Sub-menu untuk Manage Project
        DB::table('sub_menus')->insert([
            ['menu_id' => 12, 'name' => 'Get Project by ID', 'link' => 'https://api.ripit.id/api/auth/project/edit?id={id}', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 12, 'name' => 'Update Project', 'link' => 'https://api.ripit.id/api/auth/project/update?id={id}', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 12, 'name' => 'Delete Project', 'link' => 'https://api.ripit.id/api/auth/project/delete?id={id}', 'status' => 1, 'parent_id' => null],
        ]);
        
        // Sub-menu untuk TIM
        DB::table('sub_menus')->insert([
            ['menu_id' => 13, 'name' => 'Get Role', 'link' => 'https://api.ripit.id/api/auth/pengguna/get-role', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 13, 'name' => 'Create Role', 'link' => 'https://api.ripit.id/api/auth/pengguna/create-role', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 13, 'name' => 'Pengguna Page 1', 'link' => 'https://api.ripit.id/api/auth/pengguna?page=1&src=', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 13, 'name' => 'Edit Pengguna ID', 'link' => 'https://api.ripit.id/api/auth/pengguna/{id}/edit', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 13, 'name' => 'Update Pengguna', 'link' => 'https://api.ripit.id/api/auth/pengguna/{id}', 'status' => 1, 'parent_id' => null],
            ['menu_id' => 13, 'name' => 'Delete Pengguna', 'link' => 'https://api.ripit.id/api/auth/pengguna/delete/{id}', 'status' => 1, 'parent_id' => null],
        ]);
 


    }
}
