<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
//use http
use Illuminate\Support\Facades\Http;

class ApiUserController extends Controller
{
    public function getConfig(Request $request, $path, $action = null)
    {
        // Ambil data dari request
        $data_raw = $request->all();
        $header = $request->header('key');
        $content_type = $request->header('Content-Type');
        // Konfigurasi
        app()->configure('pian.ripit.gass.co.id');
        // // Mendapatkan konfigurasi
        $config = Config::get('pian.ripit.gass.co.id');
        // $url_test = 'product/list?type=1';
        //lakukan get data pakai header dan content type
        $response = Http::withHeaders([
            'key' => $header,
            'Content-Type' => $content_type
        ])->get($config . '/' . $action);
        return response()->json($response->json());


        // try {
        // } catch (\Exception $e) {
        //     return response()->json(['error' => 'Gagal terhubung ke database: ' . $e->getMessage()]);
        // }
    }
    public function index($name)
    {
        //subdomain
        $subdomain = $name;
        // Load konfigurasi dari file /app/pian.php
        $config = include(base_path('app/config.php'));
        // Mengakses konfigurasi sitename
        $sitename = isset($config['sitename']) ? $config['sitename'] : null;
        // dd($sitename);
        $server = $subdomain . '.' . $sitename;
        $message = "Config for " . $subdomain . " is " . $server;
        return response()->json(['message' => $message]);
    }
}
