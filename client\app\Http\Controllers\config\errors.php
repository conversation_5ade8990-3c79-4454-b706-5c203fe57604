<?php
namespace App\Http\Controllers\config;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class errors extends Controller
{
    //
    public function main($request){

        $message = null;
        if(isset($request['message'])){
            $message = $request['message'];
        }

        //
        $code = [
            401     =>  '',
            404     =>  'Your find not found',
            500     =>  $message !== null ? $message : 'Bad serve 500, please contact your Administrator'
        ];

        $data = [
            'message'       =>  $code[$request['code']],
            'code'          =>  (int)$request['code']
        ];


        if( isset($request['items']) ){
            $data = [
                'items'     =>  ''
            ];
        }


        return $data;

    }
}