<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\RegisterController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\ProjectController;
use App\Http\Controllers\Api\PenggunaController;
use App\Http\Controllers\Api\BroadcastController;
use App\Http\Controllers\Api\RabbitTestController;

use Illuminate\Support\Facades\Cache;
use App\Models\User;
use App\Http\Middleware\AuthCheck;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\PdfController;






Route::prefix('auth')->middleware([AuthCheck::class])->group(function () {
    Route::get('/checktoken', function () {
        return response()->json(['message' => 'Token is valid']);
    });
    //profile
    Route::resource('profile', ProfileController::class);

    Route::post('profile/get', [ProfileController::class, 'getProfile']);
    Route::post('profile/update', [ProfileController::class, 'updateProfile']);
    Route::post('profile/update-password', [ProfileController::class, 'updatePassword']);

//get plan
    Route::post('profile/plan', [ProfileController::class, 'getPlan']);
    
    //project
    Route::resource('project', ProjectController::class);
    Route::post('project/table', [ProjectController::class, 'table']);
    Route::post('project/edit', [ProjectController::class, 'edit']);
    Route::post('project/update', [ProjectController::class, 'update']);
    Route::post('project/delete', [ProjectController::class, 'delete']);
    Route::post('project/get-business', [ProjectController::class, 'getBusiness']);
    
    Route::post('project/getProvinsi', [ProjectController::class, 'getProvinsi']);
    Route::post('project/getCity', [ProjectController::class, 'getCity']);
    Route::post('project/getKecamatan', [ProjectController::class, 'getKecamatan']);
    // updateAddress
    Route::post('project/updateAddress', [ProjectController::class, 'updateAddress']);
    
    Route::post('project/invite', [ProjectController::class, 'invite']);


    //pengguna
    Route::resource('pengguna', PenggunaController::class);
    // Update pengguna menggunakan metode POST
    Route::post('/pengguna/{id}', [PenggunaController::class, 'update']);
    Route::post('/pengguna/delete/{id}', [PenggunaController::class, 'destroy']);

    Route::post('pengguna/get-role', [PenggunaController::class, 'getRole']);
    //penggunabyno_hp
    Route::post('pengguna/get-nohp', [PenggunaController::class, 'getNohp']);
    //penggunabyemail
    Route::post('pengguna/get-email', [PenggunaController::class, 'getEmail']);
    //test upload image
    Route::post('pengguna/upload-imageTest', [PenggunaController::class, 'uploadImage']);
    //get menu
    Route::post('pengguna/getMenus/{id}', [PenggunaController::class, 'getUserMenu']);
    Route::post('/pengguna/addMenus/{id}', [PenggunaController::class, 'updateUserMenuSubMenu']);

    //billing 
    Route::get('billing-checkstatus', [ProjectController::class, 'billingStatus']);
    Route::get('billing', [ProjectController::class, 'billing']);
    Route::get('billing/getSubscription-plan', [ProjectController::class, 'billingGetPlan']);
    Route::post('billing/upgrade-plan', [ProjectController::class, 'pilihPlan']);
    Route::post('billing/order-plan', [ProjectController::class, 'billingOrderPlan']);

    Route::post('project/update-usage-product', [ProjectController::class, 'productUsage']);    
    Route::post('project/update-usage-tambah-product', [ProjectController::class, 'productUsageTambah']);     
    Route::post('billing/upgrade-plan/download', [ProjectController::class, 'generateInvoicePDF']);
    Route::post('billing/download', [ProjectController::class, 'DownloadBillingOrderPlan']);
    
    Route::post('logout', [ProfileController::class, 'logout']);
});

//project approve
Route::get('project/approve', [ProjectController::class, 'approve']);
Route::get('project/kirim', [ProjectController::class, 'invite']);
Route::get('project/template', [ProjectController::class, 'template']);
Route::get('project/template-order', [ProjectController::class, 'template_order']);
Route::get('project/test-email', [ProjectController::class, 'testEmail']);
Route::get('project/get-by-name', [ProjectController::class, 'getbyname']);
Route::get('project/get-template-verification-code', [RegisterController::class, 'getTemplateVerif']);
Route::get('/template-invoice', [ProjectController::class, 'templateInvoice']);


//cek dns
Route::get('project/dns', [ProjectController::class, 'get_dns']);
Route::get('package-list', [ProjectController::class, 'get_package']);
Route::post('package-order', [ProjectController::class, 'order_package']);
Route::post('use-voucher', [ProjectController::class, 'order_package_voucher']);
Route::get('order-redirect', [ProjectController::class, 'order_redirect']);
Route::post('webhookPayOrder', [ProjectController::class, 'order_callback']);
Route::get('set-expired', [ProjectController::class, 'setExpired']);



Route::get('/menu', [ProjectController::class, 'getMenus']);
Route::get('/generate-menu', [ProjectController::class, 'generateUserMenuAccess']);
Route::get('/test-generate-menu', [PenggunaController::class, 'testGenerate']);

Route::controller(RegisterController::class)->group(function(){
    Route::post('register', 'register');
    Route::post('resend-verification-code', 'resendVerificationCode');
    Route::post('login', 'login2');
     //verfication-code
    Route::post('forgot-password', 'forgotPassword');
    Route::post('verfication-code', 'verficationCode');
    Route::post('submit-password', 'submitPassword');
    Route::post('get-template-verification-code', 'getTemplate');
    
});
   
 
Route::post('project/remote', [ProjectController::class, 'remote']);
Route::post('project/get-phone-allproject', [ProjectController::class, 'getAllPhone']);
Route::post('project/create-phone-allproject', [ProjectController::class, 'addPhone']);
Route::post('project/delete-phone-allproject', [ProjectController::class, 'deletePhone']);
//synch team
Route::get('project/sync-team', [PenggunaController::class, 'syncTeam']);

Route::post('/send-broadcast', [BroadcastController::class, 'sendBroadcast']);
Route::get('/cron-broadcast', [BroadcastController::class, 'cronBroadcast']);
Route::get('/get-broadcast-result', [BroadcastController::class, 'getBroadcastResult']);

   
Route::get('/generate-menu-owners/{id}', [ProjectController::class, 'generateMenuOwner']);
Route::get('/test', function () {
    return response()->json(['message' => 'test']);
});

Route::get('cache-data', function () {
    // Menyimpan dan mengambil pengguna pertama dari cache
    $user = Cache::remember('user', 60, function () {
        return User::all();
    });

    // Memeriksa apakah data pengguna null
    if (is_null($user)) {
        return response()->json([
            'success' => false,
            'message' => 'Pengguna tidak ditemukan.'
        ], 404);
    }

    // Mengembalikan data pengguna sebagai respons JSON
    return response()->json([
        'success' => true,
        'data' => $user
    ]);
});



Route::get('/invoice', [PdfController::class, 'invoice']);

// Route::get('/user', function (Request $request) {
//     return $request->user();
// })->middleware('auth:sanctum');
