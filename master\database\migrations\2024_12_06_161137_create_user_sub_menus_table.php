<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
    Schema::create('user_sub_menus', function (Blueprint $table) {
        $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Foreign key ke users
        $table->foreignId('sub_menu_id')->constrained('sub_menus')->onDelete('cascade'); // Foreign key ke sub_menus
        $table->tinyInteger('status')->default(1); // 1 = akses aktif, 0 = akses non-aktif
        $table->primary(['user_id', 'sub_menu_id']);
    });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_sub_menus');
    }
};
