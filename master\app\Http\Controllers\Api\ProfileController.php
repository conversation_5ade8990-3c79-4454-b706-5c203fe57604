<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\BaseController as BaseController;
use Illuminate\Http\Request;
use App\Models\Profile;
use App\Http\Resources\ProfileResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Response; // Make sure this import is correct
use App\Models\User;
use Illuminate\Support\Facades\View; // Tambahkan ini
use Aws\S3\S3Client;
use Illuminate\Support\Facades\Storage;
use Aws\S3\Exception\S3Exception;
use App\Services\WaService;
use App\Services\ActivityService;

class ProfileController extends BaseController
{
   
    /**
     * Display a listing of the resource.
     */
public function index(Request $request): JsonResponse
{
    // 1. Ambil user dari token
    $tokenHash = hash("sha256", $request->bearerToken(), true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    if (!$userToken) {
        return response()->json([
            'message' => "Token tidak valid",
            'code' => 0,
            'response' => []
        ]);
    }

    $userId = $userToken->user_id;

    // 2. Ambil user dengan profil
    $usersWithProfile = User::with('profile')->where('id', $userId)->get();

    // 3. Ambil orders + paket
    $orders = DB::table('orders')
        ->join('paket_package', 'orders.paket_id', '=', 'paket_package.id')
        ->select(
            'orders.order_id',
            'orders.transaction_status',
            'orders.nominal',
            'orders.created_at',
            'orders.tanggal_approve',
            'orders.status_paket',
            'orders.tanggal_expired',
            'paket_package.name as package',
            'orders.paket_type as package_type',
            'orders.nominal as nominal'
        )
        ->where('orders.user_id', $userId)
        ->orderByDesc('orders.id')
        ->paginate(50);


    // 4. Tambahkan periode dan status pada setiap order
    foreach ($orders as $order) {
        $order->periode = $order->tanggal_approve
            ? $order->tanggal_approve . ' to ' . $order->tanggal_expired
            : "";
        $order->status = $order->status_paket == 1 ? 'Active' : 'Inactive';
    }

    // 5. Tambahkan informasi tambahan ke user
    foreach ($usersWithProfile as $user) {
        $user->user_key = bin2hex($user->user_key);

        // Ambil order aktif
        $activeOrder = $orders->first(function ($order) {
            return $order->status === 'Active';
        });

        $user->periode = $activeOrder ? $activeOrder->periode : "";
        $user->paket_name = $activeOrder ? $activeOrder->package : "";
        $user->paket_type = $activeOrder ? $activeOrder->package_type : "";
        $user->nominal = $activeOrder ? $activeOrder->nominal : "";
    }

    // 6. Return response
    return $this->sendResponse($usersWithProfile, 'Profiles retrieved successfully.');
}


public function getPlan(Request $request): JsonResponse
{
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);

    // Find user based on the token
    $data = DB::table('user_tokens')->where('token', $tokenHash)->first()->user_id;

    // Fetch user with associated plan
    $user = User::with('plan')->where('id', $data)->first();

    // Decode the plan detail if it exists
    if ($user && $user->plan && $user->plan->detail) {
        $user->plan->detail = json_decode($user->plan->detail, true);  // Decoding the JSON string
    }

    // Return only the plan details in the response
    return response()->json([
            'plan' => $user->plan['detail'] ?? null ,
            'plan_usage' => json_decode($user['usage_paket'], true)
    ]);
}


 public function getProfile(Request $request): JsonResponse
    {
        $token = $request->bearerToken();
        $tokenHash = hash("sha256", $token, true);
        $data = DB::table('user_tokens')->where('token', $tokenHash)->first()->user_id;
        $usersWithProfile = User::with('profile')->where('id', $data)->get();
        foreach ($usersWithProfile as $user) {
        $user->user_key = bin2hex($user->user_key);
        }
        if($usersWithProfile != null){
            return response()->json([
            'message'   => "Data profile berhasil diambil",
            'code'      => 1,
            'result'    => [
                'id' => $usersWithProfile[0]['id'],
                'first_name' => $usersWithProfile[0]['name'],
                'last_name' => $usersWithProfile[0]['profile']['nama_lengkap'],
                'jenis_kelamin' => $usersWithProfile[0]['profile']['jenis_kelamin'],
                'image' => $usersWithProfile[0]['profile']['image'],
                'email' => $usersWithProfile[0]['email'],
                'phone' => $usersWithProfile[0]['profile']['phone'],
                'address' => $usersWithProfile[0]['profile']['alamat_lengkap'],
                'provinsi' => $usersWithProfile[0]['profile']['provinsi'],
                'kota' => $usersWithProfile[0]['profile']['kota'],
                'kecamatan' => $usersWithProfile[0]['profile']['kecamatan'],
                'kode_pos' => $usersWithProfile[0]['profile']['kode_pos'],
                ]
                ]);
        }else{
            return response()->json([
            'message' => 'Data profile gagal didapatkan',
            'code' => 0,
            'result' => null
                ]);
            
        }
        // return $this->sendResponse($usersWithProfile, 'Profiles retrieved successfully.');
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            // 'company_name' => 'required',
            'nama_lengkap' => 'required',
            'jenis_kelamin' => 'required',
            'alamat_lengkap' => 'required'
        ]);
        
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());       
        }
        $profile = Profile::create($input);
        return $this->sendResponse(new ProfileResource($profile), 'Profile created successfully.');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    /**
     * Display the specified resource.
     */
    public function show(Profile $profile)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Profile $profile)
    {
        //
    }

public function updateProfile(Request $request): JsonResponse
{
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);
    $user_id = DB::table('user_tokens')->where('token', $tokenHash)->first()->user_id;
    $id = $user_id;
    // Fetch the profile based on the user_id
    $profile = Profile::where('user_id', $id)->first();
    // If profile is not found, return an error message
    if (!$profile) {
        return response()->json([
            'code' => 0,
            'message' => 'Profile not found.',
            'result' => null,
        ], 404);
    }

    // Fetch the user model for updating the email
    $user = User::find($id);
    if (!$user) {
        return response()->json([
            'code' => 0,
            'message' => 'User not found.',
            'result' => null,
        ], 404);
    }

    // Validate the request data
    $validator = Validator::make($request->all(), [
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:20',
        'jenis_kelamin' => 'required', // Assuming M or F
        'address' => 'required|string|max:255',
        'kode_pos' => 'required|digits:5',
        'address_array' => 'required|array',
        'address_array.*' => 'integer', // Optional: Validates each item in the array
    ]);

    // If validation fails, return an error response
if ($validator->fails()) {
    // Get the first error message for each field and combine them
    $errorMessages = [];
    foreach ($validator->errors()->messages() as $field => $messages) {
        $errorMessages[] = ucfirst($field) . ': ' . $messages[0];
    }
    
    // Join the messages into a single string for the 'message' field
    $combinedMessage = 'Validation Error. ' . implode(' ', $errorMessages);
    
    return response()->json([
        'code' => 0,
        'message' => $combinedMessage,
        'result' => $validator->errors(),
    ], 422); // Unprocessable Entity
}


  if ($request->hasFile('image')) {
        $image = $request->file('image');
        $imageName = time() . '_' . $image->getClientOriginalName();  // Buat nama file unik
        $bucket = "gss";  // Pastikan nama bucket benar
        $filePath = $imageName;

        try {
            // Upload file ke S3
            $result = $this->s3->putObject([
                'Bucket' => $bucket,
                'Key' => $filePath,
                'Body' => fopen($image->getPathname(), 'r'),
                'ContentType' => $image->getMimeType(),  // Dinamis berdasarkan tipe file
                'ACL' => 'public-read',  // Akses publik
            ]);

            // URL dari file yang diupload
            $imageUrl = $result['ObjectURL'];

            // Simpan URL gambar baru di profil
            $profile->image = $imageUrl;

        } catch (S3Exception $e) {
            // Tangani error
            return response()->json(['error' => 'Failed to upload image to S3: ' . $e->getMessage()], 500);
        }
    }


    // Update the User model's email if provided
    $user->name = $request->input('first_name');
    $user->email = $request->input('email');
    $userUpdate = $user->save(); // Save the changes to the user and check if successful

    // Update the Profile model
    $profile->nama_lengkap   = $request->input('last_name');
    $profile->phone  = $request->input('phone');
    $profile->jenis_kelamin  = $request->input('jenis_kelamin');
    $profile->alamat_lengkap = $request->input('address');
    $profile->provinsi = $request->input('address_array.0');
    $profile->kota = $request->input('address_array.1');
    $profile->kecamatan = $request->input('address_array.2');
    $profile->kode_pos       = $request->input('kode_pos');
    // $profile->address_array = json_encode($request->input('address_array'));
    $profileUpdate = $profile->save(); // Save the changes to the profile and check if successful
    // Check if both user and profile updates were successful
    if ($userUpdate && $profileUpdate) {
        $usersWithProfile = User::with('profile')->where('id', $id)->get();
        foreach ($usersWithProfile as $user) {
        $user->user_key = bin2hex($user->user_key);
        }
        return response()->json([
            'code' => 1,
            'message' => 'Profile and user updated successfully.',
            'result' => [
                'id' => $usersWithProfile[0]['id'],
                'first_name' => $usersWithProfile[0]['name'],
                'last_name' => $usersWithProfile[0]['profile']['nama_lengkap'],
                'jenis_kelamin' => $usersWithProfile[0]['profile']['jenis_kelamin'],
                'image' => $usersWithProfile[0]['profile']['image'],
                'email' => $usersWithProfile[0]['email'],
                'phone' => $usersWithProfile[0]['profile']['phone'],
                'address' => $usersWithProfile[0]['profile']['alamat_lengkap'],
                'provinsi' => $usersWithProfile[0]['profile']['provinsi'],
                'kota' => $usersWithProfile[0]['profile']['kota'],
                'kecamatan' => $usersWithProfile[0]['profile']['kecamatan'],
                'kode_pos' => $usersWithProfile[0]['profile']['kode_pos'],
            ],
        ], 200);
    } else {
        return response()->json([
            'code' => 0,
            'message' => 'Failed to update profile or user.',
            'result' => null,
        ], 500); // Internal Server Error
    }
}



public function updatePassword(Request $request): JsonResponse
{
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);
    $user_id = DB::table('user_tokens')->where('token', $tokenHash)->first()->user_id;
    $id = $user_id;
        // Validate the request data
    $validator = Validator::make($request->all(), [
        'current_password' => 'required|string|max:255',
        'new_password' => 'required|string|max:255',
    ]);

    // If validation fails, return an error response
    if ($validator->fails()) {
        return response()->json([
            'code' => 0,
            'message' => 'Validation Error.',
            'result' => $validator->errors(),
        ], 422); // Unprocessable Entity
    }
    
    $user = User::find($id);
    $appKey = config('app.key');
    $pass1 = hash("sha256", $appKey . ";" . $user['email'] . ";" . $request->input("current_password"));
    
    // Query the database
    $user = DB::table("users")
        ->whereRaw("password = UNHEX(?)", [$pass1])
        ->first();

    if($user){
        $user_update = User::find($id);
             // Hash password baru menggunakan flow yang sama
        $newPass = hex2bin(hash('sha256', $appKey .";".  $user_update['email'] .";".$request->input("new_password")));
        // Update password baru
        $user_update->password = $newPass;
        $user_update->save(); // Simpan perubahan ke database
        return response()->json([
            'message' => 'Password berhasil diperbarui',
            'code'    => 1,
        ]);

    }else{
    return response()->json([
        'message' => 'Password Lama Salah',
        'code'    => 0,
        ]);
    }
    
}
    protected $s3;
    protected $activityService;
    protected $waService;

    public function __construct(WaService $waService, ActivityService $activityService)
    {
        $this->waService = $waService;
        $this->activityService = $activityService;
        
        $this->s3 = new S3Client([
            'version' => 'latest',
            'region' => 'us-east-1', // Sesuaikan dengan region Anda
            'endpoint' => 'https://objects-us-east-1.dream.io',
            'credentials' => [
                'key' => 'DHTHCUQ7LA7HVHCFP3GC',
                'secret' => '1BUJHjr__UCwThLOfSLVjagnPod9J3E-dTL9WNee',
            ],
        ]);
    }


public function update(Request $request, $id): JsonResponse
{


        // Temukan profil berdasarkan ID yang diberikan
    $profile = Profile::find($id);

    // Jika profil tidak ditemukan, kembalikan pesan kesalahan dengan code 0
    if (!$profile) {
        return response()->json([
            'code' => 0,
            'message' => 'Profile not found.',
            'result' => null,
        ], 404);
    }

    // Validasi data yang diterima
    $validator = Validator::make($request->all(), [
        'first_name' => 'required|string|max:255',
        'last_name' => 'required',
        'email' => 'required',
        'phone' => 'required',
        'jenis_kelamin' => 'required',
    ]);


    if ($validator->fails()) {
        return response()->json([
            'code' => 0,
            'message' => 'Validation Error.',
            'result' => $validator->errors(),
        ], 422); // Unprocessable Entity
    }

    // Perbarui profil sesuai dengan data yang diterima
    // $profile->nama_lengkap = $request->input('nama_lengkap');
    // $profile->jenis_kelamin = $request->input('jenis_kelamin');
    // $profile->alamat_lengkap = $request->input('alamat_lengkap');
    // // Simpan perubahan pada profil
    // $profile->save();

    // // Berikan respons bahwa profil telah diperbarui dengan code 1
    // return response()->json([
    //     'code' => 1,
    //     'message' => 'Profile updated successfully.',
    //     'result' => $profile,
    // ], 200); // OK
}


    /**
     * Update the specified resource in storage.
     */
    // public function update(Request $request, Profile $profile)
    // {
    //     //
    // }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Profile $profile)
    {
        //
    }
    
    
    public function logout(Request $request): JsonResponse
    {
        // Retrieve the token from the Authorization header
    $token = $request->bearerToken();
    //cehck table uer_token
     $tokenHash = hash("sha256", $token, true);

   $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    // Check if data is found
    if (!$userToken) {
        return Response::json(['error' => 'Token not found'], 404);
    }
    
   try {
       
       
       
     $user = User::where('id', $userToken->user_id)->first();
            $userProjects = DB::table('user_projects')
            ->join('projects', 'user_projects.project_id', '=', 'projects.id')
            ->where('user_projects.user_id', $user->id)
            ->select('projects.project_name as project_name')
            ->get();

           $userRole = $user->role_id == 1 ? 'owner' : 'member';
            if ($userProjects->isNotEmpty()) {
                    // return response()->json($userProjects,500);
                foreach ($userProjects as $proj) {
    
                    $sendActivity = [
                        'users_nama' => $user->name,
                        'users_email' => $user->email,
                        'users_activity' => "Logout",
                        'users_role' => $userRole, 
                        'users_project_name' => $proj->project_name
                    ];
                    
                    $result = $this->activityService->sendActivity($sendActivity);
                }
            } else {
                // Jika tidak ada project, kirim aktivitas tanpa project_name
                $sendActivity = [
                    'users_nama' => $user->name,
                    'users_email' => $user->email,
                     'users_role' => $userRole, 
                    'users_activity' => "Logout"
                ];
                $result = $this->activityService->sendActivity($sendActivity);
            }
            
    
    $deletedRows = DB::table('user_tokens')->where('token', $tokenHash)->delete();

    // Check if the token data was successfully deleted
    if ($deletedRows > 0) {
        return Response::json([
            'message' => 'Logout successful',
            'code' => 1
            ], 200);
    } else {
        // Log the failure to delete token
        Log::error('Failed to delete token: No rows were deleted from the database');
        return Response::json(['error' => 'Failed to delete token'], 500);
    }
} catch (\Exception $e) {
    // Log the exception
    Log::error('Failed to delete token: ' . $e->getMessage());
    return Response::json(['error' => 'Failed to delete token'], 500);
}

    //lakukan delete lalu data berhasil logout
    
    // Prepare the response data
    // $responseData = [
        // 'token' => bin2hex(substr($data->token, 0, 34)) // Display the first 34 bytes of the token in hexadecimal
    // ];
    


    
}}
