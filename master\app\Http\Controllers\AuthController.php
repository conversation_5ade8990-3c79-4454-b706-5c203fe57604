<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;



use Exception;
use <PERSON>lad<PERSON>ahi<PERSON>\Jwt\Generator;
use <PERSON>ladRahimi\Jwt\Parser;
use MiladRahimi\Jwt\Cryptography\Algorithms\Hmac\HS256;


class AuthController extends Controller
{
   
   
   
   public function register(Request $request)
{
    // Validasi input
    $validator = Validator::make($request->all(), [
        'name' => 'required|string|max:255',
        'email' => 'required|string|email|max:255|unique:users',
        'password' => 'required|string|min:6|confirmed',
    ]);

    // Jika validasi gagal, kembalikan respons error
    if ($validator->fails()) {
        return response()->json(['success' => false, 'message' => $validator->errors()], 422);
    }

    // Buat pengguna baru
    $user = User::create([
        'name' => $request->name,
        'email' => $request->email,
        'password' => Hash::make($request->password),
    ]);

    // Kembalikan respons data pengguna yang berhasil dibuat
    return response()->json([
        'success' => true,
        'user' => $user
    ], 201);
}



public function login(Request $request)
{
    // Validasi input
    $validator = Validator::make($request->all(), [
        'email' => 'required|string|email|max:255',
        'password' => 'required|string|min:6',
    ]);

    // Jika validasi gagal, kembalikan respons error
    if ($validator->fails()) {
        return response()->json(['success' => false, 'message' => $validator->errors()], 422);
    }

    // Lakukan autentikasi pengguna secara manual
    $user = User::where('email', $request->email)->first();

    // Periksa apakah pengguna ditemukan dan password cocok
    if (!$user || !Hash::check($request->password, $user->password)) {
        return response()->json(['success' => false, 'message' => 'Invalid credentials.'], 401);
    }

    // Buat token secara acak
    $token = Str::random(60);

    // Simpan token di cache dengan key sebagai user_id untuk 60 menit
    $cacheSuccess = Cache::put('user_token_' . $user->id, $token, 60);

    // Periksa apakah penyimpanan token ke cache berhasil
    if (!$cacheSuccess) {
        return response()->json(['success' => false, 'message' => 'Failed to store token in cache.'], 500);
    }

    // Dapatkan data cache yang sesuai dengan pengguna yang diautentikasi
    $cachedToken = Cache::get('user_token_' . $user->id);

    // Kembalikan respons sukses bersama dengan token, data pengguna, dan data cache
    return response()->json([
        'success' => true,
        'message' => 'User logged in successfully.',
        'token' => $token,
        'user' => $user,
        'cache' => $cachedToken,
    ], 200);
}






    // Metode untuk menyandikan token
    function encryptToken($key, $data)
    {
        // Serialize the data array
        $serializedData = serialize($data);

        // Generate a random IV (Initialization Vector)
        $iv = openssl_random_pseudo_bytes(
            openssl_cipher_iv_length("aes-256-cbc")
        );

        // Encrypt the serialized data using AES-256-CBC encryption
        $encryptedData = openssl_encrypt(
            $serializedData,
            "aes-256-cbc",
            $key,
            0,
            $iv
        );

        // Base64 encode the encrypted data and IV
        $encodedData = base64_encode($encryptedData . "::" . $iv);

        return $encodedData;
    }

    // Metode untuk mendekripsi token
    function decryptToken($key, $encodedData)
    {
        // Base64 decode the encoded data
        $decodedData = base64_decode($encodedData);

        // Extract the IV (Initialization Vector) from the decoded data
        [$encryptedData, $iv] = explode("::", $decodedData, 2);

        // Decrypt the encrypted data using AES-256-CBC decryption
        $decryptedData = openssl_decrypt(
            $encryptedData,
            "aes-256-cbc",
            $key,
            0,
            $iv
        );

        // Unserialize the decrypted data
        $data = unserialize($decryptedData);

        return $data;
    }
}
