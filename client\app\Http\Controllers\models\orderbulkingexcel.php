<?php
namespace App\Http\Controllers\models;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\order_bulking_excels as tblOrderBulkingExcels;
use App\obe_upload_excels as tblObeUploadExcels;
use App\obe_upload_bbs as tblObeUploadBbs;
use App\Http\Controllers\config\index as Config;
use App\obe_items as tblObeItems;

class orderbulkingexcel extends Controller
{
    //
    public function main($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblOrderBulkingExcels::count(),
            'length'        =>  15
        ]);

        $countmonth = tblOrderBulkingExcels::where([
            ['created_at', 'like', '%' . date('Y-m', time()) . '%']
        ])->count();

        $token = md5($newid);
        $invoice = $Config->numberFZero([$countmonth,3]) . '/' . date('d/m/Y', time()) . '/D-INV';

        $addnew                 =   new tblOrderBulkingExcels;
        $addnew->id             =   $newid;
        $addnew->token          =   $token;
        $addnew->invoice        =   $invoice;
        $addnew->field          =   '';
        $addnew->items          =   '';
        $addnew->distributor_id =   trim($request->distributor_selected);
        $addnew->user_id        =   trim($request->user_id);
        $addnew->order_status   =   0;
        $addnew->subtotal       =   0;
        $addnew->ongkir         =   0;
        $addnew->discount       =   0;
        $addnew->total          =   0;
        $addnew->debt           =   0;
        $addnew->payment_type   =   0;
        $addnew->payment        =   0;
        $addnew->payment_user   =   0;
        $addnew->payment_date   =   '';
        $addnew->due_date       =   '';
        $addnew->status         =   1;
        $addnew->save();

        return ['id'=>$newid, 'invoice'=>$invoice];
    }


    //upload excel
    public function uploadexcel($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblObeUploadExcels::count(),
            'length'        =>  15
        ]);

        $token = md5($newid);
        $file = $request->file('file_excel');
        $namefile = $token . '.' . $file->getClientOriginalExtension();
        
        $base = $Config->path()['s3'];
        $path = '/upload/file/excel/bulking/';

        

        $addnew                 =   new tblObeUploadExcels;
        $addnew->id             =   $newid;
        $addnew->token          =   $token;
        $addnew->name           =   $file->getClientOriginalName();
        $addnew->path           =   $base . $path . $namefile;
        $addnew->order_id       =   trim($request->order_id);
        $addnew->user_id        =   trim($request->user_id);
        $addnew->status         =   1;
        $addnew->save();


        //data upload
        $dataupload = [
            'name'          =>  $namefile,
            'file'          =>  $file,
            'path'          =>  $path,
            "URL"           =>  $Config->apps()["URL"]["STORAGE"] . "/s3/upload/file"
        ];

        $upload = new \App\Http\Controllers\tdparty\s3\herbindo;
        $upload = $upload->transfer($dataupload);

    }


    // upload image
    public function uploadbb($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblObeUploadBbs::count(),
            'length'        =>  15
        ]);

        $token = md5($newid);
        $file = $request->file('file_image');
        $namefile = $token . '.' . $file->getClientOriginalExtension();
        
        $base = $Config->path()['s3'];
        $path = '/images/distributor/order/';
        $url = $Config->apps()["URL"]["STORAGE"] . '/images/distributor/order/' . $token . ".jpg";
        

        $addnew                 =   new tblObeUploadBbs;
        $addnew->id             =   $newid;
        $addnew->token          =   $token;
        $addnew->name           =   $file->getClientOriginalName();
        $addnew->path           =   $base . $path . $token;
        $addnew->url            =   $url;
        $addnew->nominal        =   $Config->numbers(trim($request->nominal));
        $addnew->order_id       =   trim($request->order_id);
        $addnew->user_id        =   trim($request->user_id);
        $addnew->paid           =   0;
        $addnew->paid_date      =   '';
        $addnew->paid_user      =   0;
        $addnew->status         =   1;
        $addnew->save();


        //data upload
        $dataupload = [
            'name'          =>  $token,
            'file'          =>  $file,
            'path'          =>  $path,
            "URL"           =>  $Config->apps()["URL"]["STORAGE"] . "/s3/upload/transfer"
        ];

        $upload = new \App\Http\Controllers\tdparty\s3\herbindo;
        $upload = $upload->transfer($dataupload);

        return $url;

    }


    //add items
    public function additem($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblObeItems::count(),
            'length'        =>  15
        ]);

        $addnew             =   new tblObeItems;
        $addnew->id         =   $newid;
        $addnew->order_id   =   trim($request->order_id);
        $addnew->product_id =   trim($request->product_id);
        $addnew->quantity   =   0;
        $addnew->price      =   trim($request->price);
        $addnew->subtotal      =   0;
        $addnew->receiver   =   '';
        $addnew->address    =   '';
        $addnew->courier    =   '';
        $addnew->ongkir     =   0;
        $addnew->total      =   0;
        $addnew->user_id    =   trim($request->user_id);
        $addnew->status     =   1;
        $addnew->save();

        return ["id"=>$newid];
    }
}