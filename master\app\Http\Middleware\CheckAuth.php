<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CheckAuth
{
    public function handle(Request $request, Closure $next): Response
    {
        // Cek apakah ada token di header
  $token = $request->bearerToken();
    if ($request->header('Accept') === 'application/json' && $token) {
        return response()->json(['token' => $token]);
    } else {
        return response()->json(['error' => 'Unauthorized'], 401);
    }

        // Lanjutkan permintaan
        return $next($request);
    }
}
