<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePurchaseOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->integer('no');
            $table->string('kode');
            $table->integer('suplier');
            $table->bigInteger('user_id');
            $table->integer('progress');
            $table->bigInteger('user_progress');
            $table->string('date_progress');
            $table->integer('total');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('purchase_orders');
    }
}
