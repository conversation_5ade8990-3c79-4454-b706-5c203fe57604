<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
class ActivityService
{
    public function sendActivity(array $data): bool
    {
        try {
              $phone = DB::table('users')
                ->join('profile', 'users.id', '=', 'profile.user_id')
                ->where('users.email', $data['users_email'] ?? '')
                ->value('phone');

            $response = Http::post('https://min.ripit.id/api.php?act=activity', [
                'users_nama'           => $data['users_nama'] ?? '',
                'users_email'          => $data['users_email'] ?? '',
                'users_phone'          => $phone ?? 0,
                'users_role'           => $data['users_role'] ?? '',
                'users_project_name'   => $data['users_project_name'] ?? '',
                'users_activity'       => $data['users_activity'] ?? '',
                'users_activity_value' => $data['users_activity_value'] ?? '',
            ]);
            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Konekwa Activity API error: ' . $e->getMessage());
            return false;
        }
    }
}
