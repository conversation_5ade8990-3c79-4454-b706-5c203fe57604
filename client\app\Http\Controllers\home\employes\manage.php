<?php
namespace App\Http\Controllers\home\employes;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\Http\Controllers\account\index as Account;
use App\user_employes as tblUserEmployes;
use App\users as tblUsers;
use DB;

class manage extends Controller
{
    //
    public function createAccount(Request $request)
    {
        $CekAccount = new Account;
        $account = $CekAccount->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);
                
        //
        $employe_id = trim($request->employe_id);

        // CHECK
        $cek = tblUserEmployes::where([
            'id'        =>  $employe_id
        ])
        ->first();

        //jika id tidak ditemukan
        if( $cek == null)
        {
            $data = [
                'message'       =>  'Data tidak ditemukan'
            ];

            return response()->json($data,404);
        }


        //CHECK EMAIL
        $cekemail = tblUsers::where([
            'email'     =>  $cek->email
        ])->first();

        //JIKA EMAIL TERDAFTAR TETAPI LEVEL NYA BUKAN 1
        if( $cekemail != null && $cekemail->level != 1)
        {
            $data = [
                'message'       =>  'Alamat email telah terdaftar sebagai partner'
            ];
            return response()->json($data, 401);
        }

        //JIKA EMAIL NULL MAKA BUAT AKUN USER
        if( $cekemail == null )
        {
             // DATA USER    
            $dataUser = [
                'name'          =>  $cek->name,
                'email'         =>  $cek->email,
                'password'      =>  '',
                'username'      =>  '',
                'company_id'    =>  $cek->company_id,
                'level'         =>  1,
                'sub_level'     =>  3,
                'gender'        =>  $cek->gender,
                'phone'         =>  $cek->phone,
                'phone_code'        =>  62,
                'admin_id'          =>  $account['id'],
                'company_id'        =>  $cek->company_id
            ];

            //add user
            $addUser = new \App\Http\Controllers\models\users;
            $addUser = $addUser->createEmploye($dataUser);
        }

        //JIKA EMAIL ADA DAN LEVEL 1 MAKA HANYA UPDATE
        // 1. BUAT MENU HOME
        // 2. UPDATE TABLE EMPLOYE DI FIELD EMPLOYE ID
        $createMenu = new \App\Http\Controllers\home\menu\manage;
        $createMenu = $createMenu->createMenus(['employe_id'=>$employe_id]);
        
        //UPDATE FIELD USER ID
        $updateemploye = tblUserEmployes::where([
            'id'        =>  $employe_id
        ])
        ->update([
            'user_id'       => $cekemail === null ? $addUser['id'] : $cekemail['id']
        ]);

        //
        $data = [
            'message'       =>  'success'
        ];

        return response()->json($data,200);
    }
}