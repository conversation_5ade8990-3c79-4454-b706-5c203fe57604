<?php
namespace App\Http\Controllers\home\inventory\suplier;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\supliers as tblSupliers;
use App\Http\Controllers\config\index as Config;

class table extends Controller
{
    //
    public function main(Request $request)
    {
        $Config = new Config;

        //
        $src = '%' . trim($request->search) .'%';
        $paging = trim($request->paging);
        $sort = trim($request->sort_name);

        //
        $getdata = tblSupliers::from('supliers as s')
        ->select(
            's.id', 's.type', 's.name', 's.phone', 's.fax', 's.email','s.website', 's.mp',
            's.owner', 's.owner_phone', 's.owner_email', 's.mp as marketplace','s.website',
            's.address', 's.kodepos',
            's.created_at as date',
            'op.name as provinsi_name',
            'oc.name as city_name', 'oc.type_label as city_label',
            'ok.name as kecamatan_name',
            'ct.alias as type_name'
        )
        ->leftJoin('app_origin_provinsis as op', function($join)
        {
            $join->on('op.id', '=', 's.provinsi');
        })
        ->leftJoin('app_origin_cities as oc', function($join)
        {
            $join->on('oc.id', '=', 's.city');
        })
        ->leftJoin('app_origin_kecamatans as ok', function($join)
        {
            $join->on('ok.id', '=', 's.kecamatan');
        })
        ->leftJoin('corporate_types as ct', function($join)
        {
            $join->on('ct.id', '=', 's.type');
        })
        ->where([
            ['s.name', 'like', $src],
            ['s.status',    '=',    1]
        ]);

        $count = $getdata->count();
        //
        if( $count === 0) //NULL
        {
            $data = [
                'message'       =>  'Data Tidak ditemukan',
                'response'      =>  ''
            ];

            return response()->json($data, 404);
        }

        //
        $gettable = $getdata->orderBy('s.name', $sort)
        ->take($Config->table(['paging'=>$paging])['paging_item'])
        ->skip($Config->table(['paging'=>$paging])['paging_limit'])
        ->get();

        foreach($gettable as $row)
        {
            $list[] = [
                'id'            =>  $row->id,
                'type'          =>  $row->type === 1 ? '' : 'Maklon',
                'name'          =>  ($row->type_name === null ? '' : $row->type_name . " ") . $row->name,
                'phone'         =>  $row->phone,
                'email'         =>  $row->email,
                'owner'         =>  $row->owner,
                'owner_phone'   =>  $row->owner_phone,
                'owner_email'   =>  $row->owner_email,
                'address'       =>  $row->address,
                'address2'       =>  ('Kec.' . $row->kecamatan_name . '-' . $row->city_label . '. ' . $row->city_name),
                'address3'      =>  'Prov. ' . $row->provinsi_name . ' ' . $row->kodepos,
                'marketplace'   =>  $row->marketplace,
                'website'       =>  $row->website,
                'kodepos'       =>  $row->kodepos,
                'date'          =>  $Config->timeago($row->date)
            ];
        }

        //
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'list'          =>  $list,
                'paging'        =>  $paging,
                'total'         =>  $count,
                'countpage'     =>  ceil($count / $Config->table(['paging'=>$paging])['paging_item'] )
            ]
        ];

        return response()->json($data, 200);

    }
}