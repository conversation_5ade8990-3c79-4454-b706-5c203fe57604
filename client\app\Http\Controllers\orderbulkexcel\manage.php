<?php
namespace App\Http\Controllers\orderbulkexcel;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\order_bulking_excels as tblOrderBulkingExcels;
use App\obe_upload_excels as tblObeUploadExcels;
use App\obe_upload_bbs as tblObeUploadBbs;
use App\obe_items as tblObeItems;
use DB;

class manage extends Controller
{
    //
    public function create(Request $request)
    {
        //check
        $uid = trim($request->user_id);


        //check
        $check = tblOrderBulkingExcels::where([
            'distributor_id'        =>  trim($request->distributor_selected),
            'order_status'          =>  0,
            'status'                =>  1
        ])->first();

        if( $check == null )
        {
            $addnew = new \App\Http\Controllers\models\orderbulkingexcel;
            $addnew = $addnew->main($request);

            $updatefield = $this->updatefield(['id'=>$addnew['id']]);
            $data = $this->vieworder(['id'=>$addnew['id']]);

            // return $this->vieworder(['id'=>$addnew['id']]);
            return response()->json($data, 200);
        }


        $data = $this->vieworder(['id'=>$check->id]);

        return response()->json($data, 200);
    }


    public function updatefield($request)
    {
        $id = $request['id'];

        $getdata = tblOrderBulkingExcels::from("order_bulking_excels as obe")
        ->select(
            'uc.name as distributor',
            'uc.contact as distributor_contact',
            'u.name as admin', 'u.phone as admin_phone'

        )
        ->join('user_companies as uc', function($join)
        {
            $join->on('uc.id', '=', 'obe.distributor_id');
        })
        ->join('users as u', function($join)
        {
            $join->on('u.id', '=', 'obe.user_id');
        })
        ->where([
            'obe.id'        =>  $id
        ])
        ->first();

        $field = [
            'customer'  =>  [
                'name'          =>  $getdata->distributor,
                'phone'         =>  json_decode($getdata->distributor_contact,true)['phone']
            ],
            'admin'     =>  [
                'name'         =>  $getdata->admin,
                'phone'         =>  $getdata->admin_phone
            ]
        ];

        $update = tblOrderBulkingExcels::where([
            'id'        =>  $id
        ])
        ->update([
            'field' =>  json_encode($field)
        ]);
    }


    public function vieworder($request)
    {
        $id = $request['id'];

        $getdata = tblOrderBulkingExcels::from("order_bulking_excels as obe")
        ->select(
            "obe.id", "obe.token", "obe.invoice", "obe.distributor_id",
            "obe.order_status", "obe.payment_type", "obe.payment", "obe.payment_date", "obe.total", "obe.debt", "obe.ongkir", "obe.discount", "obe.due_date", "obe.created_at", "obe.field", "obe.subtotal",
            DB::raw("IFNULL(oub.url, '') as url_image"),
            DB::raw("IFNULL(u.name, '') as admin_payment")
        )
        ->leftJoin('obe_upload_bbs as oub', function($join)
        {
            $join->on('oub.order_id', '=', 'obe.id')
            ->where([
                'oub.status'        =>  1
            ]);
        })
        ->leftJoin('users as u', function($join)
        {
            $join->on('u.id', '=', 'obe.payment_user');
        })
        ->where([
            'obe.id'        =>  $id
        ])
        ->first();

        $field = json_decode($getdata->field, true);
    
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'id'            =>  $id,
                'token'         =>  $getdata->token,
                'invoice'       =>  $getdata->invoice,
                'url'           =>  $getdata->url_image,
                'status'        =>  $getdata->order_status,
                'subtotal'      =>  $getdata->subtotal,
                'total'         =>  $getdata->total,
                'ongkir'        =>  $getdata->ongkir,
                'debt'          =>  $getdata->debt,
                'discount'      =>  $getdata->discount,
                'distributor_id'   =>  $getdata->distributor_id,
                'date_temp'     =>  date('d/m/Y', strtotime($getdata->created_at)),
                'due_date'      =>  $getdata->due_date,
                'company'       =>  [
                    'name'          =>  'Herbindo Persada'
                ],
                'customer'      =>  $field['customer'],
                'admin'         =>  $field['admin'],
                'payment'       =>  [
                    'status'        =>  $getdata->payment,
                    'type'          =>  $getdata->payment_type,
                    'admin'         =>  $getdata->admin_payment,
                    'date'          =>  $getdata->payment_date
                ],
                'list'          =>  $this->listproduct($id),
                'listpayment'   =>  $this->viewlistpayment($id)
            ]
        ];

        return $data;
        // return response()->json($data, 200);
    }

    public function listproduct($request)
    {
        $id = $request;

        $getlist = tblObeItems::from('obe_items as oi')
        ->select(
            'oi.id', 'oi.product_id', 'oi.quantity', 'oi.price', 'oi.subtotal', 'oi.receiver', 'oi.address', 'oi.courier', 'oi.ongkir', 'oi.total',
            'p.name as product_name'
        )
        ->leftJoin('products as p', function($join)
        {
            $join->on('p.id', '=', 'oi.product_id');
        })
        ->where([
            'oi.order_id'      =>  $id,
            'oi.status'        =>  1
        ]);

        $count = $getlist->count();
        if( $count > 0)
        {
            foreach($getlist->get() as $row)
            {
                $list[] = [
                    'id'        =>  $row->id,
                    'product_id'    =>  $row->product_id,
                    'product_name'  =>  $row->product_name,
                    'quantity'      =>  $row->quantity,
                    'price'         =>  $row->price,
                    'subtotal'      =>  $row->subtotal,
                    'receiver'      =>  $row->receiver,
                    'address'       =>  $row->address,
                    'courier'       =>  $row->courier,
                    'ongkir'        =>  $row->ongkir,
                    'total'         =>  $row->total
                ];
            }

            return $list;
        }

        return "";
    }

    public function getview(Request $request)
    {
        $id = $request['id'];

        $check = tblOrderBulkingExcels::where([
            'id'        =>  $id,
            'status'    =>  1
        ])->count();

        if( $check == 0)
        {
            $data = [
                'message'       =>  ''
            ];

            return response()->json($data, 404);
        }

        $data = $this->vieworder(['id'=>$id]);

        return response()->json($data, 200);
    }


    //UPLOAD EXCEL
    public function uploadexcel(Request $request)
    {
        $Config = new Config;

        $updage = tblObeUploadExcels::where([
            'order_id'      =>  trim($request->order_id),
            'status'        =>  1
        ])
        ->update([
            'status'        =>  0
        ]);
        

        $addnew = new \App\Http\Controllers\models\orderbulkingexcel;
        $addnew = $addnew->uploadexcel($request);

        $data = [
            'message'       =>  '',
        ];

        return response()->json($data,200);
    }


    // UPLOAD IMAGE
    public function uploadbb(Request $request)
    {
        $Config = new Config;

        // $updage = tblObeUploadBbs::where([
        //     'order_id'      =>  trim($request->order_id),
        //     'status'        =>  1
        // ])
        // ->update([
        //     'status'        =>  0
        // ]);
        
        $addnew = new \App\Http\Controllers\models\orderbulkingexcel;
        $addnew = $addnew->uploadbb($request);

        $data = [
            'message'       =>  ''
        ];

        return response()->json($data,200);
    }


    public function createitem(Request $request)
    {
        
        $check = tblObeItems::where([
            'order_id'          =>  trim($request->order_id),
            'product_id'        =>  trim($request->product_id)
        ])->first();

        if( $check == null)
        {
            $addnew = new \App\Http\Controllers\models\orderbulkingexcel;
            $addnew = $addnew->additem($request);

            $id = $addnew['id'];
        }
        else
        {
            $update = tblObeItems::where([
                'id'        =>  $check->id
            ])
            ->update([
                'quantity'  =>  0,
                'price'     =>  trim($request->price),
                'subtotal'     =>  0,
                'receiver'  =>  '',
                'address'   =>  '',
                'courier'   =>  '',
                'total'     =>  0,
                'status'    =>  1
            ]);
            $id = $check->id;
        }


        //updatefield
        $updatefield = $this->updateitems(['id'=>trim($request->order_id)]);

        $data = [
            'message'       =>  '',
            'response'      =>  [
                'id'            =>  $id
            ]
        ];

        return response()->json($data, 200);
    }

    public function updateitem(Request $request)
    {

        $field = trim($request->field);

        $update = tblObeItems::where([
            'id'        =>  trim($request->id)
        ])
        ->update([
            $field      =>  trim($request->value),
            "subtotal"  =>  trim($request->subtotal),
            "total"     =>  trim($request->total)
        ]);

        $getdata = tblObeItems::where([
            'id'        =>  trim($request->id)
        ])->first();

        $updatefield = $this->updateitems(['id'=>trim($getdata->order_id)]);

        $data = [
            'message'       =>  ""
        ];

        return response()->json($data, 200);
    }

    //
    public function deleteitem(Request $request)
    {

        $id = trim($request->id);

        $update = tblObeItems::where([
            'id'        =>  $id,
            'status'    =>  1
        ])
        ->update([
            'status'    =>  0
        ]);

        $getdata = tblObeItems::where([
            'id'        =>  $id
        ])->first();

        $updatefield = $this->updateitems(['id'=>trim($getdata->order_id)]);

        $data = [
            'message'       =>  ""
        ];

        return response()->json($data, 200);
    }

    //SAVE
    public function save(Request $request)
    {
        $Config = new Config;

        $id = trim($request->order_id);

        $update = tblOrderBulkingExcels::where([
            'id'        =>  $id,
            'status'    =>  1
        ])
        ->update([
            'order_status'  =>  1,
            'discount'      =>  trim($request->discount),
            'payment_type'  =>  trim($request->payment_type),
            'due_date'      =>  trim($request->duedate)
        ]);

        $data = [
            'message'       =>  'Order Distributor berhasil disimpan'
        ];

        return response()->json($data, 200);
    }

    public function updateitems($request)
    {

        $id = $request['id'];
        
        //items
        $getdatapd = tblObeItems::from("obe_items as obi")
        ->select(
            'obi.quantity', 'p.name as product'   
        )
        ->leftJoin('products as p', function($join)
        {
            $join->on('p.id', '=', 'obi.product_id');
        })
        ->where([
            'obi.order_id'      =>  $id,
            'obi.status'        =>  1
        ]);

        $countpd = $getdatapd->count();

        if( $countpd > 0)
        {
            foreach($getdatapd->get() as $row)
            {
                $listpd[] = '(' . $row->quantity . 'x) ' . $row->product;
            }
        }


        //total
        $gettotal = tblObeItems::select(
            'subtotal', 'total', 'ongkir'
        )
        ->where([
            'order_id'      =>  $id,
            'status'        =>  1
        ])->get();

        $subtotal = 0; $total = 0; $ongkir = 0;
        foreach ($gettotal as $row)
        {
            $subtotal += $row->subtotal;
            $total += $row->total;
            $ongkir += $row->ongkir;
        }


        $items = [
            'product'   =>  $countpd > 0 ? implode(",", $listpd) : '',
        ];


        //UPDATE
        $updateobe = tblOrderBulkingExcels::where([
            'id'        =>  $id
        ])
        ->update([
            'items' =>  json_encode($items),
            'subtotal'      =>  $subtotal,
            'total'         =>  $total,
            'ongkir'        =>  $ongkir
        ]);

    }


    //LIST PAYMENT
    public function listpayment(Request $request)
    {
        $Config = new Config;

        $id = trim($request->id);

        
        $list = $this->viewlistpayment($id);


        $data = [
            'message'       =>  '',
            'list'          =>  $list
        ];

        return response()->json($data,200);
        

    }

    public function viewlistpayment($request)
    {
        $id = $request;
        //
        $getdata = tblObeUploadBbs::from("obe_upload_bbs as oub")
        ->select(
            'oub.id', 'oub.token', 'oub.nominal', 'oub.url', 'oub.created_at', 'oub.paid', 'oub.paid_date',
            'u.name as admin',
            DB::raw("IFNULL(ad.name, '') as paid_admin")
        )
        ->leftJoin("users as u", function($join)
        {
            $join->on('u.id', '=', 'oub.user_id');
        })
        ->leftJoin("users as ad", function($join)
        {
            $join->on('ad.id', '=', 'oub.paid_user');
        })
        ->where([
            'oub.order_id'      =>  $id,
            'oub.status'        =>  1
        ]);

        $count = $getdata->count();

        if( $count > 0 )
        {
            $gettable = $getdata->get();

            foreach($gettable as $row)
            {
                $list[] = [
                    'id'        =>  $row->id,
                    'token'     =>  $row->token,
                    'nominal'   =>  $row->nominal,
                    'url'       =>  $row->url,
                    'admin'     =>  $row->admin,
                    'date'      =>  date('d/m/Y H.i', strtotime($row->created_at)),
                    'paid'      =>  $row->paid,
                    'paid_date' =>  $row->paid_date === '' ? '' : date('d/m/Y H.i', strtotime($row->paid_date)),
                    'paid_admin'=>  $row->paid_admin
                ];
            }


            return $list;

        }

        return '';
    }

    //verif payment
    public function verifpayment(Request $request)
    {
        $id = trim($request->id);
        $order_id = trim($request->order_id);
        $user_id = trim($request->user_id);
        $type = trim($request->type);
        $nominal = trim($request->nominal);

        $check = tblObeUploadBbs::where([
            'id'        =>  $id,
            'paid'      =>  0,
            'status'    =>  1
        ])
        ->count();

        if( $check == 0)
        {
            $data = [
                'message'       =>  'Data pembayaran tidak ditemukan'
            ];

            return response()->json($data, 404);
        }

        if( $type == 'delete')
        {
            $delete = tblObeUploadBbs::where([
                'id'        =>  $id
            ])
            ->update([
                'status'        =>  0
            ]);

            $data = [
                'message'       =>  'Pembayaran berhasil dihapus'
            ];
    
            return response()->json($data, 200);
        }


        //UPDATE BBS
        $update = tblObeUploadBbs::where([
            'id'        =>  $id
        ])
        ->update([
            'paid'      =>  1,
            'paid_date' =>  date('Y-m-d H:i:s', time()),
            'paid_user' =>  $user_id
        ]);

        //DEBT
        $totaldebt = tblObeUploadBbs::where([
            'order_id'      =>  $order_id,
            'paid'          =>  1,
            'status'        =>  1
        ])->sum('nominal');


        //getdata OBE
        $getdata = tblOrderBulkingExcels::where([
            'id'        =>  $order_id
        ])->first();

        $debt = $getdata->payment_type === 0 ? 0 : (($getdata->total - $totaldebt ) - $getdata->discount);

        //UPDATE OBE
        $updateOBE = tblOrderBulkingExcels::where([
            'id'        =>  $order_id
        ])
        ->update([
            'debt'      =>  $debt
        ]);

        $data = [
            'message'       =>  'Pembayaran berhasil diverifikasi',
            'response'      =>  [
                'id'            =>  $id,
                'debt'          =>  $debt
            ]
        ];

        return response()->json($data, 200);
    }

    public function paid(Request $request)
    {
        $id = trim($request->id);
        $user_id = trim($request->user_id);

        $update = tblOrderBulkingExcels::where([
            'id'        =>  $id,
            'status'    =>  1
        ])
        ->update([
            'debt'          =>  0,
            'payment'       =>  1,
            'payment_user'  =>  $user_id,
            'payment_date'  =>  date('Y-m-d H:i:s', time())
        ]);


        $getadmin = DB::table("users")
        ->where([
            'id'        =>  $user_id
        ])->first();


        $data = [
            'message'   =>  'Order behasil terbayar',
            'response'  =>  [
                'admin'     =>  $getadmin->name,
                'date'      =>  date('d/m/Y H.i', time())
            ]
        ];

        return response()->json($data, 200);

    }


    //GET INVOICE
    public function getinvoice(Request $request)
    {
        $Config = new Config;

        $token = $request->token;

        $getdata = tblOrderBulkingExcels::where([
            'token'     =>  $token
        ])
        ->first();

        if( $getdata == null)
        {
            $data = [
                'message'       =>  'Data tidak ditemukan'
            ];

            return response()->json($data, 404);

        }

        
        $data = $this->vieworder(['id'=>$getdata->id]);

        return response()->json($data, 200);
        
    }
}