<?php
namespace App\Http\Controllers\models;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\user_employes as tblUserEmployes;
use App\employe_menus as tblEmployeMenus;
use App\Http\Controllers\config\index as Config;

class employe extends Controller
{
    //
    public function menus($request)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblEmployeMenus::count(),
            'length'        =>  9
        ]);

        $addnew             =   new tblEmployeMenus;
        $addnew->id         =   $newid;
        $addnew->employe_id =   $request['id'];
        $addnew->menu       =   json_encode($request['menu']);
        $addnew->status     =   1;
        $addnew->save();
    }
}