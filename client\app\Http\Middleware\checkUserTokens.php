<?php
namespace App\Http\Middleware;
// use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\api_user_tokens as tblApiUserTokens;
use Closure;
use Exception;

class checkUserTokens
{
    public function handle($request, Closure $next)
    {

        try{
            $thistime = strtotime(date('Y-m-d', time()));

            //
            $getdata = tblApiUserTokens::where([
                'token'             =>  $request->header('key'),
                'published'         =>  1,
                'status'            =>  1
            ])->first();

            //check publisehd
            if($getdata == null){

                $data = [
                    'message'       =>  'User Token not found!',
                    'error'         =>  [
                        'status'        =>  404
                    ]
                ];

                return response()->json($data, 404);
                // return $data;
            }

            //check expired
            if($thistime > $getdata->expired_time){
                $data = [
                    'message'       =>  'User Token expired!',
                    'error'         =>  [
                        'status'        =>  401
                    ]
                ];

                // return $data;
                return response()->json($data, 401);

            }

        }
        catch(Exception $error){
            $data = [
                'message'   =>  $error
            ];

            return response()->json($data, 500);

        }

        return $next($request);
    }
}