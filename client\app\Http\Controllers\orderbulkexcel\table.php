<?php
namespace App\Http\Controllers\orderbulkexcel;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\order_bulking_excels as tblObes;
use DB;

class table extends Controller
{
    //
    public function main(Request $request)
    {
        $Config = new Config;

        $paging = trim($request->paging);
        $customerid = trim($request->customerid);
        $sort_date = trim($request->sortdate);
        $search = '%' . trim($request->src) . '%';
        $status = trim($request->status);
        $date = trim($request->date);
        $page = trim($request->page);
        
        $getdata = tblObes::from('order_bulking_excels as obe')
        ->select(
            'obe.token', 'obe.id', 'obe.invoice', 'obe.created_at as date', 'obe.field', 'obe.items', 'obe.status', 'obe.payment_type', 'obe.order_status', 'obe.payment', 'obe.total', 'obe.debt', 'obe.due_date', 'obe.ongkir',  'obe.subtotal', 'obe.discount'
        )
        ->where([
            ['obe.invoice', 'like', $search],
            // ['obe.status', '=', 1]
        ]);
        if( $page == 'verif')
        {
            $getdata = $getdata->where([
                'obe.order_status'  =>  1
            ]);
        }
        if( $customerid != '-1')
        {
            $getdata = $getdata->where([
                'obe.distributor_id'        =>  $customerid
            ]);
        }
        if( $status != '-1')
        {
            if( $status == '1')
            {
                $getdata = $getdata->where([
                    'obe.order_status'  =>  1,
                    'obe.payment'       =>  1,
                    'obe.status'        =>  1
                ]);
            }
            elseif ( $status == '2')
            {
                $getdata = $getdata->where([
                    'obe.order_status'  =>  1,
                    'obe.payment'       =>  0,
                    'obe.status'        =>  1
                ]);
            }
            elseif ( $status == '3')
            {
                $getdata = $getdata->where([
                    'obe.order_status'  =>  0,
                    'obe.payment'       =>  0,
                    'obe.status'        =>  1
                ]);
            }
            else
            {
                $getdata = $getdata->where([
                    'obe.status'        =>  0
                ]);
            }
        }
        if( $date != '')
        {
            $date = explode("-", $date);
            $start = $Config->changeDateYMD($date[0]);
            $end = date("Y-m-d", strtotime($Config->changeDateYMD($date[1]) . '+1 day') );

            $getdata = $getdata->whereBetween('obe.created_at', [$start, $end]);
        }

        $count = $getdata->count();

        if( $count > 0 )
        {
            $gettable = $getdata->orderBy('obe.created_at', $sort_date)
            ->take($Config->table(['paging'=>$paging])['paging_item'])
            ->skip($Config->table(['paging'=>$paging])['paging_limit'])
            ->get();

            $gettempwa = DB::table('app_whatsapp_templates')
            ->where([
                'id'        =>  1000001
            ])->first();


            foreach ($gettable as $row)
            {
                $field = json_decode($row->field, true);
                $items = json_decode($row->items, true);
                
                $linkinvoice = $Config->apps()['URL']["APP"] . '/invoice/distributor?uid=' . $row->token;
                $linkcs = str_replace("{{phone}}", '62' . (int)$field['admin']['phone'], $Config->apps()['URL']['APIWASEND']);

                $contentwa = $gettempwa->content;

                $contentwa = str_replace("{{name}}", $field['customer']['name'], $contentwa);
                $contentwa = str_replace("{{linkinvoice}}", $linkinvoice, $contentwa);
                $contentwa = str_replace("{{linkcs}}", $linkcs, $contentwa);

                $linkwa = $Config->apps()['URL']['APIWATEXT'];
                $linkwa = str_replace("{{text}}", rawurlencode($contentwa), $linkwa);

                $list[] = [
                    'id'            =>  $row->id,
                    'token'         =>  $row->token,
                    'invoice'       =>  $row->invoice,
                    'date'          =>  $Config->timeago($row->date),
                    'admin'         =>  $field['admin'],
                    'customers'     =>  $field['customer'],
                    'ongkir'        =>  $row->ongkir,
                    'discount'      =>  $row->discount,
                    'linkwa'        =>  $linkwa,
                    'orders'        =>  [
                        'total'         =>  ($row->total - $row->discount),
                        'status'        =>  $row->order_status,
                        'debt'          =>  $row->debt,
                        'subtotal'      =>  $row->subtotal
                    ],
                    'product'       =>  $items['product'] === '' ? '' : explode(",", $items['product']),
                    'status'        =>  $row->status,
                    'payment'       =>  [
                        'type'          =>  $row->payment_type === 1 ? 'Tenor' : 'Transfer',
                        'status'        =>  $row->payment,
                        'date'          =>  date('d/mY', strtotime($row->payment_date)),
                        'due_date'      =>  $row->due_date
                    ]
                ];
            }
            
            $data = [
                'message'       =>  '',
                'response'      =>  [
                    'paging'        =>  $paging,
                    'total'         =>  $count,
                    'countpage'     =>  ceil($count / $Config->table(['paging'=>$paging])['paging_item'] ),
                    'list'          =>  $list
                ]
            ];

            return response()->json($data, 200);
        }

        $data = [
            'message'       =>  'Data tidak ditemukan'
        ];

        return response()->json($data, 404);

    }
}