<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\BaseController as BaseController;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Http\Resources\ProjectResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\BtApi;
use App\Models\CloudflareModel;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Mail\InviteToProjectMail;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Illuminate\Support\Facades\View; // Tambahkan ini
use Illuminate\Support\Facades\Http;
use App\Models\PaketPackage;
use App\Models\Order;
use App\Models\Profile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\VoucherPaketPackage;
use PDF;
use App\Services\WaService;
use App\Services\ErrorService;






class ProjectController extends BaseController
{
    protected $waService;
    protected $errorService;
    public function __construct(WaService $waService, ErrorService $errorService)
    {
        $this->waService = $waService;
        $this->errorService = $errorService;
    }
    /**
     * Display a listing of the resource.f
     */


//table 
public function table(Request $request)
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    // Mengambil user beserta project yang terkait
    $user = User::with('projects', 'profile')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Memfilter proyek berdasarkan parameter pencarian (src)
    $searchTerm = $request->input('src', ''); // Default ke string kosong jika tidak ada parameter src

    // Menambahkan logika pencarian dan paging
    $projects = $user->projects()->where(function($query) use ($searchTerm) {
        $query->where('project_name', 'like', "%$searchTerm%")
              ->orWhere('project_type', 'like', "%$searchTerm%");
    })
    ->paginate(10); // 10 proyek per halaman

    // Melakukan enkripsi pada project_key dan user_key jika projects tidak null
    if ($projects) {
        $projects->each(function($project) {
            $project->project_key = bin2hex($project->project_key);
        });
    }
    
    $user->user_key = bin2hex($user->user_key);

    return response()->json([
        'code' => 1,
        'message' => 'Proyek berhasil diambil.',
        'result' => $projects
    ], 200);
}



public function index(Request $request)
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    // Mengambil user beserta project yang terkait
    $user = User::with('projects','profile')->find($user_id);
    // $user = User::where('id', $user_id)
    // ->whereHas('projects', function ($query) use ($request) {
    //     $query->where('project_key', hex2bin($request->project_key));
    // })
    // ->with([
    //     'projects' => function ($query) use ($request) {
    //         $query->where('project_key', hex2bin($request->project_key));
    //     },
    //     'profile'
    // ])
    // ->first();

    

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Melakukan enkripsi pada project_key dan user_key jika projects tidak null
    if ($user->projects) {
        $user->projects->each(function($project) {
            $project->project_key = bin2hex($project->project_key);
        });
    }
    $user->user_key = bin2hex($user->user_key);
    $results = [];
    $total_keseluruhan = 0;
    $total_device_keseluruhan = 0;
//  $data_token = [
//         'token' => $request->bearerToken(), // pastikan $token benar terisi sebelum foreach
//         'id' => $userToken->id,
//         'expired' => $userToken->expired,
//         'user_id' => $user_id
//     ];
//         $response_product = Http::post('https://cloud-c.ripit.id' . '/sync-login' . 'client_id=1&project_id=011be75cc3d7e780b56833bb76aec1a6', $data_token);
//         return response()->json($response_product->json(),500);
        
    foreach ($user->projects as $project) {
    $role_id = $user['role_id'];
    $plan_usage_user = $user['usage_paket'];

    if(($role_id == 2 && $user->paket_id == null) || ($role_id == 4 && $user->paket_id == null)){
    
        $owners = DB::table('user_projects')
            ->join('users', 'user_projects.user_id', '=', 'users.id')
            ->where('user_projects.project_id', '=', $project->id)
            ->where('users.role_id', '=', 1) // Filter untuk owner
            ->select('users.id as user_id', 'users.paket_id as user_paket_id')
            ->get();

        if (!empty($owners)) {
            // Ambil user_id dari data pertama
            $ownerId = $owners[0]->user_paket_id;
        }
        
    $planDetail = PaketPackage::where('id', $ownerId)->first()->detail;
    }
    
    
    $planDetail = PaketPackage::where('id', $user->paket_id)->first()->detail ?? [];
    
    $param = '?project_key=' . $project['project_key'] . '&client_id=' . $project['id'];
    // $endpoint = 'https://' . $project->endpoint;
    $endpoint = 'https://cloud-c.ripit.id';
    // return response()->json($endpoint);
    
        $data_post = [
            'data_user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'token' => bin2hex($user['user_key']),
                'password' => bin2hex($user['password']),
                'phone' => $user['profile']['phone'],
            ],
            'data_project' => [
                'project_id' => $project['id'],
                'project_name' => $project['project_name'],
                'project_key' => $project['project_key'],
                'project_type' => $project['project_type'],
                'endpoint' => $project['endpoint'],
            ],
            'data_plan' => [
                'plan' => $planDetail,
                'plan_usage' => $plan_usage_user ]
        ];
        $data_token = [
        'token' => $request->bearerToken(), // pastikan $token benar terisi sebelum foreach
        'id' => $userToken->id,
        'expired' => $userToken->expired,
        'user_id' => $user_id
    ];
    
    if($role_id != 1 ){
    $projectIds_noOwners = DB::table('user_projects')
    ->where('user_id', $user_id)
    ->pluck('project_id');

$userIdsOwners = DB::table('user_projects as up')
    ->join('users as u', 'u.id', '=', 'up.user_id')
    ->whereIn('up.project_id', $projectIds_noOwners)
    ->where('up.user_id', '!=', $user_id)
    ->where(function ($query) {
        $query->where('u.role_id', 1);
    })->pluck('up.user_id');
$userIdsOwners = $userIdsOwners[0];

$billing = Order::where(['user_id' => $userIdsOwners, 'transaction_status' => 'SUCCESS'])->get();
$data_billing = $billing->toArray(); // <- ini penting!

try {
   $response_billing = Http::post($endpoint . '/sync-billing' . $param  , $data_billing);
   Log::info('Response sync billing (non-owner)', [
            'status' => $response_billing->status(),
            'body' => $response_billing->body()
        ]);
    // return response()->json($response_billing->json());
} catch (\Exception $e) {
            Log::error('Error saat kirim billing (non-owner)', ['message' => $e->getMessage()]);
}

}elseif($role_id == 1){
    $billing = Order::where(['user_id' => $user['id'] , 'transaction_status' => 'SUCCESS'])->get();
    $data_billing = $billing->toArray(); // <- ini penting!

try {
   $response_billing = Http::post($endpoint . '/sync-billing' . $param  , $data_billing);
    Log::info('Response sync billing (non-owner)', [
            'status' => $response_billing->status(),
            'body' => $response_billing->body()
        ]);
    // return response()->json($response_billing->json());
} catch (\Exception $e) {
    Log::error('Error saat kirim billing (non-owner)', ['message' => $e->getMessage()]);
}
}



    try {
        Log::info("Sync to project: {$project['project_key']}");

        $response_product = Http::post($endpoint . '/sync-login' . $param, $data_token);

        Log::info('Sync token response:', [
            'project' => $project['project_key'],
            'status' => $response_product->status(),
            'body' => $response_product->body()
        ]);

        // Jika perlu, lanjutkan ke /sync-data atau endpoint lain
        // Http::post($endpoint . '/sync-data' . $param, $data_post);

    } catch (\Exception $e) {
        Log::error('Sync token failed for project: ' . $project['project_key'], [
            'error' => $e->getMessage()
        ]);
    }



      
//   try {
//     // Make the request to the API endpoint
//     $response_product = Http::post($endpoint . '/totalProducts', $token);


//     // Check if the response is successful (status code 200) and the required data exists
//     if ($response_product->successful()) {
//         $project_total_product = $response_product->json();
//         $total_keseluruhan += $project_total_product;

//     } else {
//         // If response is not successful or total_products is missing, set to 0
//         $total_keseluruhan = 0;
//     }
// } catch (\Exception $e) {
//     // Handle any exception that occurs (network issues, server error, etc.)
//     $total_keseluruhan = 0;
// }
// //total Device
// try {
//     // Make the request to the /totalDevice endpoint
//     $response_device = Http::post($endpoint . '/totalDevice', $token);

//     // Check if the response is successful and contains 'total_devices'
//     if ($response_device->successful()) {
//         $project_total_device = $response_device->json();
//         $total_device_keseluruhan += $project_total_device;
//     } else {
//         // Set to 0 if unsuccessful or key not found
//         $total_device_keseluruhan = 0;
//     }
// } catch (\Exception $e) {
//     // Handle any exception for /totalDevice request
//     $total_device_keseluruhan = 0;
// }
//   return response()->json($data_post);
 
 try {
    // 
    // Make the request to the /totalDevice endpoint
    $response = Http::post($endpoint . '/setting' . $param , $data_post);
    // Check if the response is successful and contains 'total_devices'
    if ($response->successful()) {
        $response = $response->json();
        $responseData = $response['response'];
                $responseMessage = $response['message'];
    
    } else {

        // Set to 0 if unsuccessful or key not found
         $responseData = [];
    }
} catch (\Exception $e) {
    // Handle any exception for /totalDevice request
     $responseData = [];
}       
 
//  try {
//     // Make the request to the /totalDevice endpoint
//     $response_plan = Http::post($endpoint . '/updatePlan', $data_post);

//     // Check if the response is successful and contains 'total_devices'
//     if ($response_plan->successful()) {
//         $response_plan = $response_plan->json();
//         $responseDataPlan = $response_plan['response'];
//         return response()->json($responseDataPlan);
//     } else {
//         // Set to 0 if unsuccessful or key not found
//          $responseDataPlan = [];
//     }
// } catch (\Exception $e) {
//     // Handle any exception for /totalDevice request
//      $responseDataPlan = [];
// }       

//perbarui plan_usage


        // Separate arrays for project and address data
        $data_project = [
            'client_id' => $project['id'] ,
            'project_key' => $project['project_key'],
            'public_token' => $responseData['public_token'] ?? 0,
            'project_name' => $project['project_name'],
            // 'endpoint' => 'https://' . $project['endpoint'] .'/api',
            'endpoint' => $endpoint .'/api',
            'created_at' => $project['created_at'],
            'updated_at' => $project['updated_at'],
        ];
    
        $address = [
            'id' => $responseData['id'] ?? 0,
            'address' => $responseData['address'] ?? 0,
            'provinsi' => $responseData['provinsi'] ?? 0,
            'city' => $responseData['city'] ?? 0,
            'kecamatan' => $responseData['kecamatan'] ?? 0,
            'kodepos' => $responseData['kodepos'] ?? 0,
        ];
        $role_id = $user['role_id'];
        if($role_id == 1){
        $defaultMenu = [
    [
        "name" => "Dashboard",
        "link" => "/dashboard",
        "sub" => [
            [
                "name" => "Get Product",
                "link" => "/dashboard/analysis/getProduct",
                "sub" => null
            ],
            [
                "name" => "Get Timeframe",
                "link" => "/dashboard/analysis/getTimeframe",
                "sub" => null
            ],
            [
                "name" => "Get Province",
                "link" => "/dashboard/analysis/getProvince",
                "sub" => null
            ],
            [
                "name" => "Analysis",
                "link" => "/dashboard/analysis?timeframe=&startDate=&endDate=&product=All&province=All",
                "sub" => null
            ]
        ]
    ],

    //orders 
    [
        "name" => "Orders",
        "link" => "/orders",
        "sub" => [
            [
                "name" => "Orders Bulking",
                "link" => "/orders/bulking",
                "sub" => [
                    ["name" => "Import", "link" => "/orders/import", "sub" => null],
                    ["name" => "Mapping", "link" => "/orders/mapping", "sub" => null],
                    ["name" => "Preview", "link" => "/orders/preview", "sub" => null],
                    ["name" => "Store", "link" => "/orders/store", "sub" => null]
                ]
            ],
            ["name" => "Product Table", "link" => "/product/table?src=&sort=asc&status=1&pg=1&type=0", "sub" => null],
            ["name" => "Orders Table", "link" => "/orders/table?pg=&sortdate=&start_date=&end_date=&status=-1", "sub" => null],
            ["name" => "Export Orders Table", "link" => "/orders/table/export?jumlah", "sub" => null],
            ["name" => "Create", "link" => "/orders/create", "sub" => null],
            ["name" => "Orders Verified", "link" => "/orders/table?pg=&sortdate=&start_date=&end_date=&status=1", "sub" => null],
            ["name" => "Orders Unverified", "link" => "/orders/table?pg=&sortdate=&start_date=&end_date=&status=0", "sub" => null],
            ["name" => "New Order (Widget)", "link" => "/orders/widget/new", "sub" => null],
            ["name" => "Update Price (Widget)", "link" => "/orders/widget/updateprice", "sub" => null],
            ["name" => "Update Qty (Widget)", "link" => "/orders/widget/updateqty", "sub" => null],
            ["name" => "Add Item (Widget)", "link" => "/orders/widget/additem", "sub" => null],
            ["name" => "Checkout", "link" => "/orders/checkout", "sub" => null],
            ["name" => "Submit", "link" => "/orders/submit", "sub" => null],
            ["name" => "Courier List (Widget)", "link" => "/courier/list/widget", "sub" => null],
            ["name" => "Courier Cost", "link" => "/courier/cost?otype=&origin=&destination=&weight=&courier=&order_id=", "sub" => null],
            ["name" => "Set Courier Cost", "link" => "/orders/courier/setcost", "sub" => null],
            ["name" => "Payment Methods (List New)", "link" => "/orders/metodepayment/list-new?courier_id=&type=&comp_id=&oid=", "sub" => null],
            ["name" => "Payment Methods", "link" => "/orders/metodepayment", "sub" => null],
            ["name" => "View Detail", "link" => "/orders/vdetail?id=", "sub" => null],
            ["name" => "Approve Order", "link" => "/orders/approve", "sub" => null]
        ]
    ],
    //prduct
    [
        "name" => "Product",
        "link" => "/product",
        "sub" => [
            ["name" => "Create New Product", "link" => "/product/create?type=new", "sub" => null],
            ["name" => "Add Product Variant", "link" => "/product/variant/add?id=", "sub" => null],
            ["name" => "Get Product Variant", "link" => "/product/variant/get?id=", "sub" => null],
            ["name" => "Get Variant By ID", "link" => "/product/variant/get-varian-byID?variant_id=", "sub" => null],
            ["name" => "Update Variant", "link" => "/product/variant/update?variant_id=", "sub" => null],
            ["name" => "Delete Variant", "link" => "/product/variant/delete?id=", "sub" => null],
            
            
            ["name" => "Edit Product", "link" => "/product/create?type=edit", "sub" => null],
            ["name" => "Show Product", "link" => "/product/show", "sub" => null],
            ["name" => "Delete Product", "link" => "/product/create?type=delete", "sub" => null],
            ["name" => "Product Table", "link" => "/product/table?src=&sort=asc&status=1&pg=1&type=0", "sub" => null],
            ["name" => "Export Product Table", "link" => "/product/table/export", "sub" => null],
            ["name" => "Get Gudang", "link" => "/product/gudang", "sub" => null], // Menambahkan Get Gudang sebelum Create
            [
                "name" => "Product Bulking",
                "link" => "/product/bulking",
                "sub" => [
                    ["name" => "Import Product", "link" => "/product/import", "sub" => null],
                    ["name" => "Product Mapping", "link" => "/product/mapping", "sub" => null],
                    ["name" => "Preview Product", "link" => "/product/preview", "sub" => null],
                    ["name" => "Store Product", "link" => "/product/store", "sub" => null]
                ]
            ]
        ]
    ],
    //report
    [
        "name" => "Report",
        "link" => "/report",
        "sub" => [
            ["name" => "Advertiser Report List", "link" => "/report/list?platform=", "sub" => null],
            ["name" => "Dashboard Product Analysis", "link" => "/dashboard/analysis/getProduct", "sub" => null],
            ["name" => "Advertising Accounts List", "link" => "/advertising_accounts/list", "sub" => null],
            ["name" => "Get Advertising Account ID", "link" => "/advertising_accounts/getID", "sub" => null],
            ["name" => "Create Report", "link" => "/report/create", "sub" => null],
            ["name" => "Update Report", "link" => "/report/update", "sub" => null],
            ["name" => "Delete Report", "link" => "/report/delete", "sub" => null],
            [
                "name" => "Report Metriks",
                "link" => "/report/matrix",
                "sub" => [
                    ["name" => "Table", "link" => "/report/matrix/table", "sub" => null],
                    ["name" => "Get Platform", "link" => "/report/matrix/getPlatform", "sub" => null],
                    ["name" => "Get Metric", "link" => "/report/matrix/getMetric", "sub" => null],
                    ["name" => "Create", "link" => "/report/matrix/create", "sub" => null],
                    ["name" => "Get ID", "link" => "/report/matrix/getid", "sub" => null],
                    ["name" => "Update", "link" => "/report/matrix/update", "sub" => null],
                    ["name" => "Delete", "link" => "/report/matrix/delete", "sub" => null],
                    ["name" => "Delete All Metric", "link" => "/report/matrix/delete-all-metric", "sub" => null]
                ]
            ]
        ]
    ],
    //customers
    [
        "name" => "Customers",
        "link" => "/customers",
        "sub" => [
            ["name" => "Export Customers Table", "link" => "/customers/table/export", "sub" => null],
            ["name" => "Customers Table", "link" => "/customers/table?src=&pg=1&sort=desc&start_date=&end_date=", "sub" => null],
            ["name" => "Get Segment", "link" => "/customers/segment", "sub" => null],
            ["name" => "Filter Segment", "link" => "/customers/segment-filter?pg=1&sort=desc&segment_name=", "sub" => null],
            ["name" => "Get Lokasi", "link" => "/customers/lokasi", "sub" => null],
            ["name" => "Filter Lokasi", "link" => "/customers/lokasi-filter?pg=1&sort=desc&kecamatan_name=", "sub" => null],
            ["name" => "Add Customer", "link" => "/customers/manage/add?type=add", "sub" => null],
            ["name" => "View/Edit Customer", "link" => "/customers/vs-edit", "sub" => null],
            ["name" => "Edit Customer", "link" => "/customers/manage/add?type=edit", "sub" => null],
            ["name" => "Delete Customer", "link" => "/customers/delete", "sub" => null],
            ["name" => "Customer Detail", "link" => "/customers/detail", "sub" => null],
            [
                "name" => "Customers Bulking",
                "link" => "/customers/bulking",
                "sub" => [
                    ["name" => "Import", "link" => "/customers/import", "sub" => null],
                    ["name" => "Mapping", "link" => "/customers/mapping", "sub" => null],
                    ["name" => "Preview", "link" => "/customers/preview", "sub" => null],
                    ["name" => "Store", "link" => "/customers/store", "sub" => null]
                ]
            ],
            [
                "name" => "Customers Segmentation",
                "link" => "/dashboard/rfm-analysis/segment?groupBy=All",
                "sub" => null
            ],
            [
                "name" => "Cohort Analysis",
                "link" => "/cohort",
                "sub" => [
                    ["name" => "Cohort Analysis (Date Range)", "link" => "/dashboard/cohort-analysis?start=&end=", "sub" => null],
                    ["name" => "Cohort Analysis (Product)", "link" => "/dashboard/cohort-analysis?start=&end=&product=", "sub" => null]
                ]
            ]
        ]
    ],
        // CS-Broadcast
    [
        "name" => "CS-Broadcast",
        "link" => "/cs/whatsapp",
        "sub" => [
            ["name" => "Get Phone", "link" => "/cs/whatsapp?act=ripit_cs_getPhone", "sub" => null],
            ["name" => "Get Tags", "link" => "/cs/whatsapp?act=ripit_cs_getTags", "sub" => null],
            ["name" => "Get Segment", "link" => "/cs/whatsapp?act=ripit_cs_getSegment", "sub" => null],
            ["name" => "Broadcast Add", "link" => "/cs/whatsapp?filter_type=&filter_name=&act=ripit_bc_add", "sub" => null],
            ["name" => "Broadcast Get", "link" => "/cs/whatsapp?act=ripit_bc_get", "sub" => null],
            ["name" => "Broadcast edit", "link" => "/cs/whatsapp?act=ripit_bc_edit", "sub" => null],
            ["name" => "Broadcast update", "link" => "/cs/whatsapp?act=ripit_bc_update", "sub" => null],
            ["name" => "Broadcast Detail", "link" => "/cs/whatsapp?act=ripit_bc_detail", "sub" => null],
            ["name" => "Broadcast Delete", "link" => "/cs/whatsapp?act=ripit_bc_delete", "sub" => null],
            ["name" => "Broadcast Preview SpinText", "link" => "/cs/whatsapp?act=ripit_spintext_preview&more=1", "sub" => null],
            ["name" => "Broadcast Generate SpinText AI", "link" => "/cs/whatsapp?act=ripit_spintext_openai", "sub" => null],
            
        ]
    ],

  // Customer Service
    [
        "name" => "Customer Service",
        "link" => "/cs/whatsapp",
        "sub" => [
            ["name" => "CS Setting", "link" => "/cs/whatsapp?act=ripit_cs_setting", "sub" => null],
            ["name" => "Add CS", "link" => "/cs/whatsapp?act=ripit_cs_add", "sub" => null],
            ["name" => "CS Status", "link" => "/cs/whatsapp?act=ripit_cs_status", "sub" => null],
            ["name" => "Delete CS", "link" => "/cs/whatsapp?act=ripit_cs_delete", "sub" => null],
            ["name" => "CS Pairing", "link" => "/cs/whatsapp?act=ripit_cs_pairing", "sub" => null],
            ["name" => "CS Reload", "link" => "/cs/whatsapp?act=ripit_cs_reload", "sub" => null],
            ["name" => "Get CS", "link" => "/cs/whatsapp?act=ripit_cs_get", "sub" => null],
            ["name" => "Get CS Detail", "link" => "/cs/whatsapp?act=ripit_cs_get_detail", "sub" => null],
            ["name" => "Edit CS", "link" => "/cs/whatsapp?act=ripit_cs_edit", "sub" => null]
        ]
    ],

    //bussiness
    [
        "name" => "Business",
        "link" => "/business",
        "sub" => [
            ["name" => "Edit Business", "link" => "/business/edit", "sub" => null],
            ["name" => "Save Business", "link" => "/business/save", "sub" => null],
            ["name" => "Update VAT", "link" => "/business/update-vat", "sub" => null]
        ]
    ],
    //integation
    [
        "name" => "Integration",
        "link" => "/integration",
        "sub" => [
            ["name" => "Endpoint", "link" => $endpoint . $param . "&public_key=", "sub" => null],
            ["name" => "Get Public Token", "link" => "/project/get-public-token", "sub" => null],
            ["name" => "Generate Token", "link" => "/project/generate-token", "sub" => null],
            ["name" => "Send Orders", "link" => "/project/send-orders", "sub" => null]
        ]
    ],
    //gudang
    [
        "name" => "Gudang",
        "link" => "/project/gudang",
        "sub" => [
            ["name" => "Create Gudang", "link" => "/project/gudang/create", "sub" => null],
            ["name" => "Get Gudang ID", "link" => "/project/gudang/getID", "sub" => null],
            ["name" => "Update Gudang", "link" => "/project/gudang/update", "sub" => null],
            ["name" => "Delete Gudang", "link" => "/project/gudang/delete", "sub" => null]
        ]
    ],
      // Discount
    [
        "name" => "Discount",
        "link" => "/discount",
        "sub" => [
            ["name" => "Discount List", "link" => "/discount/list", "sub" => null],
            ["name" => "Discount Status", "link" => "/discount/status", "sub" => null],
            ["name" => "Create Discount", "link" => "/discount/create", "sub" => null]
        ]
    ],
    //get project
    [
        "name" => "MasterApi",
        "link" => "https://cloud-m.ripit.id/api/auth/project",
        "sub" => [
            ["name" => "Get Provinsi", "link" => "https://cloud-m.ripit.id/api/auth/project/getProvinsi", "sub" => null],
            ["name" => "Get City", "link" => "https://cloud-m.ripit.id/api/auth/project/getCity", "sub" => null],
            ["name" => "Get Kecamatan", "link" => "https://cloud-m.ripit.id/api/auth/project/getKecamatan", "sub" => null],
            ["name" => "Update Address", "link" => "https://cloud-m.ripit.id/api/auth/project/updateAddress", "sub" => null],
            ["name" => "Create Project", "link" => "https://cloud-m.ripit.id/api/auth/project", "sub" => null]
        ]
    ],
    //Manage project
    [
    "name" => "Manage Project",
    "link" => "https://cloud-m.ripit.id/api/auth/project/table",
    "sub" => [
        ["name" => "Get Project by ID", "link" => "https://cloud-m.ripit.id/api/auth/project/edit?id={id}", "sub" => null],
        ["name" => "Update Project", "link" => "https://cloud-m.ripit.id/api/auth/project/update?id={id}", "sub" => null],
        ["name" => "Delete Project", "link" => "https://cloud-m.ripit.id/api/auth/project/delete?id={id}", "sub" => null]
    ]
],
    //TIM
    [
        "name" => "TIM",
        "link" => "https://cloud-m.ripit.id/api/auth/pengguna",
        "sub" => [
            ["name" => "Get Role", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/get-role", "sub" => null],
            ["name" => "Create Role", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/create-role", "sub" => null],
            ["name" => "Pengguna Page 1", "link" => "https://cloud-m.ripit.id/api/auth/pengguna?page=1&src=", "sub" => null],
            ["name" => "Edit Pengguna ID ", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/{id}/edit", "sub" => null],
            ["name" => "Update Pengguna ", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/{id}", "sub" => null],
            ["name" => "Delete Pengguna ", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/delete/{id}", "sub" => null],
            ["name" => "Setting Pengguna ", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/getMenus/{id}", "sub" => null],
            ["name" => "Add Menu Pengguna ", "link" => "https://cloud-m.ripit.id/api/auth/pengguna/addMenus/{id}", "sub" => null]
        ]
    ],
    [
        "name" => "Billing",
        "link" => "/billing",
        "sub" => [
            ["name" => "Billing Table", "link" => "https://cloud-m.ripit.id/api/auth/billing?page=1", "sub" => null],
            ["name" => "Billing Get Plan", "link" => "https://cloud-m.ripit.id/api/auth/billing/getSubscription-plan", "sub" => null],
            ["name" => "Billing Upgrade Plan", "link" => "https://cloud-m.ripit.id/api/auth/billing/upgrade-plan", "sub" => null],
            ["name" => "Billing Upgrade Download", "link" => "https://cloud-m.ripit.id/api/auth/billing/upgrade-plan/download", "sub" => null],
            ["name" => "Billing Order Plan", "link" => "https://cloud-m.ripit.id/api/auth/billing/order-plan", "sub" => null],
            ["name" => "Billing Download Invoice", "link" => "https://cloud-m.ripit.id/api/auth/billing/download", "sub" => null],
        ]
    ],
          // Discount
    [
        "name" => "Bank",
        "link" => "/bank",
        "sub" => [
            ["name" => "Bank Table", "link" => "/bank/table?src&pg=1", "sub" => null],
            ["name" => "Bank Create", "link" => "/bank/create", "sub" => null],
            ["name" => "Bank GetID", "link" => "/bank/getID", "sub" => null],
            ["name" => "Bank Update", "link" => "/bank/update", "sub" => null],
            ["name" => "Bank Delete", "link" => "/bank/delete", "sub" => null],
        ]
    ],
    [
        "name" => "Metode Payment",
        "link" => "/metodepayment",
        "sub" => [
            ["name" => "Metode Payment Table", "link" => "/metodepayment/table?src&pg=1", "sub" => null],
            ["name" => "Metode Payment Create", "link" => "/metodepayment/create", "sub" => null],
            ["name" => "Metode Payment GetID", "link" => "/metodepayment/getID", "sub" => null],
            ["name" => "Metode Payment Update", "link" => "/metodepayment/update", "sub" => null],
            ["name" => "Metode Payment Delete", "link" => "/metodepayment/delete", "sub" => null],
        ]
    ],
    [
        "name" => "Notifications",
        "link" => "/notification",
        "sub" => [
            ["name" => "Notifications Table", "link" => "/notifications/get?sort=", "sub" => null],
            ["name" => "Notifications Read", "link" => "/notifications/read", "sub" => null],
            ["name" => "Notifications Delete", "link" => "/notifications/delete", "sub" => null],
        ]
    ],
    [
        "name" => "Loyalty Setting",
        "link" => "/loyalty-setting",
        "sub" => [
            ["name" => "Loyalty Setting Table", "link" => "/project/table-loyalty-rules?pg=1&src=", "sub" => null],
            ["name" => "Loyalty Setting Create", "link" => "/project/create-loyalty-rules", "sub" => null],
            ["name" => "Loyalty Setting GetID", "link" => "/project/getID-loyalty-rules", "sub" => null],
            ["name" => "Loyalty Setting Update", "link" => "/project/update-loyalty-rules", "sub" => null],
            ["name" => "Loyalty Setting Delete", "link" => "/project/delete-loyalty-rules", "sub" => null],
        ]
    ],
    [
        "name" => "Logs",
        "link" => "/logs",
        "sub" => [
            ["name" => "Logs Table", "link" => "/logg/table?src=&pg=1&sort=desc&type=", "sub" => null],
            ["name" => "Logs Detail", "link" => "/logg/detail?id=", "sub" => null],
            ["name" => "Logs Delete", "link" => "/logg/delete?id=", "sub" => null],
        ]
    ],
    [
        "name" => "Automations",
        "link" => "/automations",
        "sub" => [
            ["name" => "Automations Table", "link" => "/automations/list?pg=1&sortdate=desc&start_date=&end_date=&src=", "sub" => null],
            ["name" => "Get Phone", "link" => "/cs/whatsapp?act=ripit_cs_getPhone", "sub" => null],
            // ["name" => "Get Tags", "link" => "/cs/whatsapp?act=ripit_cs_getTags", "sub" => null],
            // ["name" => "Get Segment", "link" => "/cs/whatsapp?act=ripit_cs_getSegment", "sub" => null],
            // ["name" => "Get Loyalty", "link" => "/automations/get-loyalty", "sub" => null],
            ["name" => "Get Condition Type", "link" => "/automations/get-condition-type", "sub" => null],
            ["name" => "Get Condition Value", "link" => "/automations/get-condition-value?condition_type=&src=", "sub" => null],
            ["name" => "Automations Create", "link" => "/automations/create", "sub" => null],
            ["name" => "Automations GetByID", "link" => "/automations/getbyid?id=", "sub" => null],
            ["name" => "Automations Update", "link" => "/automations/update?id=", "sub" => null],
            ["name" => "Automations Delete", "link" => "/automations/delete?id=", "sub" => null],
            ["name" => "Automations Create Condition", "link" => "/automations/create-condition", "sub" => null],
            ["name" => "Automations Update Condition", "link" => "/automations/update-condition", "sub" => null],
            ["name" => "Automations Delete Condition", "link" => "/automations/delete-condition", "sub" => null],
            ["name" => "Automations History", "link" => "/automations/history", "sub" => null],
            ["name" => "Automations Preview SpinText", "link" => "/cs/whatsapp?act=ripit_spintext_preview&more=1", "sub" => null],
            ["name" => "Automations Generate SpinText AI", "link" => "/cs/whatsapp?act=ripit_spintext_openai", "sub" => null],
        
        ]
    ],
    [
        "name" => "Customers Tags",
        "link" => "/customers-tags",
        "sub" => [
            ["name" => "Customers Tags Table", "link" => "/customers/tag/table?src=&pg=1&sort=desc&start_date=&end_date=", "sub" => null],
            ["name" => "Customers Tags Create", "link" => "/customers/tag/create", "sub" => null],
            ["name" => "Customers Tags GetByID", "link" => "/customers/tag/getbyid?id=", "sub" => null],
            ["name" => "Customers Tags Update", "link" => "/customers/tag/update?id=", "sub" => null],
            ["name" => "Customers Tags Delete", "link" => "/customers/tag/delete?id=", "sub" => null],
        ]
    ],
     [
        "name" => "Setting OpenAI",
        "link" => "/setting-openai",
        "sub" => [
            ["name" => "Get Setting Open AI", "link" => "/project/get-open-ai", "sub" => null],
            ["name" => "Update Setting Open AI", "link" => "/project/update-open-ai", "sub" => null],
        ]
    ],
    
    
];            
  $projectData = array_merge($data_project, ['address' => $address, 'menu' => $defaultMenu]);
        // $projectData = array_merge($data_project, ['address' => $address, 'menu' => $this->generateMenuOwner($user_id)['menu']]);
        }
elseif($role_id == 2){
            
        $advertiserMenu = [
     [
        "name" => "Dashboard",
        "link" => "/dashboard",
        "sub" => [
            [
                "name" => "Get Product",
                "link" => "/dashboard/analysis/getProduct",
                "sub" => null
            ],
            [
                "name" => "Get Timeframe",
                "link" => "/dashboard/analysis/getTimeframe",
                "sub" => null
            ],
            [
                "name" => "Get Province",
                "link" => "/dashboard/analysis/getProvince",
                "sub" => null
            ],
            [
                "name" => "Analysis",
                "link" => "/dashboard/analysis?timeframe=&startDate=&endDate=&product=All&province=All",
                "sub" => null
            ]
        ]
    ],
    //report
    [
        "name" => "Report",
        "link" => "/report",
        "sub" => [
            ["name" => "Advertiser Report List", "link" => "/report/list?platform=", "sub" => null],
            ["name" => "Dashboard Product Analysis", "link" => "/dashboard/analysis/getProduct", "sub" => null],
            ["name" => "Advertising Accounts List", "link" => "/advertising_accounts/list", "sub" => null],
            ["name" => "Get Advertising Account ID", "link" => "/advertising_accounts/getID", "sub" => null],
            ["name" => "Create Report", "link" => "/report/create", "sub" => null],
            ["name" => "Update Report", "link" => "/report/update", "sub" => null],
            ["name" => "Delete Report", "link" => "/report/delete", "sub" => null],
            [
                "name" => "Report Metriks",
                "link" => "/report/matrix",
                "sub" => [
                    ["name" => "Table", "link" => "/report/matrix/table", "sub" => null],
                    ["name" => "Get Platform", "link" => "/report/matrix/getPlatform", "sub" => null],
                    ["name" => "Get Metric", "link" => "/report/matrix/getMetric", "sub" => null],
                    ["name" => "Create", "link" => "/report/matrix/create", "sub" => null],
                    ["name" => "Get ID", "link" => "/report/matrix/getid", "sub" => null],
                    ["name" => "Update", "link" => "/report/matrix/update", "sub" => null],
                    ["name" => "Delete", "link" => "/report/matrix/delete", "sub" => null]
                ]
            ]
        ]
    ],
    [
        "name" => "Logs",
        "link" => "/logs",
        "sub" => [
            ["name" => "Logs Table", "link" => "/logg/table?src=&pg=1&sort=desc&type=", "sub" => null],
            ["name" => "Logs Detail", "link" => "/logg/detail?id=", "sub" => null],
            ["name" => "Logs Delete", "link" => "/logg/delete?id=", "sub" => null],
        ]
    ],

];            
        $projectData = array_merge($data_project, ['address' => $address, 'menu' => $advertiserMenu]);        
        }
elseif($role_id == 4){
$customerServiceMenu = $this->getUserMenu($user_id);
       
        $projectData = array_merge($data_project, ['address' => $address, 'menu' => $customerServiceMenu]);
        
        }
else{
        $Not_akses = [
    //customers service
    [
        "name" => "Tidak ada",
        "link" => "",
        "sub" => null
    ]
];   
        $projectData = array_merge($data_project, ['address' => $address, 'menu' => $Not_akses]);
        }
        $results[] = $projectData;
    }
  
    //update kolom usage table users
    //cek paket_id
    $paket_id = User::where('id',$user_id)->first()->paket_id;
    if($paket_id == 1 || $paket_id == 2 || $paket_id == 3 ){
        
    $usagePaket = json_decode($user->usage_paket, true);
    // If the column is empty or not valid JSON, initialize it as an empty array
    if (!is_array($usagePaket)) {
        $usagePaket = [];
    }

    // Update the "Products" feature's usage
    foreach ($usagePaket as &$feature) {
        if ($feature['fitur'] === 'Products') {
            $feature['usage'] = $total_keseluruhan;
        }
    
        if ($feature['fitur'] === 'WhatsApp Device') {
            $feature['usage'] = $total_device_keseluruhan;
        }
    }

    // Re-encode the modified usage_paket JSON
        $updatedUsagePaket = json_encode($usagePaket);
        DB::table('users')->where('id', $user_id)->update(['usage_paket' => $updatedUsagePaket]);
    }


    
    
    return response()->json([
        'code' => 1,
        'message' => 'Proyek berhasil diambil.',
        'result' => $results
    ], 200);
    
    

    
}

public function getProvinsi(Request $request): JsonResponse
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    // Mengambil user beserta project yang terkait
    $user = User::with('projects')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Cek role_id user
    if ($user->role_id !== 1) {
        return response()->json([
            'code' => 0,
            'message' => 'Akses tidak diizinkan.',
            'result' => []
        ], 403);
    }

    // Jika role_id adalah 1, lanjutkan dengan POST request ke klien
    $data_post = [
        "token" => $token
        ];   
    $endpoint = 'https://' . $user->projects[0]['endpoint'] . '/api/data/provinsi' ;

    $response = Http::post($endpoint, $data_post);
    $responseData = $response->json();
    return response()->json([
        $responseData
    ], 200);
}

//get city
public function getCity(Request $request): JsonResponse
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    $provinsi_id = $request->provinsi_id;
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;

    // Mengambil user beserta project yang terkait
    $user = User::with('projects')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Cek role_id user
    if ($user->role_id !== 1) {
        return response()->json([
            'code' => 0,
            'message' => 'Akses tidak diizinkan.',
            'result' => []
        ], 403);
    }

    // Jika role_id adalah 1, lanjutkan dengan POST request ke klien
    $data_post = [
        "token" => $token,
        "provinsi_id" => $provinsi_id,
        ];   
    $endpoint = 'https://' . $user->projects[0]['endpoint'] . '/api/data/cities' ;
    $response = Http::post($endpoint, $data_post);
    $responseData = $response->json();
    return response()->json([
        $responseData
    ], 200);
}
//
public function getKecamatan(Request $request): JsonResponse
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    $city_id = $request->city_id;
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;

    // Mengambil user beserta project yang terkait
    $user = User::with('projects')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Cek role_id user
    if ($user->role_id !== 1) {
        return response()->json([
            'code' => 0,
            'message' => 'Akses tidak diizinkan.',
            'result' => []
        ], 403);
    }

    // Jika role_id adalah 1, lanjutkan dengan POST request ke klien
    $data_post = [
        "token" => $token,
        "city_id" => $city_id,
        ];   
    $endpoint = 'https://' . $user->projects[0]['endpoint'] . '/api/data/kecamatans' ;
    $response = Http::post($endpoint, $data_post);
    $responseData = $response->json();
    return response()->json([
        $responseData
    ], 200);
}
public function updateAddress(Request $request): JsonResponse
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    $provinsi_id = $request->provinsi_id;
    $city_id = $request->city_id;
    $kecamatan_id = $request->kecamatan_id;
    $kode_pos = $request->kode_pos;
    $address = $request->address;
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;

    // Mengambil user beserta project yang terkait
    $user = User::with('projects')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Cek role_id user
    if ($user->role_id !== 1) {
        return response()->json([
            'code' => 0,
            'message' => 'Akses tidak diizinkan.',
            'result' => []
        ], 403);
    }

    // Jika role_id adalah 1, lanjutkan dengan POST request ke klien
    $data_post = [
        "token" => $token,
        "project_name" =>  $user->projects[0]['project_name'],
        "provinsi_id" => $provinsi_id,
        "kecamatan_id" => $kecamatan_id,
        "city_id" => $city_id,
        "address" => $address,
        "kode_pos" => $kode_pos,
        ];   
    $endpoint = 'https://' . $user->projects[0]['endpoint'] . '/api/project/address/update' ;
    $response = Http::post($endpoint, $data_post);
 
    $responseData = $response->json();
 
    return response()->json([
        $responseData
    ], 200);
}


public function invite(Request $request): JsonResponse
{
        $token = $request->bearerToken();
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    // Mengambil user beserta project yang terkait
    $user = User::with('projects')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Melakukan enkripsi pada project_key dan user_key jika projects tidak null
    if ($user->projects) {
        $user->projects->each(function($project) {
            $project->project_key = bin2hex($project->project_key);
        });
    }

$dummyData = [
    'projectName' => $user->projects[0]['project_name'],
    'approveLink' => url('/api/project/approve?email=' . $request->email . '&project_key=' . $user->projects[0]['project_key'])
];


    // // Render view ke dalam variabel string
    $emailContent = View::make('emails.invite', $dummyData)->render();

    // Data untuk pengiriman email melalui cURL
    $post = array(
        'from' => '<EMAIL>', // Email pengirim
        'to' => $request->email, // Email penerima, bisa diambil dari $request->input('email') atau lainnya
        'subject' => 'Undangan Proyek', // Subjek email
        'html' => $emailContent // Mengirim konten email sebagai HTML
    );

    // Inisialisasi cURL
    $ch = curl_init();

    // Set URL API
    curl_setopt($ch, CURLOPT_URL, 'https://aplikasi.kirim.email/api/v3/transactional/messages');

    // Set opsi cURL
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post);

    // Set autentikasi API
    curl_setopt($ch, CURLOPT_USERPWD, 'api:f4971596d9ec231ff047efea01c5715d7dbc5234fc9087ed52fc0c21a44d5817');

    // Set header tambahan jika diperlukan
    $headers = array();
    $headers[] = 'Domain: kirim.ripit.id';
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    // Eksekusi cURL
    $result = curl_exec($ch);

    // Error handling
    if (curl_errno($ch)) {
        return response()->json(['message' => 'Failed to send email: ' . curl_error($ch)], 500);
    }

    curl_close($ch);

    // Return response
    return response()->json(['message' => 'Email sent successfully', 'response' => $result], 200);
}

public function template(){
    $dummyData = [
    'projectName' => 'tokokelontong',
    'email' => '<EMAIL>',
    'password' => 'testing'
    
];

    return view('emails.invite', $dummyData);
}

public function template_order(){
    $dummyData = [
    'package' => 'Enterprise',
    'email' => '<EMAIL>',
    'password' => 'testing'
    
];

    return view('emails.order', $dummyData);
}



public function approve(Request $request)
{
    // Validasi parameter query string
    $validator = Validator::make($request->all(), [
        'email' => 'required|email',
        'project_key' => 'required|string',
    ]);

    // Jika validasi gagal
    if ($validator->fails()) {
        return view('approve', [
            'title' => 'Validation Failed',
            'message' => 'Validation failed',
            'result' => $validator->errors()
        ]);
    }

    // Mengambil email dari parameter query string
    $email = $request->query('email');

    // Cari user berdasarkan email
    $user = DB::table('users')->where('email', $email)->first();

    if (!$user) {
        return view('approve', [
            'title' => 'Error',
            'message' => 'User not found.',
            'result' => []
        ]);
    }

    // Dapatkan dan konversi project_key dari request
    $projectKey = $request->query('project_key');
    $binaryProjectKey = hex2bin($projectKey);

    // Cek apakah project_key ada di database
    $project = DB::table('projects')->where('project_key', $binaryProjectKey)->first();

    if (!$project) {
        return view('approve', [
            'title' => 'Error',
            'message' => 'Project not found',
            'result' => []
        ]);
    }

    // Cek apakah kombinasi user_id dan project_id sudah ada di tabel user_projects
    $existingUserProject = DB::table('user_projects')
        ->where('user_id', $user->id)
        ->where('project_id', $project->id)
        ->first();

    if ($existingUserProject) {
        // Jika sudah terhubung, kembalikan data proyek yang ada
        return view('approve', [
            'title' => 'Sudah Terhubung',
            'message' => "Anda sudah terhubung dengan proyek {$project->project_name}.",
            'result' => [
                'project_key' => $projectKey,
                'project_name' => $project->project_name,
                'endpoint' => $project->endpoint,
            ]
        ]);
    }

    // Masukkan data ke dalam tabel user_projects jika belum ada
    DB::table('user_projects')->insert([
        'user_id' => $user->id,
        'project_id' => $project->id,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    // Kembalikan response sukses dengan data proyek
    return view('approve', [
        'title' => 'Sukses',
        'message' => "Selamat anda sudah terhubung dengan proyek {$project->project_name}",
        'result' => [
            'project_key' => $projectKey,
            'project_name' => $project->project_name,
            'endpoint' => $project->endpoint,
        ]
    ]);
}


        //
  

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): JsonResponse
    {

         return $this->sendResponse($request->all(), 'Project retrieved successfully.');
    }
    

    /**
     * Store a newly created resource in storage
     */
     
    public function get_dns(Request $request): JsonResponse
    {
        // $cloudFlare = new CloudflareModel();
        // $get = $cloudFlare->getDNS();
        $siteName = '2client.ripit.id';

        $btApi = new BtApi();
    $response = $btApi->set_site_directory($siteName);
    return response()->json($response);
        
    }
    
    private function dnsID(){
            $cloudFlare = new CloudflareModel();
                 $get = $cloudFlare->getDNS(); // Mengambil data dari Cloudflare API

// Ambil data dari response JSON
$data = $get['result'];

// Inisialisasi array untuk menyimpan angka dari 'name'
$dnsIds = [];

// Loop melalui data dan ambil angka dari 'name'
foreach ($data as $dns) {
    if (preg_match('/(\d+)client/', $dns['name'], $matches)) {
        $dnsIds[] = (int)$matches[1]; // Ambil angka dan ubah ke integer
    }
}

$maxDnsId = !empty($dnsIds) ? max($dnsIds) : null;
return $maxDnsId;
    }
    
    public function remote(Request $request): JsonResponse
    {
    
    $command = $request->command;       
    $btApi = new BtApi();
    $cloudFlare = new CloudflareModel();
    
    // 1. get_cron
    // 2. addDNS($recordType, $recordName, $recordContent, $ttl = 1, $proxied = true)
    // 4. execute_cron_by_name('duplicate db', $db_name);
    // 5. getDNS
    // 6. get_list_site
    if($command == 1){


     $get = $btApi->get_performance(); // Mengambil data dari Cloudflare API
   return response()->json($get);

        // code...
    }elseif ( $command == 2) {
            $recordType = 'A'; // Misalnya tipe A record
    $recordContent = '***************';
    // $recordID = 'b8ed1971626d5aa72587b098250e5144';
    $recordName = 'aiagent.gass.web.id';
    // $add_domain = $btApi->add_domain_insite($recordName);
                // Jika situs berhasil ditambahkan, buat DNS di Cloudflare
            // Tambahkan DNS di Cloudflare
            $dnsAdded = $cloudFlare->addDNS($recordType, $recordName, $recordContent);
        
        return response()->json($dnsAdded);
        // code...
 }elseif ( $command == 3) {
        // code...
 }elseif ($command == 4) {
                        //  $db_name = "2_ripit";
                        // $db_user ="2_ripit";
                        // $db_password = "2_ripit123123123";
                        // $add_database = $btApi->add_db($db_name, $db_name, $db_password);
    //  $db_name = '2_ripit';
     $exec_env = $btApi->delete_db('33_ripit');
     return response()->json($exec_env);
     // code...
 }elseif ($command == 41) {
$siteName = '2client.ripit.id';
$duplicate_project = $btApi->execute_cron_by_name('duplicate project', $siteName);
     return response()->json($duplicate_project);

     // code...
 }
 
 elseif ($command == 5) {
    //   $get = $cloudFlare->getDNS();
     $recordType = 'A'; // Misalnya tipe A record
    $recordContent = '***************';
    $recordID = 'a58fe3818b9f14f6f4b468e52e7df04c';
    $recordName = 'aiagent.ripit.id';
    $delete = $cloudFlare->deleteDNS($recordID, $recordType, $recordName, $recordContent);
    // $add_domain = $btApi->add_domain_insite($recordName);
                // Jika situs berhasil ditambahkan, buat DNS di Cloudflare
            // Tambahkan DNS di Cloudflare
            // $dnsAdded = $cloudFlare->addDNS($recordType, $recordName, $recordContent);
        
        return response()->json($delete);
        
        
     // code...
 }elseif ($command == 6 ) {
     // code...
       $get = $btApi->get_list_site();
        return response()->json($get);
 }
    
    
    else {
        $domain_new = $btApi->update_running_directory_to_public('2client.ripit.id');
        return response()->json($domain_new);
        // code...
    }
    
    
    
    
        
        
        
    }

  
  
  //new
  
      public function store(Request $request): JsonResponse
    {

        $token = $request->bearerToken();
        // Menghasilkan hash dari token menggunakan SHA-256
        $tokenHash = hash("sha256", $token, true);
        // Cari user berdasarkan token hash
        $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
        if (!$userToken) {
            return response()->json([
                'code' => 0,
                'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
                'result' => []
            ], 404);
        }

        $user_id = $userToken->user_id;




        $btApi = new BtApi();
        $cloudFlare = new CloudflareModel();
        // Validasi data request
        $validator = \Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'business_type' => 'required|string|max:255',

        ]);
        // Jika validasi gagal
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'data' => null,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        // $project_id = $this->dnsID() + 1;




        // Generate a random project key dan konversi ke binary
        $projectKey = random_bytes(16);

        // Siapkan data untuk disimpan
        $data_project = [
            // 'id'          => $project_id,
            'project_key' => $projectKey, // Simpan sebagai data biner langsung
            'project_name' => $request->input('business_name'),
            'project_type' => $request->input('business_type'),
            'endpoint' => '',  // Temporary value, will be updated later
        ];
        $get_dns = $cloudFlare->getDNS();



        $project = Project::create($data_project);

        DB::table('profile')
            ->where('user_id', $user_id)   // Find the profile by user_id
            ->update(['nama_lengkap' => $request->input('last_name')]); // Update nama_lengkap

        DB::table('users')
            ->where('id', $user_id)   // Find the profile by user_id
            ->update(['name' => $request->input('first_name')]); // Update nama_lengkap
        //cek paket_id
        $cek_paket = DB::table('users')->where('id', $user_id)->first()->paket_id;
        if($cek_paket == null){
        // $paket_1 = 
        $detail_paket = PaketPackage::where('id', 1)->first()->detail;
            DB::table('users')
            ->where('id', $user_id)   // Find the profile by user_id
            ->update([
                'paket_id' => 1,
                'usage_paket' => $detail_paket
            ]);
            
        }

        if ($project) {
            $project_id = Project::latest('id')->first()->id ;
            // Set endpoint dengan ID project yang baru dibuat
            $project->endpoint = "{$project_id}client.ripit.id";
            $project->save();

            DB::table('user_projects')->insert([
                'user_id' => $user_id,
                'project_id' => $project->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            // Tambahkan situs
            // $siteName = "{$project_id}client.ripit.id"; // Nama situs yang ingin Anda tambahkan
            // // Cek DNS untuk memastikan apakah situs sudah ada atau belum
            // $cek_dns = $cloudFlare->cekDNS($siteName);

            // if (empty($cek_dns['result'])) {
            //     // Jika DNS tidak ditemukan, tambahkan situs menggunakan BtApi
            //     // $addSite = $btApi->add_site($siteName);
            //     $addSite = $btApi->add_domain_insite($siteName);
            //     if ($addSite) {
            //         // Jika situs berhasil ditambahkan, buat DNS di Cloudflare
            //         $recordType = 'A'; // Misalnya tipe A record
            //         $recordContent = '***************'; // Misalnya alamat IP situs
            //         // Tambahkan DNS di Cloudflare
            //         $dnsAdded = $cloudFlare->addDNS($recordType, $siteName, $recordContent);
            //         if ($dnsAdded) {
            //             $db_name = "{$project_id}_ripit";
            //             $db_user ="{$project_id}_ripit";
            //             $db_password = "{$project_id}_ripit123123123";
            //             $add_database = $btApi->add_db($db_name, $db_name, $db_password);
            //             if ($add_database) {
            //                 $duplicate_DB = $btApi->execute_cron_by_name('duplicate db');
            //                 if ($duplicate_DB) {
            //                     $duplicate_env = $btApi->execute_cron_by_name('duplicate_env');
            //                     if ($duplicate_env) {
            //                         return response()->json([
            //                             'success' => true,
            //                             'code' => 1,
            //                             'data' => [
            //                                 'project_id' => $project->id,
            //                                 'project_key' => bin2hex($project->project_key),
            //                                 'project_name' => $project->project_name,
            //                                 'endpoint' => $project->endpoint,
            //                             ],
            //                             'data_site' => $addSite,
            //                             'data_dns' => $dnsAdded,
            //                             'add_db' => $add_database,
            //                             'data_db' => $duplicate_DB,
            //                             'data_config' => $duplicate_env,
            //                             'message' => 'Project created successfully, site added, and DNS added.'
            //                         ]);
            //                     } else {
            //                         return response()->json([
            //                             'success' => true,
            //                             'code' => 1,
            //                             'data' => [
            //                                 'project_id' => $project->id,
            //                                 'project_name' => $project->project_name,
            //                                 'endpoint' => $project->endpoint,
            //                             ],
            //                             'data_site' => $addSite,
            //                             'data_dns' => $dnsAdded,
            //                             'add_db' => $add_database,
            //                             'data_db' => $duplicate_DB,
            //                             'data_config' => 'gagal duplicate_config',
            //                             'message' => 'Project created successfully, site added, and DNS added.'
            //                         ]);
            //                     }
            //                 }
            //             } else {
            //                 return response()->json([
            //                     'success' => true,
            //                     'code' => 1,
            //                     'data' => [
            //                         'project_id' => $project->id,
            //                         'project_name' => $project->project_name,
            //                         'endpoint' => $project->endpoint,
            //                     ],
            //                     'data_site' => $addSite,
            //                     'data_dns' => $dnsAdded,
            //                     'data_db' => 'db gagal ditambahkan',
            //                     'message' => 'Project created successfully, site added, and DNS added.'
            //                 ]);
            //             }
            //         } else {
            //             // Jika gagal menambahkan DNS di Cloudflare
            //             return response()->json([
            //                 'success' => false,
            //                 'code' => 0,
            //                 'data' => null,
            //                 'message' => 'Project created successfully and site added, but failed to add DNS.'
            //             ]);
            //         }
            //     } else {
            //         // Jika gagal menambahkan situs menggunakan BtApi
            //         return response()->json([
            //             'success' => false,
            //             'code' => 0,
            //             'data' => null,
            //             'message' => 'Failed to add site using BtApi.'
            //         ]);
            //     }
            // } else {
            //     // Jika DNS sudah ada, Anda dapat menanggapi sesuai kebutuhan aplikasi Anda
            //     return response()->json([
            //         'success' => false,
            //         'code' => 0,
            //         'data' => null,
            //         'message' => 'Site already exists.'
            //     ]);
            // }
            //new
            $db_name = "{$project_id}_ripit";
            $db_user ="{$project_id}_ripit";
            $db_password = "{$project_id}_ripit123123123";
            $add_database = $btApi->add_db($db_name, $db_name, $db_password);
            if ($add_database) {
                 $access_result = $btApi->add_db_access_wildcard($db_name, $db_user, $db_password);
                 Log::info('Grant result', $access_result);
                $duplicate_DB = $btApi->execute_cron_by_name('duplicate db');
                if ($duplicate_DB) {
                     return response()->json([
                            'success' => true,
                            'code' => 1,
                            'data' => [
                                    'project_id' => $project->id,
                                    'project_key' => bin2hex($project->project_key),
                                    'project_name' => $project->project_name,
                                    'endpoint' => $project->endpoint,
                                    ],
                            'add_db' => $add_database,
                            'data_db' => $duplicate_DB,
                            'message' => 'Project created successfully, db added, and db duplicate.' ]);
                    }else{
                         return response()->json([
                            'success' => true,
                            'code' => 1,
                            'data' => [
                                    'project_id' => $project->id,
                                    'project_name' => $project->project_name,
                                    'endpoint' => $project->endpoint,
                                      ],
                            'add_db' => $add_database,
                            'data_db' => 'gagal duplicate db',
                            'message' => 'Project created successfully, DB add, Duplicate Erro.'
                                    ]);
                    }
            }
            else{
            return response()->json([
                    'success' => true,
                    'code' => 1,
                    'data' => [
                            'project_id' => $project->id,
                            'project_name' => $project->project_name,
                            'endpoint' => $project->endpoint,
                            ]
                        ]);
            
            }
            //end new            
        } else {
            return response()->json([
                'success' => false,
                'code' => 0,
                'data' => null,
                'message' => 'Failed to create project.'
            ]);
        }
    }
      

    public function show(string $id)
    {
        //
         return response()->json($id);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request)
    {
           // Mengambil id dari request
    $id = $request->id;

    // Memeriksa apakah id ada di dalam request
    if (!$id) {
        return response()->json([
            'success' => false,
            'message' => 'ID is missing in the request.'
        ], 400);
    }

    // Mencari data project berdasarkan id
    $data = Project::find($id);
    if($data == null){
        return response()->json([
            'message' => 'project tidak ada',
            'code'  => 0,
            'response' => []
            ]);
    }

    // Jika project ditemukan, hanya menampilkan 'project_name'
    return response()->json([
        'message' => 'berhasil mendapatkan data project',
        'code'  => 1,
        'response' => $data->project_name
    ]);
    }

    /**
     * Update the specified resource in storage.
     */
public function update(Request $request)
{
    // Mengambil id dari request
    $id = $request->id;

    // Memeriksa apakah id ada di dalam request
    if (!$id) {
        return response()->json([
            'success' => false,
            'message' => 'ID is missing in the request.'
        ]);
    }

    // Mencari data project berdasarkan id
    $data = Project::find($id);
    
    // Jika project tidak ditemukan
    if ($data == null) {
        return response()->json([
            'message' => 'Project tidak ada',
            'code'  => 0,
            'response' => []
        ]);
    }

    // Validasi request untuk project_name
    $validator = \Validator::make($request->all(), [
        'project_name' => 'required|string|max:255'
    ]);

    // Jika validasi gagal, kirimkan pesan error
    if ($validator->fails()) {
        return response()->json([
            'success' => false,
            'message' => 'Validation Error',
            'errors' => $validator->errors()
        ], 400);
    }

    // Jika validasi berhasil, update project_name
    $data->project_name = $request->project_name;
    $data->save();  // Simpan perubahan
    

    // Jika berhasil update, kembalikan respons berhasil
    return response()->json([
        'success' => true,
        'message' => 'Project updated successfully',
        'data' => $data->project_name
    ], 200);
}

public function delete(Request $request)
{
    // Mengambil id dari request
    $id = $request->id;
    $btApi = new BtApi();
    $cloudFlare = new CloudflareModel();
    // Mengambil bearer token dari request
    $token = $request->bearerToken();
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;

    // Mengambil user beserta project yang terkait
    $userProject = DB::table('user_projects')
        ->join('projects', 'user_projects.project_id', '=', 'projects.id')
        ->select('user_projects.*', 'projects.*')
        ->where('user_projects.user_id', $user_id)
        ->where('projects.id', $id) // Assuming id refers to project id
        ->first();

    if ($userProject) {
        
        //  preg_match('/(\d+)/', $userProject->endpoint, $matches);
        // $numericPart = isset($matches[1]) ? $matches[1] : null; // Get the first match
        // $endpoint = $userProject->endpoint;
        $endpoint = 'https://cloud-c.ripit.id/delete_db?' . $userProject->id . '/' .bin2hex($userProject->project_key) ;
        
         $data_post = [
            'data_project' => [
                'project_id' => $userProject->id,
                'project_key' => bin2hex($userProject->project_key),
            ],
            'db' => $id.'_ripit'
        ];
        
        // return response()->json($endpoint);
        
        // $db_name = [
        //      // Capture db name from request
        // ];

        // Endpoint should be defined or passed to this method

        // Send the POST request
        $delete_db = Http::post( $endpoint, $data_post);
        //  return response()->json($delete_db->json());
        // return $delete_db;
        if ($delete_db->successful()) {
        DB::table('user_projects')->where('id', $userProject->id)->delete();
        DB::table('projects')->where('id', $id)->delete();

        return response()->json([
            'code' => 1,
            'message' => 'Project deleted successfully.',
            'result' => [] // Return deleted project data if needed
        ]);
                    
        }else{
            return response()->json([
            'code' => 0,
            'message' => 'Gagal delete project .',
            'result' => []
        ], 404);
        }
  

    } else {
        return response()->json([
            'code' => 0,
            'message' => 'Project tidak ditemukan atau tidak terkait dengan pengguna.',
            'result' => []
        ], 404);
    }
}



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
    public function generateMenuOwner($userId)
{
    $menus = DB::table('menus')->where('status', 1)->get();
    $result = ['menu' => []];

    foreach ($menus as $menu) {
        // Cek dan insert user_menus jika belum ada
        $menuExists = DB::table('user_menus')
            ->where('user_id', $userId)
            ->where('menu_id', $menu->id)
            ->exists();

        if (!$menuExists) {
            DB::table('user_menus')->insert([
                'user_id' => $userId,
                'menu_id' => $menu->id,
                'status' => 1,
            ]);
        }

        // Ambil sub_menus terkait
        $subMenus = DB::table('sub_menus')
            ->where('menu_id', $menu->id)
            ->where('status', 1)
            ->get();

        $subMenuArray = [];

        foreach ($subMenus as $sub) {
            // Cek dan insert user_sub_menus jika belum ada
            $subMenuExists = DB::table('user_sub_menus')
                ->where('user_id', $userId)
                ->where('sub_menu_id', $sub->id)
                ->exists();

            if (!$subMenuExists) {
                DB::table('user_sub_menus')->insert([
                    'user_id' => $userId,
                    'sub_menu_id' => $sub->id,
                    'status' => 1,
                ]);
            }

            $subMenuArray[] = [
                'sub_menu_id' => $sub->id,
                'name' => $sub->name,
                'link' => $sub->link,
                'sub_menu_status' => $sub->status,
            ];
        }

        $result['menu'][] = [
            'menu_id' => $menu->id,
            'name' => $menu->name,
            'menu_status' => $menu->status,
            'link' => $menu->link,
            'sub' => $subMenuArray,
        ];
    }

    return $result;
}

    public function get_package(Request $request): JsonResponse
    
    {
        
        $packages = PaketPackage::all();
        if($packages == null){
            
        return response()->json([
            'message' => 'Data Paket Panel Ripit Kosong', 
            'code' => 0, 
            'result' => null,
        ]);
        }

        $packages->transform(function ($package) {
        $package->detail = json_decode($package->detail, true);
            return $package;
        });
        // Format response JSON
        return response()->json([
            'message' => 'Data paket panel ripit berhasil didapatkan',
            'code' => 1, // Kode sukses, bisa diubah sesuai kebutuhan (misal 1 untuk sukses)
            'result' => $packages, // Hasil data dari tabel paket_package
        ]);

        
    }
    
      //use voucher
public function order_package_voucher(Request $request): JsonResponse
{
    // Validasi request
    $validator = Validator::make($request->all(), [
        'paket_id' => 'required|integer',
        'voucher_code' => 'required|string',
        'paket_type' => 'required|string|in:monthly,6-month,yearly',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $validator->errors(),
        ], 422);
    }

    // Harga berdasarkan paket_type dan paket_id
    $hargaList = [
        'monthly' => [1 => 249000, 2 => 597600, 3 => 933750],
        '6-month' => [1 => 1344600, 2 => 3227040, 3 => 5042250],
        'yearly' => [1 => 2390400, 2 => 5736960, 3 => 8964000],
    ];

    $paketType = $request->paket_type;
    $paketId = $request->paket_id;

    if (!isset($hargaList[$paketType][$paketId])) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Invalid paket_id or paket_type',
        ], 422);
    }

    $nominal = $hargaList[$paketType][$paketId];

    // Cek voucher dari database
    $voucher = VoucherPaketPackage::where('code', $request->voucher_code)->first();

    if (!$voucher) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Voucher tidak ditemukan atau tidak valid',
        ],422) ;
    }

    $nominal = max(0, $nominal - $voucher->nominal); // hindari negatif

    return response()->json([
        'success' => true,
        'code' => 1,
        'message' => 'Berhasil menggunakan voucher',
        'response' => $nominal
    ]);
}
   public function order_package(Request $request): JsonResponse
{
    // Validasi request
    $validator = Validator::make($request->all(), [
        'email' => 'required|email',
        'name' => 'required|string',
        'phone' => 'required|string',
        'paket_id' => 'required|integer',
        'paket_type' => 'required|string|in:monthly,6-month,yearly',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $validator->errors(),
        ], 422);
    }

    // Harga berdasarkan paket_type dan paket_id
    $hargaList = [
        'monthly' => [1 => 249000, 2 => 597600, 3 => 933750],
        '6-month' => [1 => 1344600, 2 => 3227040, 3 => 5042250],
        'yearly' => [1 => 2390400, 2 => 5736960, 3 => 8964000],
    ];
    

    $paketType = $request->paket_type;
    $paketId = $request->paket_id;

    // Validasi kombinasi paket_type dan paket_id
    if (!isset($hargaList[$paketType][$paketId])) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Invalid paket_id or paket_type',
        ], 422);
    }

    $nominal = $hargaList[$paketType][$paketId];
    // voucher 
    $voucherCode = $request->voucher_code;
    $diskon = 0;

    if ($voucherCode) {
        $voucher = VoucherPaketPackage::where('code', $voucherCode)->first();

        if ($voucher) {
            $diskon = $voucher->nominal;
            $nominal = max(0, $nominal - $diskon); // hindari nilai negatif
        } else {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => 'Kode voucher tidak valid.',
            ], 404);
        }
    }
    
    $existingUser = User::where('email', $request->email)->first();
    if ($existingUser) {
        $existingOrder = Order::where('email', $request->email)
            ->where('transaction_status', 'SUCCESS')
            ->first();

        if ($existingOrder) {
             $this->errorService->reportError($request->email. 'User sudah terdaftar dan telah menyelesaikan pembayaran.' );
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => 'User sudah terdaftar dan telah menyelesaikan pembayaran.',
            ], 500);
        }
    }
    
    // ID order baru
    $id = Order::max('id');
    $order_id = $id ? $id + 1 : 1;
    $id_invoice = $order_id;
    //Transaction - dari 10000 
       if ($nominal < 10000) {
           
        $durasiHari = [
            'monthly' => 30,
            '6-month' => 180,
            'yearly' => 365,
        ];
        
        $tanggalApprove = Carbon::now();
        $tanggalExpired = $tanggalApprove->copy()->addDays($durasiHari[$paketType] ?? 30);

        $order = Order::create([
            'order_id' =>  $order_id,
            'invoice' => 'INV-BILL' . date('n-j') . '00' . $id_invoice,
            'paket_id' => $paketId,
            'paket_type' => $paketType,
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'nominal' => $nominal,
            'status' => 'SUCCESS',
            'transaction_status'=> 'SUCCESS',
            'tanggal_approve' => $tanggalApprove,
            'tanggal_expired' => $tanggalExpired,
            'status_paket' => 1,
            'link_url' => '-',
            'link_pay' => '-',
        ]);

        // Auto register user jika belum ada
 $usageMap = [
            1 => [
  ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => "Unlimited"],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => 0],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => "Unlimited"],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "Yes"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
], // isi dengan $paket_id_1
            2 =>[
  ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => "Unlimited"],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => 0],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => "Unlimited"],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "Yes"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
], // isi dengan $paket_id_2
            3 => [
   ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => "Unlimited"],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => "Unlimited"],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => "Unlimited"],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "Yes"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
],
            4 => [
      ["fitur" => "Team CRM", "usage" => "Unlimited"],
      ["fitur" => "Products", "usage" => "Unlimited"],
      ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
      ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
      ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
      ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
      ["fitur" => "Cohort Analysis", "usage" => "Yes"],
      ["fitur" => "RFM Analysis", "usage" => "Yes"],
      ["fitur" => "Segmentasi User", "usage" => "Yes"],
      ["fitur" => "Custom Tags", "usage" => "Unlimited"],
      ["fitur" => "Waba Integrations", "usage" => "Yes"],
      ["fitur" => "WhatsApp Device", "usage" => "Unlimited"],
      ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
      ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
      ["fitur" => "Customize Metric", "usage" => "Unlimited"],
      ["fitur" => "Customer Purchase Journey", "usage" => "Yes"],
      ["fitur" => "Support", "usage" => "Yes"],
      ["fitur" => "VIP Support + Pendampingan", "usage" => "Yes"]
    ],
        ];
            $usage = $usageMap[$paketId] ?? [];

            $appKey = config("app.key");
            $user_key = hex2bin(md5($appKey . ";" . $request->input("email")));
            $passwordview = Str::random(32);
            $pass = hex2bin(hash('sha256', $appKey . ";" . $request->email . ";" . $passwordview));

            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = $pass;
            $user->verification = 1;
            $user->role_id = 1;
            $user->user_key = $user_key;
            $user->paket_id = $paketId;
            $user->usage_paket = json_encode($usage);
            $user->save();

            $user_id = $user->id;
    
            $order->update(['user_id' => $user_id]);
              $get_package_name = PaketPackage::where('id', $paketId)->pluck('name')->first();
            // Pastikan create profile menyertakan user_id
            $profile = new Profile();
            $profile->user_id = $user_id;
            $profile->phone = $request->phone;
            $profile->nama_lengkap = $request->name;
            $profile->save();

            $dummyData = [
                'package' => $get_package_name,
                'email' => $request->email,
                'password' => $passwordview
            ];

            $emailContent = View::make('emails.order', $dummyData)->render();

            // Kirim email
            $to = $request->email;
            $subject = 'Order Plan Ripit.ID';
            $text = 'Selamat anda berhasil order plan di Platform Ripit.id';
            $html = $emailContent;

            $response = $this->sendEmail($to, $subject, $text, $html);
            
            
           $waMessage = "Selamat! Pesanan subscription Anda di Ripit.ID berhasil.\\n\\n"
            . "Paket: *{$get_package_name}*\\n"
            . "Email: {$request->email}\\n\\n"
            . "Password: {$passwordview}\\n\\n"
            . "Terima kasih sudah berlangganan dengan kami.\\n"
            . "Jika ada pertanyaan, jangan ragu menghubungi kami.\\n\\n"
            . "Selamat menggunakan layanan Ripit.ID!";

            $sendwa = [
                'phone' => $request->phone,
                'caption' => $waMessage,
            ];
            
            $result_wa = $this->waService->sendWaToKonekwa($sendwa);

           if ($response == true || $result_wa == true) {
                return response()->json([
                    'code' => 1,
                    'message' => 'Order berhasil. Silahkan check Email / WhatsApp anda',
                    'response' => 'https://cloud-m.ripit.id/api/order-redirect'
                ]);
            } else {
                // Email gagal => rollback manual
                $order->delete();
                $profile->delete();
                $user->delete();
                $this->errorService->reportError($request->email. 'Order' . $response );
                return response()->json([
                    'code' => 0,
                    'message' => 'Gagal mengirim email. Data dibatalkan, silakan ulangi.',
                    'error' => $response
                ], 500);
            }
    }
    // Kirim ke payment gateway
    $data = [
        'act' => 'order_create',
        'order_id' => $order_id,
        'title' => 'order panel',
        'paket_id' => $paketId,
        'paket_type' => $paketType,
        'nominal' => $nominal,
        'name' => $request->name,
        'phone' => $request->phone,
        'email' => $request->email,
        'site' => 'ripit.id',
        'redirect' => 'https://cloud-m.ripit.id/api/order-redirect',
        'callback' => 'https://cloud-m.ripit.id/api/webhookPayOrder'
    ];

    $response = Http::withOptions(['verify' => false])
        ->asForm()
        ->post('https://pay.ripit.id/api.html', $data);
    Log::info('Order Package Payripit Received:', $data);
    if ($response->successful()) {
        $responseData = $response->json();

        Order::create([
            'order_id' => $responseData['id'],
            'invoice' => 'INV-BILL' . date('n-j') . '00' . $id_invoice,
            'paket_id' => $paketId,
            'paket_type' => $paketType,
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'nominal' => $nominal,
            'status' => $responseData['msg'],
            'link_url' => $responseData['link_url'],
            'link_pay' => $responseData['link_pay'],
        ]);

        // Ambil usage berdasarkan paket_id
 $usageMap = [
            1 => [
  ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => "Unlimited"],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => 0],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => "Unlimited"],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "Yes"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
], // isi dengan $paket_id_1
            2 =>[
  ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => "Unlimited"],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => 0],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => "Unlimited"],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "Yes"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
], // isi dengan $paket_id_2
            3 => [
   ["fitur" => "Team CRM", "usage" => 0],
  ["fitur" => "Products", "usage" => "Unlimited"],
  ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
  ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
  ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
  ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
  ["fitur" => "Cohort Analysis", "usage" => "Yes"],
  ["fitur" => "RFM Analysis", "usage" => "Yes"],
  ["fitur" => "Segmentasi User", "usage" => "Yes"],
  ["fitur" => "Custom Tags", "usage" => "Unlimited"],
  ["fitur" => "Waba Integrations", "usage" => "Yes"],
  ["fitur" => "WhatsApp Device", "usage" => 0],
  ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
  ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
  ["fitur" => "Customize Metric", "usage" => "Unlimited"],
  ["fitur" => "Customer Purchase Journey", "usage" => "No"],
  ["fitur" => "Support", "usage" => "Yes"],
  ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
],
            4 => [
      ["fitur" => "Team CRM", "usage" => "Unlimited"],
      ["fitur" => "Products", "usage" => "Unlimited"],
      ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
      ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
      ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
      ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
      ["fitur" => "Cohort Analysis", "usage" => "Yes"],
      ["fitur" => "RFM Analysis", "usage" => "Yes"],
      ["fitur" => "Segmentasi User", "usage" => "Yes"],
      ["fitur" => "Custom Tags", "usage" => "Unlimited"],
      ["fitur" => "Waba Integrations", "usage" => "Yes"],
      ["fitur" => "WhatsApp Device", "usage" => "Unlimited"],
      ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
      ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
      ["fitur" => "Customize Metric", "usage" => "Unlimited"],
      ["fitur" => "Customer Purchase Journey", "usage" => "Yes"],
      ["fitur" => "Support", "usage" => "Yes"],
      ["fitur" => "VIP Support + Pendampingan", "usage" => "Yes"]
    ],
        ];

        $usage = $usageMap[$paketId] ?? [];

        $appKey = config("app.key");
        $user_key = hex2bin(md5($appKey . ";" . $request->input("email")));
        $passwordview = Str::random(32);
        $pass = hex2bin(hash('sha256', $appKey . ";" . $request->email . ";" . $passwordview));

        $cek = User::where('email', $request->email)->first();

        if ($cek != null) {
            return response()->json([
                'code' => 1,
                'message' => 'Order created successfullyy',
                'response' => $responseData['link_pay']
            ]);
        } else {
            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = $pass;
            $user->role_id = 1;
            $user->user_key = $user_key;
            $user->paket_id = $paketId;
            $user->verification = 1;
            $user->usage_paket = json_encode($usage);
            $user->save();

            Order::where('id', $order_id)->update([
                'user_id' => $user->id
            ]);

            $profile = new Profile();
            $profile->user_id = $user->id;
            $profile->phone = $request->phone;
            $profile->save();

            return response()->json([
                'code' => 1,
                'message' => 'Order created successfully',
                'response' => $responseData['link_pay']
            ]);
        }
    }
    
    $this->errorService->reportError($request->email. 'Order Payment Vailed');
    return response()->json([
        'code' => 0,
        'message' => 'Payment request failed'
    ], 500);
}


public function order_callback(Request $request)
{
    // Log seluruh request yang diterima
    Log::info('Order Callback Received:', $request->all());
    
    $decodedData = [];
    if (!empty($request->data)) {
        $decodedJson = stripslashes($request->data); // hilangkan backslash
        $decodedData = json_decode($decodedJson, true);
    }
    // return response()->json($decodedData);
    // Temukan order berdasarkan order_id
    $order = Order::where('order_id', $decodedData['order_id'])->first();

    if (!$order) {
        $this->errorService->reportError("Order not found with order_id: " . $decodedData['order_id']);
        Log::warning("Order not found with order_id: " . $decodedData['order_id']);
        return response()->json(['error' => 'Order not found']);
    }

    // Update data order berdasarkan data yang diterima
    $order->update([
        'external_id' => $decodedData['external_id'],
        'tanggal_approve' => date('Y-m-d H:i:s', strtotime($request->tanggal_approve)),
        'status_paket' => 1,
        'nominal' => $request->nominal,
        'transaction_status' => $request->transaction_status,
        'callback' =>  $request->callback,
        'redirect' => $request->redirect,
    ]);

    if ($request->transaction_status === "SUCCESS") {
        // Hitung tanggal expired berdasarkan paket_type
        $paketType = $order->paket_type ?? 'monthly';
        $baseDate = Carbon::now();

        switch (strtolower($paketType)) {
            case '6-month':
                $expiredDate = $baseDate->copy()->addDays(180);
                break;
            case 'yearly':
                $expiredDate = $baseDate->copy()->addDays(365);
                break;
            default:
                $expiredDate = $baseDate->copy()->addDays(30);
                break;
        }

        Log::debug("Mengatur tanggal_expired ke: {$expiredDate} untuk order_id: {$order->order_id}");

        $order->tanggal_expired = $expiredDate;
        $saveStatus = $order->save();

        Order::where('user_id', $order->user_id)
                ->where('order_id', '!=', $order->order_id)
                ->update(['status_paket' => 0]);

        if ($saveStatus) {
            Log::info("tanggal_expired berhasil di-update untuk order_id: {$order->order_id}");
        } else {
            Log::error("GAGAL mengupdate tanggal_expired untuk order_id: {$order->order_id}");
        }

        // Proses user dan kirim email
        $paket_id = $order->paket_id;
        $email = $order->email;
        $name = $order->name;

        $passwordview = Str::random(32);
        $appKey = config('app.key');

        $pass = hex2bin(hash('sha256', $appKey . ";" . $email . ";" . $passwordview));

        $users = User::where('email', $email)->first();
        
        //check Upgrade / New 
        if($users && $order->IsUpgrade == 1){
           
            $usageMap = [
            1 => [
      ["fitur" => "Team CRM", "usage" => 0],
      ["fitur" => "Products", "usage" => "Unlimited"],
      ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
      ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
      ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
      ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
      ["fitur" => "Cohort Analysis", "usage" => "Yes"],
      ["fitur" => "RFM Analysis", "usage" => "Yes"],
      ["fitur" => "Segmentasi User", "usage" => "Yes"],
      ["fitur" => "Custom Tags", "usage" => 0],
      ["fitur" => "Waba Integrations", "usage" => "Yes"],
      ["fitur" => "WhatsApp Device", "usage" => 0],
      ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
      ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
      ["fitur" => "Customize Metric", "usage" => "Unlimited"],
      ["fitur" => "Customer Purchase Journey", "usage" => "No"],
      ["fitur" => "Support", "usage" => "Yes"],
      ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
    ], // isi dengan $paket_id_1
            2 =>[
      ["fitur" => "Team CRM", "usage" => 0],
      ["fitur" => "Products", "usage" => "Unlimited"],
      ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
      ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
      ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
      ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
      ["fitur" => "Cohort Analysis", "usage" => "Yes"],
      ["fitur" => "RFM Analysis", "usage" => "Yes"],
      ["fitur" => "Segmentasi User", "usage" => "Yes"],
      ["fitur" => "Custom Tags", "usage" => 0],
      ["fitur" => "Waba Integrations", "usage" => "Yes"],
      ["fitur" => "WhatsApp Device", "usage" => 0],
      ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
      ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
      ["fitur" => "Customize Metric", "usage" => "Unlimited"],
      ["fitur" => "Customer Purchase Journey", "usage" => "No"],
      ["fitur" => "Support", "usage" => "Yes"],
      ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
    ], // isi dengan $paket_id_2
            3 => [
       ["fitur" => "Team CRM", "usage" => 0],
      ["fitur" => "Products", "usage" => "Unlimited"],
      ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
      ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
      ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
      ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
      ["fitur" => "Cohort Analysis", "usage" => "Yes"],
      ["fitur" => "RFM Analysis", "usage" => "Yes"],
      ["fitur" => "Segmentasi User", "usage" => "Yes"],
      ["fitur" => "Custom Tags", "usage" => "Unlimited"],
      ["fitur" => "Waba Integrations", "usage" => "Yes"],
      ["fitur" => "WhatsApp Device", "usage" => 0],
      ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
      ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
      ["fitur" => "Customize Metric", "usage" => "Unlimited"],
      ["fitur" => "Customer Purchase Journey", "usage" => "No"],
      ["fitur" => "Support", "usage" => "Yes"],
      ["fitur" => "VIP Support + Pendampingan", "usage" => "No"]
    ],
            4 => [
      ["fitur" => "Team CRM", "usage" => "Unlimited"],
      ["fitur" => "Products", "usage" => "Unlimited"],
      ["fitur" => "Dashboard Analytics", "usage" => "Yes"],
      ["fitur" => "Leads & Contacts Management", "usage" => "Yes"],
      ["fitur" => "Broadcast Campaign", "usage" => "Unlimited"],
      ["fitur" => "Broadcast User List", "usage" => "Unlimited"],
      ["fitur" => "Cohort Analysis", "usage" => "Yes"],
      ["fitur" => "RFM Analysis", "usage" => "Yes"],
      ["fitur" => "Segmentasi User", "usage" => "Yes"],
      ["fitur" => "Custom Tags", "usage" => "Unlimited"],
      ["fitur" => "Waba Integrations", "usage" => "Yes"],
      ["fitur" => "WhatsApp Device", "usage" => "Unlimited"],
      ["fitur" => "Team Advertiser", "usage" => "Unlimited"],
      ["fitur" => "Advertiser Reporting", "usage" => "Yes"],
      ["fitur" => "Customize Metric", "usage" => "Unlimited"],
      ["fitur" => "Customer Purchase Journey", "usage" => "Yes"],
      ["fitur" => "Support", "usage" => "Yes"],
      ["fitur" => "VIP Support + Pendampingan", "usage" => "Yes"]
    ],
        ];
            $usage = $usageMap[$paket_id] ?? [];
            $users->paket_id = $paket_id;
            $users->usage_paket = json_encode($usage); // karena bukan tipe JSON di DB
            $users->save();
            
            $data_package = PaketPackage::where('id',$paket_id)->first();
            
            if($data_package){
               $plan_usage = $usage;
               $plan = json_decode($data_package->detail, true);                  
               $user_project = User::with('projects','profile')->find($users->id);
                // Melakukan enkripsi pada project_key dan user_key jika projects tidak null
                if ($user_project->projects) {
                    $user_project->projects->each(function($project) {
                        $project->project_key = bin2hex($project->project_key);
                    });
                }
                // $user->user_key = bin2hex($user->user_key);

                foreach ($user_project->projects as $project) {
    
                $param = '?project_key=' . $project['project_key'] . '&client_id=' . $project['id'];
                $data_upgrade = [
                    'user_id' => $users->id,
                    'plan' => $plan,
                    'plan_usage' => json_encode($plan_usage)
                    ];
                $endpoint = 'https://cloud-c.ripit.id';
            
                    try {
                       $response_billing = Http::post($endpoint . '/sync-upgrade' . $param  , $data_upgrade);
                    } catch (\Exception $e) {
                        $message =  $e->getMessage();
                        $this->errorService->reportError("Gagal Synch Upgrade Plan : {$email}, detail {$message}");
                        Log::error('Error saat kirim billing (non-owner)', ['message' => $e->getMessage()]);
                    }
                    
                }
             }

            // $this->errorService->reportError("User Upgrade Dengan Pembayaran Sukses : {$users->email} paket id : {$users->paket_id}");

            return response()->json([
                'message' => "Orders Berhasil, dengan Status Transaction SUCCESS",
                'code' => 1,
            ]);
        }
        else if ($users && $order->IsUpgrade == 0) {
            $users->password = $pass;
            $users->save();

            // Panggil webhook eksternal
            Http::post('https://connect.pabbly.com/workflow/sendwebhookdata/IjU3NjYwNTZjMDYzMTA0MzU1MjY0NTUzNjUxMzUi_pc', [
                'name' => $name,
                'email' => $email,
            ]);

            $get_package_name = PaketPackage::where('id', $paket_id)->pluck('name')->first();

            $dummyData = [
                'package' => $get_package_name,
                'email' => $email,
                'password' => $passwordview
            ];

            $emailContent = View::make('emails.order', $dummyData)->render();

            // Kirim email
            $to = $email;
            $subject = 'Order Plan Ripit.ID';
            $text = 'Selamat! Pesanan subscription Anda di Ripit.ID berhasil';
            $html = $emailContent;

            $response = $this->sendEmail($to, $subject, $text, $html);
            
            $waMessage = "Selamat! Pesanan subscription Anda di Ripit.ID berhasil.\\n\\n"
            . "Paket: *{$get_package_name}*\\n"
            . "Email: {$email}\\n\\n"
            . "Password: {$passwordview}\\n\\n"
            . "Terima kasih sudah berlangganan dengan kami.\\n"
            . "Jika ada pertanyaan, jangan ragu menghubungi kami.\\n\\n"
            . "Selamat menggunakan layanan Ripit.ID!";

            $sendwa = [
                'phone' => $users->profile['phone'],
                'caption' => $waMessage,
            ];
            // return response()->json($sendwa);
            $result_wa = $this->waService->sendWaToKonekwa($sendwa);


            return response()->json([
                'message' => 'Order Berhasil Diupdate, Silakan cek email anda',
                'code' => 1,
                'response' => $response,
            ], 200);
        } else {
            $this->errorService->reportError("User tidak ditemukan saat mencoba set password untuk: {$email}");
            Log::error("User tidak ditemukan saat mencoba set password untuk: {$email}");
            return response()->json([
                'message' => "Gagal membuat users",
                'code' => 0,
            ]);
        }
    }

    Log::info("Pembayaran untuk order_id {$order->order_id} tidak SUCCESS, status: {$request->transaction_status}");
    $this->errorService->reportError("Pembayaran untuk order_id {$order->order_id} tidak SUCCESS, status: {$request->transaction_status}");
    return response()->json([
        'message' => "Orders Berhasil diupdate, namun status pembayaran bukan SUCCESS",
        'code' => 0,
    ]);
}

   

public function order_redirect(Request $request){
      $message = 'Pembayaran Berhasil, Cek Email anda untuk melihat akun, dan buat project di platform RIPIT.ID';
    
    // Return the HTML page with a dynamic message
    return response()->view('redirectOrder', [
        'message' => $message
    ]);
    
}

public function testEmail(Request $request){
    $email = '<EMAIL>';
    $name = 'arfiyan';
    $passwordview = $email . ";" . $name . "123123123";

    $get_package_name = "tokokelontong";

    $dummyData = [
        'package' => $get_package_name,
        'email' => $email,
        'password' => $passwordview
    ];

    // Render the email view into a string
    $emailContent = View::make('emails.order', $dummyData)->render();

    // Prepare the POST data for the email API
    $post = [
        'from' => '<EMAIL>',
        'to' => $email,
        'subject' => 'Undangan Project',
        'html' => $emailContent
    ];

    // Initialize cURL
    $ch = curl_init();

    // Set the API URL
    curl_setopt($ch, CURLOPT_URL, 'https://aplikasi.kirim.email/api/v3/transactional/messages');

    // Set cURL options
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post);

    // Set API authentication
    curl_setopt($ch, CURLOPT_USERPWD, 'api:f4971596d9ec231ff047efea01c5715d7dbc5234fc9087ed52fc0c21a44d5817');

    // Set additional headers if required
    $headers = [
        'Domain: kirim.ripit.id'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    // Execute the cURL request
    $result = curl_exec($ch);

    // Error handling
    if (curl_errno($ch)) {
        return response()->json(['message' => 'Failed to send email: ' . curl_error($ch)], 500);
    }

    // Close the cURL session
    curl_close($ch);

    // Response data
    $response = [
        'message' => 'Pembayaran Berhasil, Cek Email anda untuk melihat akun, dan buat project diplatform RIPIT.ID',
        'code' => 1,
        'response' => $result
    ];

    return response()->json($response);
}
 
 
 //template order Panel
    private $apiKey = '**************************************************';
    private $domain = 'email.ripit.id';

    /**
     * Private function to send email using Mailgun
     */
    private function sendEmail($to, $subject, $text, $html = null, $from = '<EMAIL>')
    {
        // Mailgun API endpoint
        $url = "https://api.mailgun.net/v3/{$this->domain}/messages";

        // Prepare the email data
        $postData = [
            'from' => $from,
            'to' => $to,
            'subject' => $subject,
            'text' => $text,
        ];

        // Add HTML content if provided
        if ($html) {
            $postData['html'] = $html;
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$this->apiKey}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        // Send the request and capture the response
        $result = curl_exec($ch);
        $error = curl_error($ch);

        // Close cURL session
        curl_close($ch);

        // Check for errors
        if ($error) {
            return "cURL Error: $error";
        }

        return $result;
    }
 
public function getBusiness(Request $request)
{
    $businessTypes = [
        'Agriculture',
        'Art & Creative Services',
        'Automotive',
        'Beauty & Personal Care',
        'Construction',
        'Consulting Services',
        'E-commerce',
        'Education',
        'Entertainment',
        'Environmental Services',
        'Event Management',
        'F&B (Food & Beverage)',
        'Fashion',
        'Finance',
        'Gaming',
        'Healthcare',
        'Home & Living',
        'Legal Services',
        'Logistics & Transportation',
        'Manufacturing',
        'Media & Publishing',
        'Nonprofit & Social Services',
        'Pharmaceuticals',
        'Real Estate',
        'Retail',
        'Software Development',
        'Sports & Fitness',
        'Technology',
        'Telecommunications',
        'Tourism & Hospitality',
        'Lainnya'
    ];

    return response()->json([
        'code' => 1,
        'message' => 'Data berhasil diambil',
        'response' => $businessTypes
    ]);
}
//billing
public function billing(Request $request)
{
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    $page = $request->query('page', 1); // Ambil parameter ?page, default 1

    $orders = DB::table('orders')
        ->join('paket_package', 'orders.paket_id', '=', 'paket_package.id')
        ->select(
            'orders.order_id',
            'orders.invoice',
            'orders.transaction_status',
            'orders.nominal',
            'orders.created_at',
            'orders.tanggal_approve',
            'orders.tanggal_expired',
            'paket_package.name as paket',
            'orders.paket_type as paket_type',
            'orders.status_paket',
            'link_pay as link_pay'
        )
        ->where('orders.user_id', $user_id)
        ->paginate(50, ['*'], 'page', $page); // Pastikan pagination sesuai dengan request page

    if ($orders->isEmpty()) {
        return response()->json([
            'message' => "Data Billing Kosong",
            'code' => 0,
            'response' => []
        ]);
    }

    foreach ($orders as $order) {
    if (is_null($order->transaction_status)) {
        DB::table('orders')
            ->where('order_id', $order->order_id)
            ->update(['transaction_status' => 'Pending']);

        $order->transaction_status = 'Pending';
    }

    if ($order->transaction_status === 'Pending' && strtotime($order->created_at) < strtotime('-3 days')) {
        DB::table('orders')
            ->where('order_id', $order->order_id)
            ->update(['transaction_status' => 'Expired']);

        $order->transaction_status = 'Expired';
    }

    $order->periode = $order->tanggal_approve ? ($order->tanggal_approve . ' - ' . ($order->tanggal_expired ?? '')) : '';

    // Otomatis update status_paket jadi 0 jika sudah expired
    if ($order->status_paket == 1 && $order->tanggal_expired && Carbon::parse($order->tanggal_expired)->lt(now())) {
        DB::table('orders')
            ->where('order_id', $order->order_id)
            ->update(['status_paket' => 0]);

        $order->status_paket = 0;
    }

    // Status cuma Active atau Inactive
    $order->status = $order->status_paket == 1 ? 'Active' : 'Inactive';
}

    return response()->json([
        'message' => "Berhasil mengambil data orders",
        'code' => 1,
        'response' => $orders
    ]);
}


public function billingGetPlan(Request $request){
        $packages = PaketPackage::all();
        if($packages == null){
            
        return response()->json([
            'message' => 'Data Paket Panel Ripit Kosong', 
            'code' => 0, 
            'result' => null,
        ]);
        }
        

        $packages->transform(function ($package) {
        $package->detail = json_decode($package->detail, true);
            return $package;
        });
        // Format response JSON
        return response()->json([
            'message' => 'Data paket panel ripit berhasil didapatkan',
            'code' => 1, // Kode sukses, bisa diubah sesuai kebutuhan (misal 1 untuk sukses)
            'result' => $packages, // Hasil data dari tabel paket_package
        ]);
    
}


public function pilihPlan(Request $request)
{
    $validator = Validator::make($request->all(), [
        'paket_id' => 'required|integer',
        'paket_type' => 'required|in:monthly,6-month,yearly',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $validator->errors(),
        ], 422);
    }

    // Token validasi
    $tokenHash = hash("sha256", $request->bearerToken(), true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    $user = User::with('profile')->find($user_id);
    $package = PaketPackage::find($request->paket_id);

    if (!$package) {
        return response()->json([
            'code' => 0,
            'message' => 'Paket tidak ditemukan.',
        ], 404);
    }
    
    // Daftar harga dan durasi paket
    $hargaList = [
        'monthly' => [1 => 249000, 2 => 597600, 3 => 933750],
        '6-month' => [1 => 1344600, 2 => 3227040, 3 => 5042250],
        'yearly' => [1 => 2390400, 2 => 5736960, 3 => 8964000],
    ];
    $durasiHari = [
        'monthly' => 30,
        '6-month' => 180,
        'yearly' => 365,
    ];

    $paketTypeBaru = $request->paket_type;
    $paketIdBaru = (int)$request->paket_id;
    $hargaBaru = $hargaList[$paketTypeBaru][$paketIdBaru] ?? 0;

    if ($hargaBaru === 0) {
        return response()->json([
            'code' => 0,
            'message' => 'Kombinasi paket_type dan paket_id tidak valid.',
        ], 400);
    }
   
    $now = now();
    $activePackage = DB::table('orders')
        ->where('user_id', $user_id)
        ->where('transaction_status', 'SUCCESS')
        ->where('tanggal_approve', '<=', $now)
        ->where('tanggal_expired', '>=', $now)
        ->latest('id')
        ->first();

    $potongan = 0;

    if ($activePackage) {
        $paketTypeLama = $activePackage->paket_type;
        $paketIdLama = $activePackage->paket_id;
        $hargaLama = $activePackage->nominal;
        $tanggalExpired = Carbon::parse($activePackage->tanggal_expired);

        $sisaHari = $now->diffInDays($tanggalExpired, false);
        $sisaHari = max(0, $sisaHari);

        $durasiLama = $durasiHari[$paketTypeLama] ?? 0;
        if ($hargaLama > 0 && $durasiLama > 0 && $sisaHari > 0) {
            $nilaiSisa = ($hargaLama / $durasiLama) * $sisaHari;
            $potongan = round($nilaiSisa);
        }
    }

    $nominal = max(0, $hargaBaru - $potongan);
    $ppn = 15000;
    $admin = 5000;
    $total = $nominal + $ppn + $admin;

    $id_invoice = DB::table('orders')->where('user_id', $user_id)->latest('id')->value('id') ?? 1;

    $invoice = [
        "billed_to" => [
            "name" => $user->name . " " . $user->profile['nama_lengkap'],
            "address" => $user->profile['alamat_lengkap'],
            "city_country" => "Jakarta Selatan, DKI JAKARTA - INDONESIA",
            "phone" => $user->profile['phone'],
        ],
        "invoice_details" => [
            "invoice_number" => 'INV-BILL' . date('n-j') . '00' . $id_invoice,
            "subject" => "Upgrade Plan",
            "invoice_date" => $now->format('d M, Y'),
            "due_date" => $now->copy()->addDays(5)->format('d M, Y'),
        ],
        "items" => [
            [
                "paket_id" => $package->id,
                "name" => $package->name,
                "qty" => 1,
                "price" => number_format($hargaBaru, 2, '.', ''),
            ],
            [
                "name" => "Potongan sisa langganan",
                "qty" => 1,
                "price" => number_format($potongan, 2, '.', '')
            ]
        ],
        "summary" => [
            // number_format($angka, 2, '.', '')

            "subtotal" => number_format($nominal, 2, '.', ''),
            "ppn_12" => number_format($ppn, 2, '.', ''),
            "admin_fee" => number_format($admin, 2, '.', ''),
            "total" => number_format($total, 2, '.', ''),
        ],
        "currency" => "IDR",
        "total_in_idr" => number_format($total, 2, '.', ''),
    ];

    return response()->json([
        'message' => "Berhasil memilih paket, silahkan lanjutkan submit order plan anda",
        'code' => 1,
        'result' => $invoice
    ]);
}
//download orders

public function DownloadBillingOrderPlan(Request $request){
     $validator = Validator::make($request->all(), [
        'invoice' => 'required',
    ]);
    
        if ($validator->fails()) {
    return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $validator->errors(),
        ], 422);
    }
    
    //data billing information users 
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    $user = User::with('profile')->find($user_id);
    // $package = PaketPackage::where('id' ,$request->id)->first()->name;
    
    $order =  DB::table('orders')->where('invoice', $request->invoice)->first();
    $transaction_status = $order->transaction_status;

    $payment_status = (strtolower($transaction_status) === 'SUCCESS') ? 'paid' : 'unpaid';
    $package = PaketPackage::where('id', $order->paket_id)->first();
    $package_current = PaketPackage::where('id', $user->paket_id)->first();
    
    $hargaList = [
    'monthly' => [1 => 249000, 2 => 597600, 3 => 933750],
    '6-month' => [1 => 1344600, 2 => 3227040, 3 => 5042250],
    'yearly' => [1 => 2390400, 2 => 5736960, 3 => 8964000],
    ];
 $paketType = $order->paket_type ?? 'monthly';
$baseDate = Carbon::parse($order->created_at);
switch (strtolower($paketType)) {
    case '6-month':
        $expiredDate = $baseDate->copy()->addDays(180);
        break;
    case 'yearly':
        $expiredDate = $baseDate->copy()->addDays(365);
        break;
    default:
        $expiredDate = $baseDate->copy()->addDays(30);
        break;
}

$unit_price = $hargaList[$paketType][$package->id] ?? 0;
$order->tanggal_expired = $expiredDate->format('Y-m-d');
$profile = $user->profile;

$provinsi = DB::table('app_origin_provinsis')->where('id', $profile['provinsi'] ?? 0)->value('name') ?? '-';
$kota = DB::table('app_origin_cities')->where('id', $profile['kota'] ?? 0)->value('name') ?? '-';
$kecamatan = DB::table('app_origin_kecamatans')->where('id', $profile['kecamatan'] ?? 0)->value('name') ?? '-';

$kode_post = $profile['kode_pos'] ?? '-';
    
   
    $projectKey = $request->project_key;

$project = DB::table('projects')
    ->whereRaw('project_key = UNHEX(?)', [$projectKey])
    ->select('project_name')
    ->first();

$project_name = "";
if ($project) {
    $project_name = $project->project_name;
}

$logo = null;
    $logoPath = public_path('storage/logo.jpg');

if (file_exists($logoPath)) {
    $mimeType = mime_content_type($logoPath);
    $logo = 'data:' . $mimeType . ';base64,' . base64_encode(file_get_contents($logoPath));
}

    $data = [
        'invoice_number' => $order->invoice,
        'issue_date' => Carbon::parse($order->created_at)->format('Y-m-d'),
        'due_date' => Carbon::parse($order->created_at)->format('Y-m-d'),
        'bill_to' => [
            'name' => $user->name ." ". $user->profile['nama_lengkap'],
            'email' => $user->email
        ],
        'company' => [
            'name' => $project_name,
            'address' => $provinsi.', ' . $kota .', '. $kecamatan . ' (' . $kode_post . ' )',
            'email' => $user->email,
        ],
        'items' => [
            [
                'description' => $package->name . '( '.$order->paket_type. ' )' . ' -' . $baseDate->format('Y-m-d') . ' - ' . $order->tanggal_expired,
                'qty' => 1,
                'unit_price' => number_format($unit_price, 2, '.', ''),
                'amount' => number_format($order->nominal, 2, '.', ''),
            ],
        ],
        'subtotal' => number_format($order->nominal, 2, '.', ''),
        'discount' => 0,
        'total' => number_format($order->nominal, 2, '.', ''),
    ];
    

 $pdf = PDF::loadView('pdf.template-invoice', ['result' => $data, 'logo' => $logo])
          ->setOptions(['isRemoteEnabled' => true]);

    return $pdf->stream('invoice.pdf');   
}

public function templateInvoice()
{
    $logo = "https://gss.objects-us-east-1.dream.io/logo.png";

    $data = [
        'invoice_number' => '30F25202-0049',
        'issue_date' => Carbon::now()->format('F d, Y'),
        'due_date' => Carbon::now()->format('F d, Y'),
        'bill_to' => [
            'name' => 'Tangahu Rifqi',
            'email' => '<EMAIL>',
        ],
        'company' => [
            'name' => 'Frase, Inc',
            'address' => '45 School St, 2nd Floor, Boston, Massachusetts 02108, United States',
            'email' => '<EMAIL>',
        ],
        'items' => [
            [
                'description' => 'addon - May 28 – Jun 25, 2025',
                'qty' => 1,
                'unit_price' => 34.99,
                'amount' => 34.99,
            ],
            [
                'description' => 'team_lifetime (per seat) - May 28 – Jun 25, 2025',
                'qty' => 3,
                'unit_price' => 0.00,
                'amount' => 0.00,
            ],
            [
                'description' => 'First 3',
                'qty' => 3,
                'unit_price' => 0.00,
                'amount' => 0.00,
            ],
        ],
        'subtotal' => 34.99,
        'discount' => 17.50,
        'total' => 17.49,
    ];

   
$pdf = PDF::loadView('pdf.template-invoice', ['result' => $data, 'logo' => $logo])
    ->setOptions(['isRemoteEnabled' => true]);

    return $pdf->stream('invoice.pdf'); 
    // return 
            // return view('coba', ['result' => $data]);
}

//end
public function billingOrderPlan(Request $request){
    $validator = Validator::make($request->all(), [
        'paket_id' => 'required|integer',
        'nominal' => 'required'
    ]);

    // If validation fails
    if ($validator->fails()) {
        return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $validator->errors(),
        ], 422);
    }
    
   
    
    
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
   
      $id_invoice = DB::table('orders')
    ->where('user_id', $user_id)
    ->latest('id') // Urutkan berdasarkan kolom 'id' secara descending
    ->value('id'); 
    $today = date('Y-m-d');
    
    $user = User::with('profile')->find($user_id);
    //pengecekan
   
    
$id = Order::max('order_id'); // Mengambil ID terbesar yang ada
$order_id = $id ? $id + 1 : 1;
    
    // Prepare data for the payment gateway
$data = [
        'act' => 'order_create',
        'order_id' => $order_id,
        'title' => 'Order Panel Ripit',
        'paket_id' => $request->paket_id,
        'nominal' => $request->nominal,
        'name' => $user->name . " " . $user->profile['nama_lengkap'],
        'phone' => $user->profile['phone'],
        'email' => $user->email,
        'site' => 'ripit.id',
        'redirect' => 'https://cloud-m.ripit.id/api/order-redirect',
        'callback' => 'https://cloud-m.ripit.id/api/webhookPayOrder'
    ];

    try {
        //   return response()->json($user_id,500);
        // Kirim request POST
        Log::info('Mengirim request ke payment gateway', $data);

        $responseData = Http::asForm()->post('https://pay.ripit.id/api.html', $data);
            // Log response dari payment gateway
        Log::info('Response dari payment gateway', [
            'status' => $responseData->status(),
            'body' => $responseData->body()
        ]);

        if ($responseData->successful()) {
               Log::info('Pembayaran berhasil diproses', ['order_id' => $order_id]);
     
        $order = Order::create([
            'order_id' => $responseData['id'],
            'invoice' => 'INV-BILL' . date('n', strtotime($today)) . '-' . date('j', strtotime($today)) .'00' . $id_invoice,
            'user_id' => $user_id,
            'paket_id' => $request->paket_id,
            'IsUpgrade' => 1,
            'paket_type' => $request->paket_type,
            'name' => $user->name . "" . $user->profile['nama_lengkap'],
            'phone' => $data['phone'],
            'email' => $user->email,
            'nominal' => $request->nominal,
            'status' => $responseData['msg'],
            'link_url' => $responseData['link_url'],   // Save link_url
            'link_pay' => $responseData['link_pay']    // Save link_pay
        ]);
            // Jika sukses, kembalikan hasil
            return response()->json([
                'message' => "Order Berhasil dibuat, silahkan lakukan pembayaran",
                'code' => '1',
                'response' => $responseData->json(),
            ]);
        } else {
            $body = $responseData->body();
             $this->errorService->reportError("Failed to process order  {$user->email} body : {$body}");
            // Jika gagal, kembalikan error
            return response()->json([
                'message' => 'Failed to process order. Server responded with an error.',
                'code' => '0',
                'response' => $responseData->body()
            ], $responseData->status());
        }
    } catch (\Exception $e) {
        $message = $e->getMessage();
         $this->errorService->reportError("Failed to process order  {$user->email} body : {$message}");
        // Tangani error lainnya
        return response()->json([
            'code' => '0',
            'message' => 'An error occurred while processing your request.',
            'response' => $e->getMessage(),
        ], 500);
    }
    //end try
    
}

    
       public function getMenus()
    {
        // Ambil data menu utama
        $menus = DB::table('menus')->where('status', 1)->get();
        $menuData = [];
        
        foreach ($menus as $menu) {
            // Ambil sub-menu untuk menu ini, termasuk sub-sub-menu berdasarkan parent_id
            $subMenus = $this->getSubMenus($menu->id);
            
            $menuData[] = [
                'menu_id' => $menu->id,
                'name' => $menu->name,
                'link' => $menu->link,
                'sub' => $subMenus
            ];
        }

        return response()->json($menuData);
    }

    // Fungsi untuk mengambil sub-menu berdasarkan menu_id dan parent_id
public function generateUserMenuAccess(Request $request)
{
    $userId = $request->userID;

    // Ambil semua menu yang aktif
    $menus = DB::table('menus')->where('status', 1)->get();

    // Cek apakah user adalah owner
    $isOwner = DB::table('users')
        ->join('roles', 'users.role_id', '=', 'roles.id')
        ->select('roles.name')
        ->where('users.id', $userId)
        ->first();

    // Untuk owner, kita beri akses ke semua menu dan sub-menu
    foreach ($menus as $menu) {
        // Cek apakah menu sudah ada di user_menus
        $userMenuExists = DB::table('user_menus')->where('user_id', $userId)
                                                 ->where('menu_id', $menu->id)
                                                 ->exists();

        if ($userMenuExists) {
            // Jika menu sudah ada, perbarui status menjadi 1
            DB::table('user_menus')
                ->where('user_id', $userId)
                ->where('menu_id', $menu->id)
                ->update(['status' => 1]);
        } else {
            // Jika menu belum ada, tambahkan ke user_menus
            DB::table('user_menus')->insert([
                'user_id' => $userId,
                'menu_id' => $menu->id,
                'status' => 1 // Akses diberikan
            ]);
        }

        // Ambil semua sub-menu untuk menu ini
        $subMenus = DB::table('sub_menus')->where('menu_id', $menu->id)->get();

        foreach ($subMenus as $subMenu) {
            // Cek apakah sub-menu sudah ada di user_sub_menus
            $userSubMenuExists = DB::table('user_sub_menus')->where('user_id', $userId)
                                                            ->where('sub_menu_id', $subMenu->id)
                                                            ->exists();

            if ($userSubMenuExists) {
                // Jika sub-menu sudah ada, perbarui status menjadi 1
                DB::table('user_sub_menus')
                    ->where('user_id', $userId)
                    ->where('sub_menu_id', $subMenu->id)
                    ->update(['status' => 1]);
            } else {
                // Jika sub-menu belum ada, tambahkan ke user_sub_menus
                DB::table('user_sub_menus')->insert([
                    'user_id' => $userId,
                    'sub_menu_id' => $subMenu->id,
                    'status' => 1 // Akses diberikan
                ]);
            }
        }
    }

    return response()->json(['message' => 'Akses menu telah berhasil di-generate untuk user.']);
}


//coba new
private function getUserMenu($id)
{
    $user_id = $id;

    // Ambil semua menu utama
    $menus = DB::table('menus')
        ->get(['menus.id as menu_id', 'menus.status as menu_status', 'menus.name as name', 'menus.link']);

    // Filter menu berdasarkan akses user
    $menus = $menus->filter(function ($menu) use ($user_id) {
        // Cek akses user untuk menu ini
        $userMenu = DB::table('user_menus')
            ->where('user_id', $user_id)
            ->where('menu_id', $menu->menu_id)
            ->first();

        // Update menu_status berdasarkan akses
        $menu->menu_status = $userMenu ? $userMenu->status : 0;

        // Hanya menu dengan status 1 yang lolos
        return $menu->menu_status == 1;
    });

    foreach ($menus as &$menu) {
        // Ambil sub-menu dengan status 1
        $menu->sub = $this->getSubMenus($menu->menu_id, null, $user_id);
    }

    return $menus->values(); // Reset array keys
}

private function getSubMenus($menuId, $parentId = null, $userId)
{
    // Ambil sub-menu berdasarkan menu_id dan parent_id
    $subMenus = DB::table('sub_menus')
        ->where('menu_id', $menuId)
        ->where('parent_id', $parentId)
        ->get(['id as sub_menu_id', 'name as name', 'link', 'status as sub_menu_status']);

    // Filter sub-menu berdasarkan akses user
    $filteredSubMenus = $subMenus->filter(function ($subMenu) use ($userId) {
        $userSubMenu = DB::table('user_sub_menus')
            ->where('user_id', $userId)
            ->where('sub_menu_id', $subMenu->sub_menu_id)
            ->first();

        // Update sub_menu_status berdasarkan akses user
        $subMenu->sub_menu_status = $userSubMenu ? $userSubMenu->status : 0;

        // Hanya sub-menu dengan status 1 yang lolos
        return $subMenu->sub_menu_status == 1;
    });

    foreach ($filteredSubMenus as &$subMenu) {
        // Panggil fungsi rekursif untuk mendapatkan sub-menu anak
        $subMenu->sub = $this->getSubMenus($menuId, $subMenu->sub_menu_id, $userId);
    }

    return $filteredSubMenus->values(); // Reset array keys
}


public function generateInvoicePDF(Request $request)
{
 
    $validator = Validator::make($request->all(), [
        'paket_id' => 'required|integer',
    ]);
    
        if ($validator->fails()) {
    return response()->json([
            'success' => false,
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $validator->errors(),
        ], 422);
    }
    
    //data billing information users 
    $token = $request->bearerToken();
    $tokenHash = hash("sha256", $token, true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;
    $user = User::with('profile')->find($user_id);
    // $package = PaketPackage::where('id' ,$request->id)->first()->name;
    $package = PaketPackage::where('id', $request->paket_id)->first();
    $package_current = PaketPackage::where('id', $user->paket_id)->first();
    
    $harga_current_package = (float)$package_current->harga;
    $harga_upgrade_package = (float)$package->harga;
    
if ($harga_upgrade_package <= $harga_current_package) {
    return response()->json([
        'code' => 0,
        'message' => 'Gagal Upgrade Plan, Silahkan Pilih Plan diatas Plan Anda ',
        'result' => []
    ], 500);
}

    // $cek_pugrade = 
    $details = json_decode($package->detail, true);
    $packages = [
        'paket' => $package->name,
        'detail' => $details
        ];

    $id_invoice = DB::table('orders')
    ->where('user_id', $user_id)
    ->latest('id') // Urutkan berdasarkan kolom 'id' secara descending
    ->value('id'); // Ambil nilai dari kolom 'id'
    
    $tanggal_mulai = now()->format('Y-m-d H:i:s');
    $tanggal_selesai = now()->addDays(30)->format('Y-m-d H:i:s');
    $tanggal_selesai_payment = now()->addDays(5)->format('Y-m-d H:i:s');
    //cek apa ada paket yang berjalan
     $currentDate = now()->format('Y-m-d H:i:s'); // Tanggal hari ini
     $activePackage = DB::table('orders')
        ->where('user_id', $user_id)
        ->where('transaction_status', 'SUCCESS')
        ->where('tanggal_approve', '<=', $currentDate)
        ->where('tanggal_expired', '>=', $currentDate)
        ->first(); // Ambil data pertama jika ada
    $nominal = 0;
    if ($activePackage) {
        $nominal_paket_berjalan = $activePackage->nominal;
        $tanggalApprove = Carbon::parse($activePackage->tanggal_approve);
        $hari_berjalan = round($tanggalApprove->diffInDays(Carbon::now()));
        $nominal_paket_order = round($package->harga);
        $nominal = ($nominal_paket_order - $nominal_paket_berjalan) / 30 * $hari_berjalan;

    //     return response()->json([
    //     'message' => 'Paket sedang berjalan',
    //     'code' => 1,
    //     'package' => [
    //         'nominal_sisa' => $nominal_sisa
    //     ]
    // ]);
    }else{
        $nominal = $package->harga;
    }
    
    // $package = [];
    $billingInformation = [
        'name' => $user->name ." ". $user->profile['nama_lengkap'],
        'email' => $user->email,
        'phone' => $user->profile['phone'],
        ];
    
    $subtotal = $nominal;
    $ppn = 15000;
    $admin = 5000;
    $total = $subtotal + $ppn + $admin;
   
    $today = date('Y-m-d');
    $data = [
    "billed_to" => [
        "name" => $user->name ." ". $user->profile['nama_lengkap'],
        "address" => $user->profile['alamat_lengkap'],
        "city_country" => "Jakarta Selatan, DKI JAKARTA - INDONSIA",
        "phone" => $user->profile['phone'],
    ],
    "invoice_details" => [
        "invoice_number" => 'INV-BILL' . date('n', strtotime($today)) . '-' . date('j', strtotime($today)) .'00' . $id_invoice,
        "subject" => "Upgrade Plan",
        "invoice_date" => $currentDate,
        "due_date" => $tanggal_selesai_payment,
    ],
    "items" => [
        [
            "paket_id" => $package->id,
            "name" => $package->name,
            "qty" => 1,
            "price" => $package->harga
        ],
        [
            "name" => "Discount",
            "qty" => 1,
            "price" => "Rp.0"
        ]
    ],
    "summary" => [
        "subtotal" => number_format($subtotal, 2, '.', ''),
        "ppn_12" => number_format($ppn, 2, '.', ''),
        "admin_fee" => number_format($admin, 2, '.', ''),
        "total" => number_format($total, 2, '.', ''),
    ],
    "currency" => "IDR",
    "total_in_idr" => number_format($total, 2, '.', ''),
];

    // Generate PDF
    $pdf = PDF::loadView('pdf.template', [
        'result' => $data
        ]);

        return $pdf->stream('invoice.pdf');

}

    //kurangi usage
    public function productUsage(Request $request){
        
    
        $token = $request->bearerToken();
        
        // Menghasilkan hash dari token menggunakan SHA-256
        $tokenHash = hash("sha256", $token, true);
        
        // Cari user berdasarkan token hash
        $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    
        if (!$userToken) {
            return response()->json([
                'code' => 0,
                'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
                'result' => []
            ], 404);
        }
    
        $user_id = $userToken->user_id;
        
                $data_plan = DB::table('users')
        ->where('id', $user_id)
        ->first();
    
    $plan_usage = json_decode($data_plan->usage_paket, true);
    $cek_plan = $plan_usage[1]['usage'];
    
        foreach ($plan_usage as &$fitur) {
            if ($fitur['fitur'] === 'Products') {
                $fitur['usage'] = max(0, $fitur['usage'] - 1); // Pastikan tidak kurang dari 0
                break;
            }
        }
        // Encode kembali ke JSON
        $updated_plan_usage = json_encode($plan_usage);
       
        // Update data ke database
          $delete =   DB::table('users')
            ->where('id', $user_id)
            ->update(['usage_paket' => $updated_plan_usage]);
        
        if($delete){
                return response()->json([
                    'message' => 'berhasil hapus paket dimaster',
                    'code' => 1,
                    'result' => []
                    ]);        
        }
        
    }
    //tambah usage
    public function productUsageTambah(Request $request){
        
    
        $token = $request->bearerToken();
        
        // Menghasilkan hash dari token menggunakan SHA-256
        $tokenHash = hash("sha256", $token, true);
        
        // Cari user berdasarkan token hash
        $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    
        if (!$userToken) {
            return response()->json([
                'code' => 0,
                'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
                'result' => []
            ], 404);
        }
    
        $user_id = $userToken->user_id;
        
                $data_plan = DB::table('users')
        ->where('id', $user_id)
        ->first();
    
    $plan_usage = json_decode($data_plan->usage_paket, true);
    $cek_plan = $plan_usage[1]['usage'];
    
        foreach ($plan_usage as &$fitur) {
            if ($fitur['fitur'] === 'Products') {
                $fitur['usage'] = max(0, $fitur['usage'] - 1); // Pastikan tidak kurang dari 0
                break;
            }
        }
        // Encode kembali ke JSON
        $updated_plan_usage = json_encode($plan_usage);
       
        // Update data ke database
          $delete =   DB::table('users')
            ->where('id', $user_id)
            ->update(['usage_paket' => $updated_plan_usage]);
        
        if($delete){
                return response()->json([
                    'message' => 'berhasil hapus paket dimaster',
                    'code' => 1,
                    'result' => []
                    ]);        
        }
        
    }
    

public function getAllPhone()
{
    // Get all projects from the database
    $data = DB::table('cs_phones')->select('cs_phone')->get();
    // Return the combined list of phone numbers
    return response()->json([
        'code' => 1,
        'message' => 'Data ditemukan',
        'result' => $data
    ]);
}

public function addPhone(Request $request)
{

    // Insert the phone number into the cs_phones table
    DB::table('cs_phones')->insert([
        'cs_phone' => $request->cs_phone,
    ]);

    // Return a response indicating success
    return response()->json([
        'code' => 1,
        'message' => 'Phone number added successfully',
        'result' => $request->phone
    ]);
}

// Function to delete a phone number
public function deletePhone(Request $request)
{
    // Validate the input phone number

    // Delete the phone number from the cs_phones table
    DB::table('cs_phones')
        ->where('cs_phone', $request->cs_phone)
        ->delete();

    // Return a response indicating success
    return response()->json([
        'code' => 1,
        'message' => 'Phone number deleted successfully',
        'result' => $request->cs_phone
    ]);
}

public function setExpired(Request $request)
{
    $request->validate([
        'email' => 'required|email',
        'days' => 'required|integer|min:1',
    ]);

    $email = $request->input('email');
    $days = (int) $request->input('days');

    $user = DB::table('users')->where('email', $email)->first();
    
    if (!$user) {
        return response()->json(['error' => 'User not found'], 404);
    }
    
    $profile = DB::table('profile')->where('user_id', $user->id)->first();
    
    if (!$profile) {
        return response()->json(['error' => 'profile not found'], 404);
    }

    // Cek jika ada lebih dari satu order dengan status_paket = 1
    $jumlahStatusPaket1 = DB::table('orders')
        ->where('user_id', $user->id)
        ->where('status_paket', 1)
        ->count();

    if ($jumlahStatusPaket1 > 1) {
        return response()->json([
            'error' => 'User memiliki lebih dari satu order dengan status_paket = 1. Harap bersihkan data duplikat terlebih dahulu.'
        ], 400);
    }

    // Cari order sesuai prioritas
    $order = DB::table('orders')
        ->where('user_id', $user->id)
        ->where('status_paket', 1)
        ->orderByDesc('id')
        ->first();

    if (!$order) {
        $order = DB::table('orders')
            ->where('user_id', $user->id)
            ->where('transaction_status', 'SUCCESS')
            ->orderByDesc('id')
            ->first();
    }

    if (!$order) {
        $order = DB::table('orders')
            ->where('user_id', $user->id)
            ->where('transaction_status', 'EXPIRED')
            ->orderByDesc('id')
            ->first();
    }
    $random = str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT); // hasil: "0382"
    // Jika tetap tidak ada order, buat order dummy
    if (!$order) {
        $dummyId = DB::table('orders')->insertGetId([
            'user_id' => $user->id,
            'order_id' => $random,
            'name' => $user->name,
            'phone' => $profile->phone,
            'email' => $user->email,
            'invoice' => 'INV-DUMMY-' . time(),
            'paket_id' => 1,
            'paket_type' => 'monthly',
            'nominal' => 100000,
            'status' => 'Dummy Created',
            'link_url' => 'https://dummy.url',
            'link_pay' => 'https://dummy.url/pay',
            'transaction_status' => 'DUMMY',
            'status_paket' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $order = DB::table('orders')->where('id', $dummyId)->first();
    }

    // Hitung tanggal expired baru
    $tanggalExpiredBaru = Carbon::now()->addDays($days);

    // Update tanggal_expired dan transaction_status ke SUCCESS
    DB::table('orders')
        ->where('id', $order->id)
        ->update([
            'tanggal_expired' => $tanggalExpiredBaru,
            'transaction_status' => 'SUCCESS',
            'status_paket' => 1
        ]);

    return response()->json([
        'message' => 'Tanggal expired & status transaksi berhasil diperbarui',
        'order_id' => $order->id,
        'tanggal_expired' => $tanggalExpiredBaru->toDateString(),
    ]);
}


    public function getbyname(Request $request)
    {
        $name = $request->query('name');

        if (!$name) {
            return response()->json([
                'status' => 'error',
                'message' => 'Parameter name wajib diisi'
            ], 400);
        }

        $project = Project::where('project_name', $name)->first();

        if (!$project) {
            return response()->json([
                'status' => 'error',
                'message' => 'Project tidak ditemukan'
            ], 404);
        }

        // Ubah binary ke hex
        $projectKeyHex = bin2hex($project->project_key);

        return response()->json([
            'id' => $project->id,
            'project_name' => $project->project_name,
            'project_key' => $projectKeyHex
        ]);
    }


}