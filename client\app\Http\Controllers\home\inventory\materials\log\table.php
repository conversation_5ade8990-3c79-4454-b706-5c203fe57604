<?php
namespace App\Http\Controllers\home\inventory\materials\log;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\po_items as tblPoItems;

class table extends Controller
{
    //
    public function inbb(Request $request)
    {
        $Config = new Config;

        $search = "%" . trim($request->search) . "%";
        $categori = trim($request->categori_selected);
        $suplier = trim($request->suplier_selected);
        $date = trim($request->date);
        $paging = trim($request->paging);


        //
        $getdata = tblPoItems::from('po_items as pi')
        ->select(
            'pi.id'
        )
        ->where([
            'pi.progress'   =>  1,
            'pi.status'     =>  1
        ]);
        if( $suplier != '')
        {
            $getdata = $getdata->where([
                'pi.suplier_id' =>  $suplier
            ]);
        }
        if( $categori != '')
        {
            $getdata = $getdata->where([
                'pi.categori_id' =>  $categori
            ]);
        }
        if($date != '')
        {
            $dt = explode("_", $date);
            
            $getdata = $getdata->whereBetween('pi.created_at', [$t[0],$dt[1]]);
        }

        //
        $count = $getdata->count();
        if( $count == 0)
        {
            $data = [
                "message"       =>  "Data tidak ditemukan"
            ];

            return response()->json($data, 404);
        }

        //
        $gettable = $getdata->orderBy('pi.id', 'desc')
        ->take($Config->table(['paging'=>$paging])['paging_item'])
        ->skip($Config->table(['paging'=>$paging])['paging_limit'])
        ->get();

        foreach($gettable as $row)
        {
            $list[] = [
                'id'        =>  $row->id
            ];
        }

        //
        $data = [
            "message"       =>  ""
        ];

        return response()->json($data, 200);
    }
}