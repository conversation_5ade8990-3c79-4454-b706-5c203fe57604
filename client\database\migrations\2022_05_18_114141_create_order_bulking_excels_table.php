<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrderBulkingExcelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_bulking_excels', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->string('token');
            $table->string('invoice');
            $table->text('field');
            $table->text('items');
            $table->integer('distributor_id');
            $table->bigInteger('user_id');
            $table->integer('order_status');
            $table->integeR('subtotal');
            $table->integer('ongkir');
            $table->integer('discount');
            $table->integer('total');
            $table->integer('debt');
            $table->integer('payment_type');
            $table->integer('payment');
            $table->bigInteger('payment_user');
            $table->string('payment_date');
            $table->string('due_date');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_bulking_excels');
    }
}
