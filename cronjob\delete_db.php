<?php

// Initialize parameters array
$parameters = [];

// Check if the script is called from the command line
if (php_sapi_name() === 'cli') {
    // Collect command-line arguments (skip the first, which is the script name)
    $args = $argv;
    array_shift($args); // Remove the script name
    foreach ($args as $arg) {
        $parameters[] = $arg; // Store all parameters in an array
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // If called from an API, collect parameters from POST request
    $parameters = $_POST; // Assuming parameters are sent via POST
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // If called from an API, collect parameters from GET request
    $parameters = $_GET; // Assuming parameters are sent via GET
}

// Example usage of the parameters
if (empty($parameters)) {
    die("Error: At least one parameter is required.\n");
}

// Check for a specific parameter (e.g., database name)
$dbName = isset($parameters['dbName']) ? $parameters['dbName'] : null; // Adjust the key according to how you send it

if (!$dbName) {
    die("Error: Database name is required.\n");
}

// Here you can use $dbName in your logic
echo "Deleting database: " . htmlspecialchars($dbName) . "\n";

// Add your database deletion logic here
// e.g., $btApi->deleteDatabase($dbName);

// ...

?>
