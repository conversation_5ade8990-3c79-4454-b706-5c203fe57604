<?php

// Koneksi ke database
$host = 'localhost'; // Host database
$username = 'root'; // Username database
$password = '891e956c1bec2f40'; // Password database

// Nama database dan user yang akan dihapus
$databaseName = '74_ripit';
$databaseUser = '74_ripit';

try {
    // Membuat koneksi ke MySQL
    $conn = new PDO("mysql:host=$host", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Koneksi berhasil.\n";

    // Hapus database
    echo "Menghapus database: $databaseName\n";
    $conn->exec("DROP DATABASE `$databaseName`");
    echo "Database $databaseName berhasil dihapus.\n";

    // Cek apakah user ada sebelum dihapus
    echo "Menghapus user: $databaseUser\n";
    
    // Query untuk mengecek apakah user ada
    $checkUserQuery = $conn->prepare("SELECT COUNT(*) FROM mysql.user WHERE user = :user");
    $checkUserQuery->execute(['user' => $databaseUser]);
    $userCount = $checkUserQuery->fetchColumn();

    if ($userCount > 0) {
        // Hapus user
        $conn->exec("DROP USER '$databaseUser'@'localhost'");
        $conn->exec("DROP USER '$databaseUser'@'%'");
        echo "User $databaseUser berhasil dihapus.\n";
    } else {
        echo "User $databaseUser tidak ditemukan.\n";
    }

} catch (PDOException $e) {
    // Tangani error koneksi atau query
    echo "Terjadi kesalahan: " . $e->getMessage() . "\n";
} finally {
    // Tutup koneksi
    $conn = null;
    echo "Koneksi ditutup.\n";
}
// Nama file: show_db_users.php

// // Pastikan script ini berjalan hanya dari CLI
// if (php_sapi_name() !== 'cli') {
//     die("Script ini hanya bisa dijalankan melalui CLI.\n");
// }

// // Koneksi ke database
// $host = 'localhost'; // Host database
// $username = 'root'; // Username database
// $password = '891e956c1bec2f40'; // Password database

// try {
//     // Membuat koneksi
//     $conn = new PDO("mysql:host=$host;dbname=mysql", $username, $password);
//     $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

//     echo "========================\n";
//     echo "Tanggal: " . date('Y-m-d H:i:s') . "\n";
//     echo "Daftar Database Users dan Privileges:\n\n";

//     // Query untuk mendapatkan semua pengguna MySQL
//     $query = $conn->query("SELECT User, Host FROM mysql.user");

//     foreach ($query as $row) {
//         $user = $row['User'];
//         $host = $row['Host'];

//         echo "User: $user\n";
//         echo "Host: $host\n";

//         // Tampilkan daftar database yang dapat diakses user ini
//         try {
//             $dbQuery = $conn->query("
//                 SELECT schema_name 
//                 FROM information_schema.schemata
//                 WHERE schema_name NOT IN ('mysql', 'performance_schema', 'information_schema', 'sys')
//             ");

//             echo "  Accessible Databases:\n";
//             foreach ($dbQuery as $dbRow) {
//                 $dbname = $dbRow['schema_name'];

//                 // Cek hak akses untuk setiap database
//                 $privQuery = $conn->query("
//                     SELECT * 
//                     FROM mysql.db 
//                     WHERE User = '$user' AND Host = '$host' AND Db = '$dbname'
//                 ");
//                 if ($privQuery->rowCount() > 0) {
//                     echo "    - $dbname\n";
//                 }
//             }
//         } catch (PDOException $e) {
//             echo "  [Gagal mendapatkan daftar database]\n";
//         }

//         echo "\n";
//     }

//     echo "========================\n";
// } catch (PDOException $e) {
//     echo "Terjadi kesalahan: " . $e->getMessage() . "\n";
// } finally {
//     // Tutup koneksi
//     $conn = null;
// }
?>
