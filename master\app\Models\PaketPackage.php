<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaketPackage extends Model
{
    
    use HasFactory;
    protected $table = 'paket_package';

    // protected $casts = [
    //     'detail' => 'array', // ini penting agar otomatis decode JSON ke array
    // ];
     protected $fillable = [
        'name',
        'harga',
        'detail',

    ];

public function users()
{
    return $this->hasMany(User::class, 'paket_id', 'id');
}

}
