<?php
namespace App\Http\Controllers\home\employes;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\employe_groups as tblEmployeGroups;
use App\user_employes as tblUserEmployes;

class data extends Controller
{
    //
    public function listgroup(Request $request)
    {
        $getdata = tblEmployeGroups::where([
            'status'        =>  1
        ])
        ->get();

        $data = [
            'message'       =>  '',
            'response'      =>  $getdata
        ];
        
        return response()->json($data,200);
    }


    public function groups(Request $request)
    {
        $getdata = tblEmployeGroups::where([
            'status'        =>  1
        ])
        ->get();

        $data = [
            'message'       =>  '',
            'response'      =>  $getdata
        ];
        
        return response()->json($data,200);
    }


    public function list(Request $request)
    {
        $type = trim($request->type);
        $id = json_decode($request->id);
        $field = trim($request->field);
        $uid = trim($request->uid);

        //
        $getdata = tblUserEmployes::where([
            'status'        =>  1
        ]);
        if( $uid == "true" )
        {
            $getdata = $getdata->where([
                ['user_id', '!=', 0]
            ]);
        }
        if( $type == 'select')
        {
            $getdata = $getdata->whereIn($field, $id);
        }
        $getdata = $getdata->get();
        
        $data = [
            'message'       =>  '',
            'response'      =>  $getdata
        ];

        return $data;
    }

}