<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;





// class User extends Authenticatable implements JWTSubject
// {
//     use HasFactory, Notifiable;

//     /**
//      * The attributes that are mass-assignable.
//      *
//      * @var array<int, string>
//      */
//     protected $fillable = [
//         'name',
//         'email',
//         'password',
//     ];

//     /**
//      * The attributes that should be hidden for serialization.
//      *
//      * @var array<int, string>
//      */
//     protected $hidden = [
//         'password',
//         'remember_token',
//     ];

//     /**
//      * Get the attributes that should be cast.
//      *
//      * @return array<string, string>
//      */
//     protected function casts(): array
//     {
//         return [
//             'email_verified_at' => 'datetime',
//             'password' => 'hashed',
//         ];
//     }

//     /**
//      * Get the identifier that will be stored in the subject claim of the JWT.
//      *
//      * @return mixed
//      */
//     public function getJWTIdentifier()
//     {
//         return $this->getKey();
//     }

//     /**
//      * Return a key value array, containing any custom claims to be added to the JWT.
//      *
//      * @return array
//      */
//     public function getJWTCustomClaims()
//     {
//         return [];
//     }
// }

// last 
class User extends Authenticatable
{

    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'user_key',
        'password',
        'is_online',
        'paket_id',
        'usage_paket',
        'verification_code',
        
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
        ];
    }
    
      public function profile()
    {
        return $this->hasOne(Profile::class);
    }
    
    // Relasi banyak-ke-banyak dengan Project melalui tabel user_projects
    public function projects()
    {
        return $this->belongsToMany(Project::class, 'user_projects', 'user_id', 'project_id')
                    ->withTimestamps();
    }

    public function role()
    {
        return $this->hasOne(Role::class, 'role_id');
    }
    
  public function plan()
{
    return $this->belongsTo(PaketPackage::class, 'paket_id', 'id');
}

    
}
