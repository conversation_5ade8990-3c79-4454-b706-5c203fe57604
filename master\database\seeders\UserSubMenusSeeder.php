<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSubMenusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
              $user_id = 4; // ID pengguna yang akan mendapatkan akses penuh

        // Akses sub-menu untuk Dashboard
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 1, 'status' => 1],  // Akses 'Get Product'
            ['user_id' => $user_id, 'sub_menu_id' => 2, 'status' => 1],  // Akses 'Get Timeframe'
            ['user_id' => $user_id, 'sub_menu_id' => 3, 'status' => 1],  // Akses 'Get Province'
            ['user_id' => $user_id, 'sub_menu_id' => 4, 'status' => 1],  // Akses 'Analysis'
        ]);

        // Akses sub-menu untuk Orders
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 5, 'status' => 1],  // Akses 'Product Table'
            ['user_id' => $user_id, 'sub_menu_id' => 6, 'status' => 1],  // Akses 'Table'
            ['user_id' => $user_id, 'sub_menu_id' => 7, 'status' => 1],  // Akses 'Create'
            ['user_id' => $user_id, 'sub_menu_id' => 8, 'status' => 1],  // Akses 'Orders Verified'
            ['user_id' => $user_id, 'sub_menu_id' => 9, 'status' => 1],  // Akses 'Orders Unverified'
            ['user_id' => $user_id, 'sub_menu_id' => 10, 'status' => 1], // Akses 'New Order (Widget)'
            ['user_id' => $user_id, 'sub_menu_id' => 11, 'status' => 1], // Akses 'Update Price (Widget)'
            ['user_id' => $user_id, 'sub_menu_id' => 12, 'status' => 1], // Akses 'Update Qty (Widget)'
            ['user_id' => $user_id, 'sub_menu_id' => 13, 'status' => 1], // Akses 'Add Item (Widget)'
            ['user_id' => $user_id, 'sub_menu_id' => 14, 'status' => 1], // Akses 'Checkout'
            ['user_id' => $user_id, 'sub_menu_id' => 15, 'status' => 1], // Akses 'Submit'
            ['user_id' => $user_id, 'sub_menu_id' => 16, 'status' => 1], // Akses 'Courier List (Widget)'
            ['user_id' => $user_id, 'sub_menu_id' => 17, 'status' => 1], // Akses 'Courier Cost'
            ['user_id' => $user_id, 'sub_menu_id' => 18, 'status' => 1], // Akses 'Set Courier Cost'
            ['user_id' => $user_id, 'sub_menu_id' => 19, 'status' => 1], // Akses 'Payment Methods (List New)'
            ['user_id' => $user_id, 'sub_menu_id' => 20, 'status' => 1], // Akses 'Payment Methods'
            ['user_id' => $user_id, 'sub_menu_id' => 21, 'status' => 1], // Akses 'View Detail'
            ['user_id' => $user_id, 'sub_menu_id' => 22, 'status' => 1], // Akses 'Approve Order'
        ]);
        //
               // Akses sub-menu untuk Product
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 23, 'status' => 1],  // Akses 'Create New Product'
            ['user_id' => $user_id, 'sub_menu_id' => 24, 'status' => 1],  // Akses 'Add Product Variant'
            ['user_id' => $user_id, 'sub_menu_id' => 25, 'status' => 1],  // Akses 'Get Product Variant'
            ['user_id' => $user_id, 'sub_menu_id' => 26, 'status' => 1],  // Akses 'Edit Product'
            ['user_id' => $user_id, 'sub_menu_id' => 27, 'status' => 1],  // Akses 'Show Product'
            ['user_id' => $user_id, 'sub_menu_id' => 28, 'status' => 1],  // Akses 'Delete Product'
            ['user_id' => $user_id, 'sub_menu_id' => 29, 'status' => 1],  // Akses 'Product Table'
            ['user_id' => $user_id, 'sub_menu_id' => 30, 'status' => 1],  // Akses 'Get Gudang'
            
            // Akses ke Product Bulking
            ['user_id' => $user_id, 'sub_menu_id' => 31, 'status' => 1],  // Akses 'Product Bulking'

            // Sub-sub-menu untuk Product Bulking
            ['user_id' => $user_id, 'sub_menu_id' => 32, 'status' => 1], // Akses 'Import Product'
            ['user_id' => $user_id, 'sub_menu_id' => 33, 'status' => 1], // Akses 'Product Mapping'
            ['user_id' => $user_id, 'sub_menu_id' => 34, 'status' => 1], // Akses 'Preview Product'
            ['user_id' => $user_id, 'sub_menu_id' => 35, 'status' => 1], // Akses 'Store Product'
        ]);

        // Akses sub-menu untuk Report
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 36, 'status' => 1],  // Akses 'Advertiser Report List'
            ['user_id' => $user_id, 'sub_menu_id' => 37, 'status' => 1],  // Akses 'Dashboard Product Analysis'
            ['user_id' => $user_id, 'sub_menu_id' => 38, 'status' => 1],  // Akses 'Advertising Accounts List'
            ['user_id' => $user_id, 'sub_menu_id' => 39, 'status' => 1],  // Akses 'Get Advertising Account ID'
            ['user_id' => $user_id, 'sub_menu_id' => 40, 'status' => 1],  // Akses 'Create Report'
            ['user_id' => $user_id, 'sub_menu_id' => 41, 'status' => 1],  // Akses 'Update Report'
            ['user_id' => $user_id, 'sub_menu_id' => 42, 'status' => 1],  // Akses 'Delete Report'

            // Akses ke Report Metriks
            ['user_id' => $user_id, 'sub_menu_id' => 43, 'status' => 1], // Akses 'Report Metriks'

            // Sub-sub-menu untuk Report Metriks
            ['user_id' => $user_id, 'sub_menu_id' => 44, 'status' => 1], // Akses 'Table'
            ['user_id' => $user_id, 'sub_menu_id' => 45, 'status' => 1], // Akses 'Get Platform'
            ['user_id' => $user_id, 'sub_menu_id' => 46, 'status' => 1], // Akses 'Get Metric'
            ['user_id' => $user_id, 'sub_menu_id' => 47, 'status' => 1], // Akses 'Create'
            ['user_id' => $user_id, 'sub_menu_id' => 48, 'status' => 1], // Akses 'Get ID'
            ['user_id' => $user_id, 'sub_menu_id' => 49, 'status' => 1], // Akses 'Update'
            ['user_id' => $user_id, 'sub_menu_id' => 50, 'status' => 1], // Akses 'Delete'
        ]);
    
    //     Akses sub-menu untuk Customers
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 51, 'status' => 1], // Akses 'Export Customers Table'
            ['user_id' => $user_id, 'sub_menu_id' => 52, 'status' => 1], // Akses 'Import Customers Table'
            ['user_id' => $user_id, 'sub_menu_id' => 53, 'status' => 1], // Akses 'Get Segment'
            ['user_id' => $user_id, 'sub_menu_id' => 54, 'status' => 1], // Akses 'Filter Segment'
            ['user_id' => $user_id, 'sub_menu_id' => 55, 'status' => 1], // Akses 'Get Lokasi'
            ['user_id' => $user_id, 'sub_menu_id' => 56, 'status' => 1], // Akses 'Filter Lokasi'
            ['user_id' => $user_id, 'sub_menu_id' => 57, 'status' => 1], // Akses 'Add Customer'
            ['user_id' => $user_id, 'sub_menu_id' => 58, 'status' => 1], // Akses 'View/Edit Customer'
            ['user_id' => $user_id, 'sub_menu_id' => 59, 'status' => 1], // Akses 'Edit Customer'
            ['user_id' => $user_id, 'sub_menu_id' => 60, 'status' => 1], // Akses 'Delete Customer'
            ['user_id' => $user_id, 'sub_menu_id' => 61, 'status' => 1], // Akses 'Customer Detail'
        ]);
        // Akses sub-menu untuk Customers Bulking
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 62, 'status' => 1], // Akses 'Import'
            ['user_id' => $user_id, 'sub_menu_id' => 63, 'status' => 1], // Akses 'Mapping'
            ['user_id' => $user_id, 'sub_menu_id' => 64, 'status' => 1], // Akses 'Preview'
            ['user_id' => $user_id, 'sub_menu_id' => 65, 'status' => 1], // Akses 'Store'
        ]);
        // Akses sub-menu untuk Cohort Analysis
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 66, 'status' => 1], // Akses 'Cohort Analysis (Date Range)'
            ['user_id' => $user_id, 'sub_menu_id' => 67, 'status' => 1], // Akses 'Cohort Analysis (Product)'
        ]);
        // Akses sub-menu untuk CS-Broadcast
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 68, 'status' => 1], // Akses 'Get Phone'
            ['user_id' => $user_id, 'sub_menu_id' => 69, 'status' => 1], // Akses 'Get Tags'
            ['user_id' => $user_id, 'sub_menu_id' => 70, 'status' => 1], // Akses 'Get Segment'
            ['user_id' => $user_id, 'sub_menu_id' => 71, 'status' => 1], // Akses 'Broadcast Add'
            ['user_id' => $user_id, 'sub_menu_id' => 72, 'status' => 1], // Akses 'Broadcast Get'
            ['user_id' => $user_id, 'sub_menu_id' => 73, 'status' => 1], // Akses 'Broadcast Edit'
            ['user_id' => $user_id, 'sub_menu_id' => 74, 'status' => 1], // Akses 'Broadcast Update'
        ]);
        // Akses sub-menu untuk Customer Service
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 75, 'status' => 1], // Akses 'CS Setting'
            ['user_id' => $user_id, 'sub_menu_id' => 76, 'status' => 1], // Akses 'Add CS'
            ['user_id' => $user_id, 'sub_menu_id' => 77, 'status' => 1], // Akses 'CS Status'
            ['user_id' => $user_id, 'sub_menu_id' => 78, 'status' => 1], // Akses 'Delete CS'
            ['user_id' => $user_id, 'sub_menu_id' => 79, 'status' => 1], // Akses 'CS Pairing'
            ['user_id' => $user_id, 'sub_menu_id' => 80, 'status' => 1], // Akses 'CS Reload'
            ['user_id' => $user_id, 'sub_menu_id' => 81, 'status' => 1], // Akses 'Get CS'
            ['user_id' => $user_id, 'sub_menu_id' => 82, 'status' => 1], // Akses 'Get CS Detail'
            ['user_id' => $user_id, 'sub_menu_id' => 83, 'status' => 1], // Akses 'Edit CS'
        ]);
        // Akses sub-menu untuk Business
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 84, 'status' => 1], // Akses 'Edit Business'
            ['user_id' => $user_id, 'sub_menu_id' => 85, 'status' => 1], // Akses 'Save Business'
            ['user_id' => $user_id, 'sub_menu_id' => 86, 'status' => 1], // Akses 'Update VAT'
        ]);
        // Akses sub-menu untuk Integration
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 87, 'status' => 1], // Akses 'Get Public Token'
            ['user_id' => $user_id, 'sub_menu_id' => 88, 'status' => 1], // Akses 'Generate Token'
            ['user_id' => $user_id, 'sub_menu_id' => 89, 'status' => 1], // Akses 'Send Orders'
        ]);
        // Akses sub-menu untuk Gudang
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 90, 'status' => 1], // Akses 'Create Gudang'
            ['user_id' => $user_id, 'sub_menu_id' => 91, 'status' => 1], // Akses 'Get Gudang ID'
            ['user_id' => $user_id, 'sub_menu_id' => 92, 'status' => 1], // Akses 'Update Gudang'
            ['user_id' => $user_id, 'sub_menu_id' => 93, 'status' => 1], // Akses 'Delete Gudang'
        ]);
        // Akses sub-menu untuk Discount
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 94, 'status' => 1], // Akses 'Discount List'
            ['user_id' => $user_id, 'sub_menu_id' => 95, 'status' => 1], // Akses 'Discount Status'
            ['user_id' => $user_id, 'sub_menu_id' => 96, 'status' => 1], // Akses 'Create Discount'
        ]);
      // Akses sub-menu untuk MasterApi
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 97, 'status' => 1], // Akses 'Get Provinsi'
            ['user_id' => $user_id, 'sub_menu_id' => 98, 'status' => 1], // Akses 'Get City'
            ['user_id' => $user_id, 'sub_menu_id' => 99, 'status' => 1], // Akses 'Get Kecamatan'
            ['user_id' => $user_id, 'sub_menu_id' => 100, 'status' => 1], // Akses 'Update Address'
            ['user_id' => $user_id, 'sub_menu_id' => 101, 'status' => 1], // Akses 'Create Project'
        ]);
        // Akses sub-menu untuk Manage Project
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 102, 'status' => 1], // Akses 'Get Project by ID'
            ['user_id' => $user_id, 'sub_menu_id' => 103, 'status' => 1], // Akses 'Update Project'
            ['user_id' => $user_id, 'sub_menu_id' => 104, 'status' => 1], // Akses 'Delete Project'
        ]);
        // Akses sub-menu untuk TIM
        DB::table('user_sub_menus')->insert([
            ['user_id' => $user_id, 'sub_menu_id' => 105, 'status' => 1], // Akses 'Get Role'
            ['user_id' => $user_id, 'sub_menu_id' => 106, 'status' => 1], // Akses 'Create Role'
            ['user_id' => $user_id, 'sub_menu_id' => 107, 'status' => 1], // Akses 'Pengguna Page 1'
            ['user_id' => $user_id, 'sub_menu_id' => 108, 'status' => 1], // Akses 'Edit Pengguna ID'
            ['user_id' => $user_id, 'sub_menu_id' => 109, 'status' => 1], // Akses 'Update Pengguna'
            ['user_id' => $user_id, 'sub_menu_id' => 110, 'status' => 1], // Akses 'Delete Pengguna'
        ]);
        
        
    }
}
