<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PDF; // Atau: use Barryvdh\DomPDF\Facade\Pdf as PDF;

class PdfController extends Controller
{
 public function invoice()
    {
        // Ambil logo dari public/storage/RIPITLOGO.png dan encode base64
       $logo = null;
    $logoPath = public_path('storage/logo.jpg');

if (file_exists($logoPath)) {
    $mimeType = mime_content_type($logoPath);
    $logo = 'data:' . $mimeType . ';base64,' . base64_encode(file_get_contents($logoPath));
}

        // Data dummy
        $data = [
            'title' => 'Invoice #INV-0001',
            'date' => now()->format('d M Y'),
            'customer' => [
                'name' => 'Arfiyan Wahyu',
                'phone' => '0812-3456-7890',
                'email' => '<EMAIL>',
                'address' => 'Jl. Contoh Alamat No. 123, Jakarta',
            ],
            'items' => [
                ['name' => 'Produk A', 'qty' => 2, 'price' => 50000],
                ['name' => 'Produk B', 'qty' => 1, 'price' => 75000],
            ],
            'logo' => $logo
        ];

        $pdf = PDF::loadView('pdf.invoice', $data)
        ->setOptions([
            'isRemoteEnabled' => true,
            'isHtml5ParserEnabled' => true,
        ]);

        return $pdf->download('invoice.pdf');
    }
}
