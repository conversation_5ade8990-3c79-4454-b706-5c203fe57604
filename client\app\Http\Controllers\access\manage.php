<?php

namespace App\Http\Controllers\access;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Tymon\JWTAuth\Manager;
use App\Http\Controllers\config\index as Config;
// use Auth;
use App\users as tblUsers;
use App\user_logins as tblUserlogins;
use App\user_registers as tblRegisters;
use App\reset_passwords as tblResetPasswords;
use App\reset_password as tblResetPassword;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Str; // Import Str class untuk generate token
use Illuminate\Support\Facades\Auth;
use App\users;
use Illuminate\Support\Facades\Hash;

// exception_
use Exception;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Tymon\JWTAuth\Contracts\JWTSubject;

// Start the session

class manage extends Controller
{
    //new api 
    public function daftar(Request $request)
    {
        // Validasi request
        // Validasi request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8',
            'username' => 'required|string|max:255|unique:users,username',
            'company_id' => 'required|integer',
            'level' => 'required|string|max:255',
            'sub_level' => 'required|string|max:255',
            'gender' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'phone_code' => 'required|string|max:255',
        ]);

        // Jika validasi gagal
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }
        $token = Str::random(60); // Generate token
        // Buat entri baru dalam tabel pengguna
        $userId = DB::table('users')->insertGetId([
            'id' => DB::table('users')->max('id') + 1,
            'search' => $request->input('name') . ' ' . $request->input('email') . ' ' . $request->input('phone'),
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            //token diisi dengan token yang sudah digenerate
            'token' => $token,
            'password' => app('hash')->make($request->input('password')), // Menggunakan app('hash')->make() untuk hash password
            'username' => $request->input('username'),
            'company_id' => $request->input('company_id'),
            'level' => $request->input('level'),
            'sub_level' => $request->input('sub_level'),
            'gender' => $request->input('gender'),
            'phone' => $request->input('phone'),
            'phone_code' => $request->input('phone_code'),
            'status' => 1, // Set default status to 1
            'registers' => 1, // Set default registers to 1
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
        $id = DB::table('users')
            ->latest('id')
            ->pluck('id')
            ->first();


        // Response sukses
        return response()->json([
            'message' => 'User berhasil didaftarkan',
            'user_id' => $id,
            'code' => 1
        ], 201);
    }

    //masuk 

    public function masuk(Request $request)
    {
        // Ambil token dari header
        $token = $request->header('key');
        $userId = DB::table('users')->where('token', $token)->value('id');
        $user = users::find($userId);
        if ($user != null) {
            Auth::login($user);
            $status_login = Auth::check();
            $message = $status_login ? 'Berhasil login' : 'Gagal login';
            $data_login = [
                'user_id' => $user->id,
                'username' => $user->username,
            ];
            $true_login = hash('sha256', json_encode($data_login));
            return response()->json([
                'message' => $message,
                "response" => [
                    "account" => $user,
                    "token" => $true_login,
                ],
                'code' => $status_login ? 1 : 0,
                'status' => $status_login ? '200 OK' : '401 Unauthorized',
            ], 200);
        } else {
            return response()->json(['error' => 'Token tidak valid'], 401);
        }
    }
    //keluar 
    public function keluar(Request $request)
    {
        $token = $request->header('token');
        // Cari pengguna berdasarkan token
        $user = users::where('token', $token)->first();
        if ($user) {
            // Lakukan logout pengguna jika pengguna ditemukan
            Auth::logout();

            // Sekarang pengguna sudah logout, Anda dapat memberikan respons yang sesuai
            return response()->json(['message' => 'Berhasil logout'], 200);
        } else {
            // Jika token tidak valid atau pengguna tidak ditemukan, kembalikan respons dengan pesan kesalahan
            return response()->json(['error' => 'Token tidak valid'], 401);
        }
    }




    //manage login
    public function login(Request $request)
    {

        $Config = new Config;

        //ceking field jika tidak lengkap maka munculkan error
        if (!isset($request->email) || !isset($request->password)) {
            $data = [
                'message'   =>  'Kolom Email atau Kolom Password tidak lengkap',
                'response'  =>  [
                    'focus'     =>  ['email', 'password']
                ]
            ];

            return response()->json($data, 401);
        }


        //ceking email password
        $ceklogin = tblUsers::where([
            'email'     =>  trim($request->email)
        ])->first();

        if ($ceklogin == null) {

            $data = [
                'message'       =>  'Alamat email tidak terdaftar',
                'response'      =>  [
                    'focus'         =>  ['email']
                ]
            ];

            return response()->json($data, 401);
        }


        // IF EMAIL TRUE


        $pwd = $ceklogin->password;
        $cekpwd = $pwd === '' ? 1 : (Hash::check($request->password, $pwd) ? 1 : 0);

        //CHECK PASSWORD
        //IF WRONG PASSWORD
        if ($cekpwd == 0) {
            $data = [
                'message'       =>  'Harap periksa kembali password Anda!',
                'response'      =>  [
                    'focus'         =>  ['password']
                ]
            ];

            return response()->json($data, 401);
        }


        //CHECK VISIT PAGE

        //ERROR: IF OPEN PAGE ADMIN 
        if ($request->level == '0' && $ceklogin->level != 1) {
            $data = [
                'message'       =>  'Maaf akses Anda tidak di ijinkan!',
                'response'      =>  [
                    'focus'         =>  ['email']
                ]
            ];

            return response()->json($data, 401);
        }

        //ERROR: IF OPEN PAGE CRM

        if ($request->level == '1') {

            $checkLevel = $Config->checkLevelLogin($ceklogin->level);
            // if not level 2,3,4

            if (!$checkLevel) {
                $data = [
                    'message'       =>  'Maaf akses Anda tidak di ijinkan!',
                    'response'      =>  [
                        'focus'         =>  ['email']
                    ]
                ];

                return response()->json($data, 401);
            }
        }

        //cek status
        if ($ceklogin->status != 1) {
            $data = [
                'message'       =>  'Akun Anda ditangguhkan!',
                'response'      =>  [
                    'focus'         =>  ['email']
                ]
            ];

            return response()->json($data, 401);
        }

        //not register
        if ($ceklogin->registers == 0) {
            // $getregister = tblRegisters::where([

            $data = [
                'message'       =>  'Akun Anda belum di verifikasi, <a href="/registers/success?token=' . $ceklogin->token . '">Verifikasi sekarang?</a>',
                'focus'         =>  [
                    'focus'         =>  ['email']
                ]
            ];

            return response()->json($data, 401);
        }


        //login
        //buat token JWT
        $datalogin = [
            'email'         =>  trim($request->email),
            'password'      =>  trim($request->password),
            'info'          =>  $request['info'],
            'apps'          =>  $request->level
        ];

        $truelogin = $this->truelogin($datalogin);

        if ($truelogin['message'] != '') {
            return response()->json($truelogin['message'], 401);
        }


        return response()->json($truelogin, 200);
    }

    //cekstatus
    public function cekstatus(Request $request)

    {
        $key = $request->header('key');
        // login dengan  key 
        $users = users::where('token', $key)->first();
        if ($users) {
            $data = [
                'message'       =>  'Token valid',
                'response'      =>  [
                    'account'        => $users,
                ],
                'code'          =>  1,
                'status'        =>  '200 OK',
            ];
            return response()->json($data, 200);
        } else {
            // Jika token tidak valid atau pengguna tidak ditemukan, kembalikan respons dengan pesan kesalahan
            return response()->json(['error' => 'Token tidak valid'], 401);
        }
    }
    //true login
    public function truelogin($request)
    {
        //request
        $credentials = [
            'email'     =>  $request['email'],
            'password'  =>  $request['password']
        ];
        $token = $this->guard()->attempt($credentials);

        if ($token == false) {
            $data = [
                'message'       =>  'Proses login gagal'
            ];

            return $data;
        }

        //account

        $account = new \App\Http\Controllers\account\index;
        $account = $account->show($this->guard()->user());

        $datalogins = [
            'account'       =>  [
                'id'            =>  $account['id']
            ],
            'token'         =>  $token,
            'info'          =>  $request['info']
        ];


        //create new log in table logins
        $logins = new \App\Http\Controllers\log\access\manage;
        $logins = $logins->logins($datalogins);

        $cookie = [
            'account'       =>  $account,
            'token'         =>  $token
        ];

        $data = [
            'message'       =>  '',
            'response'      =>  [
                'cookie'        =>  $cookie,
                'homepage'      =>  $request['apps'] === '0' ? '/home' : '/dashboard'
            ]
        ];

        return $data;
    }


    //manage logout
    //cek expire session token
    public function logout(Request $request)
    {


        // $token = trim($request->token);

        // $logout = new \App\Http\Controllers\log\access\manage;
        // $logout = $logout->logout($token);

        // //logout 
        // $this->guard()->logout();

        //response
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'redirect'      =>  '/login'
            ]
        ];

        return response()->json($data, 200);
    }

    // signup
    public function signup(Request $request)
    {


        $name = trim($request->name);
        $email = trim($request->email);
        $password = trim($request->password);

        $cekemail = tblUsers::where([
            'email'         =>  $email
        ])->first();

        if ($cekemail != null) {

            //cek registers
            if ($cekemail->status != 1) {
                $data = [
                    'message'       =>  'Akun anda ditangguhkan!'
                ];
            } else {

                if ($cekemail->registers == 0) {
                    // $getregisters = tblRegisters::where([
                    //     'user_id'       =>  $cekemail->id,
                    //     'status'        =>  1
                    // ])->first();

                    $data = [
                        'message'           =>  'Email telah terdaftar dan belum verifikasi akun, <a href="/registers/success?token=' . $cekemail->token . '">Verifikasi sekarang?</a>'
                    ];
                } else {
                    $data = [
                        'message'           =>  'Email telah terdaftar, <a href="/login">masuk akun Anda?</a>'
                    ];
                }
            }

            return response($data, 401);
        } else {


            //insert table user
            $addtbluser = new \App\Http\Controllers\account\signup;
            $addtbluser = $addtbluser->main($request);


            return $addtbluser;

            if ($addtbluser['message'] == '') {
                $data = [
                    'redirect'      =>  $addtbluser['response']
                ];
            } else {
                $data = [
                    'message'      =>  $addtbluser['message']
                ];
            }
            return response()->json($data, $addtbluser['message'] === '' ? 200 : 401);
        }
    }

    //reset password
    public function resetpassword(Request $request)
    {

        // config
        $Config = new Config;


        // request
        $email = trim($request->email);


        //start
        $cekemail = tblUsers::where([
            'email'         =>  $email
        ])->first();

        if ($cekemail == null) {

            $data = [
                'message'           =>  'Email tidak terdaftar'
            ];

            return response()->json($data, 404);
        } else {

            //ERROR: IF OPEN PAGE ADMIN 
            if ($request->level == '0' && $ceklogin->level != 1) {
                $data = [
                    'message'       =>  'Maaf akses Anda tidak di ijinkan!'
                ];

                return response()->json($data, 401);
            }

            //ERROR: IF OPEN PAGE CRM
            if ($request->level == '1') {

                $checkLevel = $Config->checkLevelLogin($cekemail->level);
                // if not level 2,3,4

                if (!$checkLevel) {
                    $data = [
                        'message'       =>  'Maaf akses Anda tidak di ijinkan!',
                        'response'      =>  [
                            'focus'         =>  ['email']
                        ]
                    ];

                    return response()->json($data, 401);
                }
            }

            //cek registers
            if ($cekemail->status != 1) {
                $data = [
                    'message'       =>  'Akun anda ditangguhkan!'
                ];

                return response()->json($data, 401);
            }

            if ($cekemail->registers == 0) {
                $getregisters = tblRegisters::where([
                    'user_id'       =>  $cekemail->id,
                    'status'        =>  1
                ])->first();

                $data = [
                    'message'           =>  'Email telah terdaftar dan belum verifikasi akun, <a href="/account/verification?token=' . $getregisters->token . '">verifikasi sekarang?</a>'
                ];

                return response()->json($data, 401);
            }


            //ceklimit
            $thisday = date('Y-m-d', time());
            $ceklimit = tblResetPasswords::where([
                ['user_id',     '=',    $cekemail->id],
                ['created_at',  'like', '%' . $thisday . '%']
            ])->count();

            //keep limit if maxlength > 2
            if ($ceklimit > 2) {
                $data = [
                    'message'       =>  'Permintaan perubahan password dibatasi hanya boleh 3x dalam 1 hari'
                ];

                return response()->json($data, 401);
            }



            //next process
            $dataresetpassword = [
                'user_id'           =>  $cekemail->id,
                'user_level'        =>  $request->level, //1 apps, 2 produsen, 3 distributor
                'email'             =>  $cekemail->email,
                'name'              =>  $cekemail->name,
                'info'              =>  $request->info
            ];

            //ADD table reset password
            $addresetpassword = new \App\Http\Controllers\models\access;
            $addresetpassword = $addresetpassword->resetpassword($dataresetpassword);


            return response()->json([
                'message'       =>  '',
                'response'      =>  'Permintaan perubahan Password berhasil dikirim ke alamat email ' . $email
            ], 200);
        }
    }


    // refresh token
    public function refresh()
    {

        $account = new \App\Http\Controllers\account\index;
        $account = $account->show($this->guard()->user());


        $gettoken = tblUserlogins::where([
            'user_id'       =>  $account['id'],
            'status'        =>  1
        ])->first();

        $data = [
            'refresh'      =>  [
                'account'           =>  $account,
                'token'             =>  $gettoken->token_jwt,
                'check'             =>   $this->guard()->check()
            ]
        ];

        return $data;
    }



    // public function refreshJWT($request)
    // {

    //     $getaccount = new \App\Http\Controllers\account\index;
    //     $getaccount = $getaccount->viewtype([
    //         'type'      =>  'key',
    //         'token'     =>  $request->header('key')
    //     ]);

    //     $gettoken = tblUserlogins::where([
    //         'user_id'       =>  $account['id'],
    //         'status'        =>  1
    //     ])->first();

    //     $data = [
    //         'refresh'      =>  [
    //             'account'           =>  $account,
    //             'token'             =>  $gettoken->token_jwt
    //         ]
    //     ];

    //     return $data;
    // }

    public function guard()
    {
        return app('auth')->guard();
    }


    public function profile()
    {
        $refresh = $this->refresh();

        $data = [
            'message'       =>  '',
            'response'      =>  'response',
            'refresh'       =>  $refresh
        ];


        return response()->json($data, 200);
    }
}
