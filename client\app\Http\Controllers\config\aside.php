<?php
namespace App\Http\Controllers\config;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\users as tblUsers;
use App\user_configs as tblUserConfigs;
use DB;

class aside extends Controller
{


    //view aside
    public function view(Request $request)
    {
        $userid = $request['id'];

        //
        $getuser = tblUsers::from('users as u')
        ->select(
            'u.id',
            'uc.aside_menu'
        )
        ->leftJoin('user_configs as uc', function($join)
        {
            $join->on('uc.user_id', '=', 'u.id');
        })
        ->where([
            'u.id'        =>  $userid
        ])
        ->first();

        $dt = json_decode($getuser->aside_menu);

        $data = [
            'response'  =>  [
                'menu'          =>  $dt
            ]
        ];

        return response()->json($data, 200);
    }

    //
    public function test(Request $request)
    {
        $data = [
            'level'         =>  $request->level,
            'sublevel'      =>  $request->sublevel,
            'user_id'       =>  $request->id
        ];

        $create = $this->createaside($data);


        $update = tblUserConfigs::where([
            'user_id'           =>  $request->id
        ])
        ->update([
            'aside_menu'        =>  json_encode($create)
        ]);

        return $create;

    }

    public function viewSingle(Request $request)
    {
        $data = [
            'level'         =>  $request->level,
            'sublevel'      =>  $request->sublevel,
        ];

        $create = $this->createaside($data);

        return response()->json($create, 200);
    }

    //crate aside
    public function createaside($request)
    {
        $level = $request['level'];
        $sublevel = $request['sublevel'];



        // $asidelevel = $level === '2' ? $this->produsen() : ( $level === '3' ? $this->distributor() : $this->maklon() );
        // $asidelevel = $level === '2' ? $this->produsen() : ( $level === '3' ? $this->distributor() : $this->reseller() );

        if( $level == '2'){
            $asidelevel = $this->produsen();
        }elseif( $level == '3'){
            $asidelevel = $this->distributor();
        }else{
            $asidelevel = $this->reseller();
        }
        //level 2 for produsen
        //level 3 distributor
        // level 4 maklon

        $data = [
            'menu'      =>  $asidelevel[$sublevel]['menu'],
            'submenu'   =>  $asidelevel[$sublevel]['submenu']
        ];


        //getaside template
        $asidetemp = $this->tempaside($data);

        // $update = tblUserConfigs::where([
        //     'user_id'           =>  $request['user_id']
        // ])
        // ->update([
        //     'aside_menu'        =>  json_encode($asidetemp)
        // ]);

        return $asidetemp;
    }


    public function tempaside($request)
    {
        $menu = explode(",", $request['menu']);
        $submenu = $request['submenu'];

        //
        $cmenu = $this->menu();
        $csubmenu = $this->submenu();

        //
        foreach($menu as $m)
        {
            $vmenu = $cmenu[$m];

            if( $vmenu['type'] != '')
            {
                $vsubmenu = explode(",", $submenu[$m]);
                $child = [];

                foreach($vsubmenu as $s)
                {
                    
                    $child[] = [
                        'title'         =>  $csubmenu[$s]['title'],
                        'url'           =>  $csubmenu[$s]['url'],
                    ];
                }
            }
            else
            {
                $child = '';
            }

            //
            $list[] = [
                'title'     =>  $vmenu['title'],
                'type'      =>  $vmenu['type'],
                'url'       =>  $vmenu['url'],
                'arrow'     =>  $vmenu['arrow'],
                'icon'      =>  $vmenu['icon'],
                'child'     =>  $child
            ];
        }

        //
        return $list;
    }

    //level produsen
    public function produsen()
    {
        $data = [
            '1'         =>  [ //administator
                'menu'          =>  'dashboard,marketing,mg-customers,admin,production,pengaturan',
                'submenu'       =>  [
                    'marketing'     =>  'orders,orderdistributor,uploadorders',
                    'mg-customers'  =>  'customers,uploadcustomers',
                    'admin'         =>  'veriforders,verifbulking,verifod,shiping',
                    'production'    =>  'product',
                    'pengaturan'    =>  'pengguna,partner,manageglobal'
                ]
            ],
            '2'         =>  [ //supervisor
                'menu'          =>  'dashboard,marketing,mg-customers,admin,production,pengaturan',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'mg-customers'  =>  'customers',
                    'admin'         =>  'veriforders,verifbulking,shiping',
                    'production'    =>  'product',
                    'pengaturan'    =>  'pengguna,partner,manageglobal'
                ]
            ],
            '3'         =>  [ //customer service
                'menu'          =>  'dashboard,marketing,mg-customers',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'mg-customers'  =>  'customers',
                    // 'admin'         =>  'veriforders,verifbulking,verifod',
                ]
            ],
            '4'         =>  [ //admin product
                'menu'          =>  'dashboard,production',
                'submenu'       =>  [
                    'production'    =>  'product,stockproduct'
                ]
            ],
            '5'         =>  [ //admin payment
                'menu'          =>  'dashboard,admin',
                'submenu'       =>  [
                    // 'marketing'     =>  'orders,orderdistributor',
                    'admin'         =>  'veriforders,verifbulking,shiping',
                ]
            ],
            '6'         =>  [ //admin shiping
                'menu'          =>  'dashboard,admin',
                'submenu'       =>  [
                    'admin'         =>  'shiping',
                ]
            ],
            '7'         =>  [ //admin Data
                'menu'          =>  'dashboard,manage-data',
                'submenu'       =>  [
                    'manage-data'     =>  'data-customer,data-cs'
                ]
            ],
            '8'         =>  [ //CS CRM
                'menu'          =>  'dashboard,manage-lead',
                'submenu'       =>  [
                    'manage-lead'     =>  'data-lead'
                ]
            ],
        ];

        return $data;

    }

    //level distributor
    public function distributor()
    {
        $data = [
            '1'         =>  [ //Administrator
                'menu'          =>  'dashboard,marketing,mg-customers,stock,admin,pengaturan',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'mg-customers'  =>  'customers',
                    'stock'         =>  'orderstock',
                    'admin'         =>  'veriforders,paymentbulking,shiping',
                    'pengaturan'    =>  'pengguna,manageglobal'
                ]
            ],
            '2'         =>  [ //supervisor
                'menu'          =>  'dashboard,marketing,mg-customers,stock,admin,pengaturan',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'mg-customers'  =>  'customers',
                    'stock'         =>  'orderstock',
                    'admin'         =>  'veriforders,paymentbulking,shiping',
                    'pengaturan'    =>  'pengguna,manageglobal'
                ]
            ],
            '3'         =>  [ //marketing
                'menu'          =>  'dashboard,marketing,mg-customers',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'mg-customers'  =>  'customers',
                ]
            ],
            '5'         =>  [
                'menu'          =>  'dashboard,marketing,stock,admin',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'stock'         =>  'orderstock',
                    'admin'         =>  'veriforders,paymentbulking,shiping'
                ]
            ],
            '6'         =>  [
                'menu'          =>  'dashboard,admin',
                'submenu'       =>  [
                    'admin'         =>  'shiping',
                ]
            ]
        ];

        return $data;
    }

    //level maklon
    public function maklon()
    {
        $data = [
            '1'         =>  [ //administator
                'menu'          =>  'dashboard,marketing,admin,production,pengaturan',
                'submenu'       =>  [
                    'marketing'     =>  'orders,customers',
                    'admin'         =>  'veriforders,verifbulking,shiping',
                    'production'    =>  'product,stockproduct',
                    'pengaturan'    =>  'pengguna,partner,manageglobal'
                ]
            ],
            '2'         =>  [
                'menu'          =>  'dashboard,marketing,admin,production,pengaturan',
                'submenu'       =>  [
                    'marketing'     =>  'orders,customers',
                    'admin'         =>  'veriforders,verifbulking,shiping',
                    'production'    =>  'product,stockproduct',
                    'pengaturan'    =>  'pengguna,partner,manageglobal'
                ]
            ],
            '3'         =>  [
                'menu'          =>  'dashboard,marketing',
                'submenu'       =>  [
                    'marketing'     =>  'orders,customers'
                ]
            ],
            '4'         =>  [
                'menu'          =>  'dashboard,production',
                'submenu'       =>  [
                    'production'    =>  'product,stockproduct'
                ]
            ],
            '5'         =>  [
                'menu'          =>  'dashboard,marketing,admin',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'admin'         =>  'veriforders,verifbulking,shiping',
                ]
            ],
            '6'         =>  [
                'menu'          =>  'dashboard,admin',
                'submenu'       =>  [
                    'admin'         =>  'shiping',
                ]
            ],
        ];

        return $data;

    }

    //level reseller 
    public function reseller(){
        $data = [
            '1'         =>  [
                'menu'          =>  'dashboard,marketing,mg-customers',
                'submenu'       =>  [
                    'marketing'     =>  'orders',
                    'mg-customers'  =>  'customers,history-customers'
                ]
            ]
        ];

        return $data;
    }

    //menu
    public function menu()
    {

        $data = [
            'dashboard'     =>  [
                'title'         =>  'Dashboard',
                'icon'          =>  'icon fa flaticon2-line-chart',
                'type'          =>  '',
                'arrow'         =>  '',
                'url'           =>  '/dashboard',
                'child'         =>  ''
            ],//end dashboard
            'marketing'     =>  [
                'title'         =>  'Kelola Pesanan',
                'icon'          =>  'icon fa flaticon-businesswoman',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ], //end marketing
            'stock'         =>  [
                'title'         =>  'Stock Barang',
                'icon'          =>  'icon fa flaticon2-open-box',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ], //end stock
            'admin'             =>  [
                'title'         =>  'Admin',
                'icon'          =>  'icon sli_icon-users',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ], //end admin
            'pengaturan'             =>  [
                'title'         =>  'Pengaturan',
                'icon'          =>  'icon fa flaticon-cogwheel-1',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ], //end pengaturan
            'production'             =>  [
                'title'         =>  'Kelola Produk',
                'icon'          =>  'icon sli_icon-social-dropbox',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ], //end pengaturan
            'manage-data'   =>  [
                'title'         =>  'Kelola Data',
                'icon'          =>  'icon sli_icon-social-dropbox',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ],
            'manage-lead'   =>  [
                'title'         =>  'Kelola Lead',
                'icon'          =>  'icon sli_icon-social-dropbox',
                'type'          =>  'collaps',
                'arrow'         =>  'icon icon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ],
            'mg-customers'   =>  [
                'title'         =>  'Kelola Customers',
                'icon'          =>  'icon sli_icon-users',
                'type'          =>  'collaps',
                'arrow'         =>  'icon iscon-keyboard_arrow_down arrow-icon',
                'child'         =>  '',
                'url'           =>  ''
            ]
        ];


        return $data;
        

    }

    //sub menu
    public function submenu()
    {

        $data = [
            'orders'         =>  [ //marketing
                'title'             =>  'Pesanan',
                'url'               =>  '/dashboard/orders'
            ],
            'orderdistributor'         =>  [ //marketing
                'title'             =>  'Pesanan Distributor',
                'url'               =>  '/dashboard/order/distributor'
            ],
            'verifod'         =>  [ //marketing
                'title'             =>  'Verifikasi Pesanan Distributor',
                'url'               =>  '/dashboard/verif-order-distributor'
            ],
            'customers'         =>  [ //marketing
                'title'             =>  'Customer',
                'url'               =>  '/dashboard/customers'
            ],
            'history-customers'         =>  [ //marketing
                'title'             =>  'History Customer',
                'url'               =>  '/dashboard/customers/history'
            ],
            'veriforders'        =>  [ //admin produsen, distributor
                'title'             =>  'Verifikasi Pembayaran',
                'url'               =>  '/dashboard/veriforders'
            ],
            'paymentbulking'        =>  [ //admin distributor
                'title'             =>  'Pembayaran Bulking',
                'url'               =>  '/dashboard/bulkingpayment'
            ],
            'verifbulking'        =>  [ //admin produsen
                'title'             =>  'Verifikasi Bulking',
                'url'               =>  '/dashboard/verifbulking'
            ],
            'shiping'        =>  [ //admin produsen, distributor
                'title'             =>  'Shiping',
                'url'               =>  '/dashboard/shiping'
            ],
            'orderstock'        =>  [ //admin distributor
                'title'             =>  'Order Stock',
                'url'               =>  '/dashboard/orderstock'
            ],
            'pengguna'        =>  [ //pengaturan produsen, distributor
                'title'             =>  'Pengguna',
                'url'               =>  '/dashboard/pengguna'
            ],
            'partner'        =>  [ //pengaturan produsen
                'title'             =>  'Partner',
                'url'               =>  '/dashboard/partner'
            ],
            'manageglobal'          =>  [ //pengaturan produsen, distributor
                'title'             =>  'Pengaturan Umum',
                'url'               =>  '/dashboard/pengaturan-umum'
            ],
            'stockproduct'          =>  [ //production produsen
                'title'             =>  'Stock Produk',
                'url'               =>  '/dashboard/stockproduct'
            ],
            'product'           =>  [ //production produsen
                'title'             =>  'Produk',
                'url'               =>  '/dashboard/product'
            ],
            'prepare'           =>  [ //production produsen
                'title'             =>  'Prepare',
                'url'               =>  '/dashboard/prepare'
            ],
            'data-customer'         =>  [ //marketing
                'title'             =>  'Data Customers',
                'url'               =>  '/dashboard/data-customer'
            ],
            'data-cs'         =>  [ //marketing
                'title'             =>  'Data CS',
                'url'               =>  '/dashboard/data-cs'
            ],
            'data-lead'         =>  [ //marketing
                'title'             =>  'Data Lead',
                'url'               =>  '/dashboard/data-leads'
            ],
            'uploadorders'         =>  [ //upload order
                'title'             =>  'Upload Order',
                'url'               =>  '/dashboard/orders/uploads'
            ],
            'uploadcustomers'         =>  [ //upload customers
                'title'             =>  'Upload Customers',
                'url'               =>  '/dashboard/customers/uploads'
            ]
        ];


        return $data;
    }

}