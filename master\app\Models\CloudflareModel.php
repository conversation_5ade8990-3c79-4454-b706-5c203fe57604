<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CloudflareModel extends Model
{
    public $bearer;
    public $zoneID;
    public $globalkey, $cakey, $email;

    public function __construct()
    {
        $this->email     = '<EMAIL>';
        $this->bearer    = 'bXs2BRI1SIzFpPMYl3zAj3Ho1BE3L5wUXKvkRQPo';
        $this->zoneID    = '73423710c98b9808ec08464aba68f49b';
        $this->globalkey = '15d5ce07ea69b813f4a5fb49533acf79195b6';
        $this->cakey     = 'v1.0-41eb9dbdfb5a5c6edc35c782-05396506375e07d32698fb6c5bab63fb114744493634c0b0ba6f930859a967db850f7c61a2684fe80cf0af8ac5c4b54bf41893c73b4ecd892d5e0cdccb949766cd1661ee4b2ab2ab';
    }

    public function addDNS($recordType, $recordName, $recordContent, $ttl = 1, $proxied = true)
    {
        $headers = [
            'X-Auth-Email: ' . $this->email,
            'X-Auth-Key: ' . $this->globalkey,
            'Authorization: ' . $this->bearer,
            'X-Auth-User-Service-Key: ' . $this->cakey,
            'Content-Type: application/json',
        ];

        $data = [
            'type'    => $recordType,
            'name'    => $recordName,
            'content' => $recordContent,
            'ttl'     => $ttl,
            'proxied' => $proxied,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }

    public function getDNS()
    {
        $headers = [
            'X-Auth-Email: ' . $this->email,
            'X-Auth-Key: ' . $this->globalkey,
            'Authorization: ' . $this->bearer,
            'X-Auth-User-Service-Key: ' . $this->cakey,
            'Content-Type: application/json',
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    public function editDNS($recordID, $recordType, $recordName, $recordContent, $ttl = 1, $proxied = true)
    {
        $headers = [
            'X-Auth-Email: ' . $this->email,
            'X-Auth-Key: ' . $this->globalkey,
            'Authorization: ' . $this->bearer,
            'X-Auth-User-Service-Key: ' . $this->cakey,
            'Content-Type: application/json',
        ];

        $data = [
            'type'    => $recordType,
            'name'    => $recordName,
            'content' => $recordContent,
            'ttl'     => $ttl,
            'proxied' => $proxied,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records/{$recordID}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $response = curl_exec($ch);
        curl_close($ch);

        return $response;
    }
    //cek dns_records
    public function cekDNS($recordName)
{
    $headers = [
        'X-Auth-Email: ' . $this->email,
        'X-Auth-Key: ' . $this->globalkey,
        'Authorization: ' . $this->bearer,
        'X-Auth-User-Service-Key: ' . $this->cakey,
        'Content-Type: application/json',
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records?name={$recordName}");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}
public function deleteDNSByRecordName($recordName) {
    // Step 1: Check if the DNS record exists and retrieve its ID
    $dnsRecords = $this->cekDNS($recordName); // Fetch DNS records with the given name

    if (!isset($dnsRecords['result']) || empty($dnsRecords['result'])) {
        return [
            'status' => false,
            'message' => "DNS record with name '$recordName' not found."
        ];
    }

    // Assuming you want to delete the first matched DNS record
    $recordID = $dnsRecords['result'][0]['id'];

    // Step 2: Delete the DNS record using the record ID
    $deleteResponse = $this->deleteDNS($recordID);

    if (isset($deleteResponse['success']) && $deleteResponse['success']) {
        return [
            'status' => true,
            'message' => "DNS record '$recordName' deleted successfully."
        ];
    } else {
        return [
            'status' => false,
            'message' => "Failed to delete DNS record '$recordName'. Error: " . ($deleteResponse['errors'][0]['message'] ?? 'Unknown error')
        ];
    }
}


public function deleteDNS($recordID)
{
    // Header yang digunakan untuk autentikasi dengan API Cloudflare
    $headers = [
        'X-Auth-Email: ' . $this->email,
        'X-Auth-Key: ' . $this->globalkey,
        'Authorization: ' . $this->bearer,
        'X-Auth-User-Service-Key: ' . $this->cakey,
        'Content-Type: application/json',
    ];

    // Inisialisasi cURL
    $ch = curl_init();
    
    // Mengatur URL endpoint API untuk menghapus record DNS
    curl_setopt($ch, CURLOPT_URL, "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records/{$recordID}");
    
    // Mengatur opsi cURL untuk mengembalikan hasil sebagai string
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    // Mengatur header untuk permintaan cURL
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    // Mengatur metode HTTP untuk DELETE
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
    
    // Mengeksekusi permintaan cURL dan menyimpan responsnya
    $response = curl_exec($ch);
    
    // Menutup sesi cURL
    curl_close($ch);

    // Mengembalikan respons yang diterima dari API
    return json_decode($response, true);
}


}
