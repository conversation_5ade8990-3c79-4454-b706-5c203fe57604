<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('broadcasts', function (Blueprint $table) {
            $table->id();
            $table->string('act');
            $table->string('phone')->default('0');
            $table->string('phone_cs');
            $table->string('file_url')->nullable();
            $table->string('type'); // text, jpg, png, etc
            $table->string('file_bc')->nullable();

            $table->integer('count_process')->default(5);
            $table->integer('delay_process')->default(5); // in minutes
            $table->integer('hour_start')->default(8);
            $table->integer('hour_end')->default(17);
            $table->string('audience')->nullable()->default(null);
            $table->string('success')->nullable()->default(null);
            $table->string('fail')->nullable()->default(null);
            $table->string('replay')->nullable()->default(null);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->enum('status', ['pending', 'processing', 'done'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('broadcasts');
    }
};
