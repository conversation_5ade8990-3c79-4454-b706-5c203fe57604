<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Broadcast extends Model
{
   protected $fillable = [
        'act',
        'phone',
        'phone_cs',
        'file_url',
        'type',
        'file_bc',
        'count_process',
        'delay_process',
        'hour_start',
        'hour_end',
        'audience',
        'success',
        'fail',
        'replay',
        'start_date',
        'end_date',
        'status',
    ];
    //
}
