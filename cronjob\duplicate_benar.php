<?php

function getLargestClientFolder() {
    // Direktori tempat folder client berada
    $baseDir = '/www/wwwroot/';
    
    // Ambil semua folder yang sesuai dengan pola \d+client.ripit.id
    $folders = array_filter(glob($baseDir . '*client.ripit.id'), 'is_dir');
    
    $maxNumber = 0;

    foreach ($folders as $folder) {
        // Ambil nama folder saja tanpa path
        $folderName = basename($folder);
        
        // Ekstrak angka dari folder (misalnya 1client.ripit.id => 1)
        if (preg_match('/^(\d+)client\.ripit\.id$/', $folderName, $matches)) {
            $number = (int) $matches[1];
            // Cari angka terbesar
            if ($number > $maxNumber) {
                $maxNumber = $number;
            }
        }
    }

    // Return angka terbesar ditambah 1
    return $maxNumber ;
}

function duplicateProject($targetFolder) {
    // Folder proyek utama
    $sourceFolder = '/www/wwwroot/project_utama';

    // Path untuk folder tujuan
    $destinationFolder = '/www/wwwroot/' . $targetFolder;

    // Buat folder tujuan jika belum ada
    if (!file_exists($destinationFolder)) {
        if (!mkdir($destinationFolder, 0755, true)) {
            error_log("Gagal membuat folder tujuan: $destinationFolder");
            return;
        }
    }

    // Rekursif menyalin semua file dan folder dari sumber ke tujuan
    function recurseCopy($src, $dst) {
        $dir = opendir($src);
        if (!$dir) {
            error_log("Gagal membuka direktori sumber: $src");
            return;
        }
        @mkdir($dst, 0755, true);
        while (false !== ($file = readdir($dir))) {
            if (($file != '.') && ($file != '..')) {
                if (is_dir($src . '/' . $file)) {
                    // Mengatur izin direktori sebelum menyalin
                    chmod($src . '/' . $file, 0755);
                    recurseCopy($src . '/' . $file, $dst . '/' . $file);
                } else {
                    // Mengatur izin file sebelum menyalin
                    chmod($src . '/' . $file, 0644);
                    if (!@copy($src . '/' . $file, $dst . '/' . $file)) {
                        error_log("Gagal menyalin file: $src/$file ke $dst/$file, mengabaikan dan melanjutkan");
                    }
                }
            }
        }
        closedir($dir);
    }

    recurseCopy($sourceFolder, $destinationFolder);

    // Update file .env di folder tujuan
    $envFile = $destinationFolder . '/.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        if ($envContent === false) {
            error_log("Gagal membaca file .env di $envFile");
            return;
        }
        $newDatabaseName = str_replace('client.ripit.id', '_ripit', $targetFolder);
        $envContent = preg_replace('/DB_DATABASE=\d+_ripit/', 'DB_DATABASE=' . $newDatabaseName, $envContent);
        if (file_put_contents($envFile, $envContent) === false) {
            error_log("Gagal menulis ke file .env di $envFile");
            return;
        }
    } else {
        error_log("File .env tidak ditemukan di $destinationFolder");
        return;
    }

    error_log("Proyek berhasil disalin ke $destinationFolder");
}

// Cari angka terbesar dari folder client.ripit.id yang ada dan buat folder baru
$largestNumber = getLargestClientFolder();
$targetFolder = $largestNumber . 'client.ripit.id';

duplicateProject($targetFolder);

?>
