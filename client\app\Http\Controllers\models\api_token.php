<?php
namespace App\Http\Controllers\models;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\api_user_tokens as tblApiUserTokens;
use App\Http\Controllers\config\index as Config;
use Exception;

class api_token extends Controller
{
    //
    public function main(Request $request){
        $getdata = $this->checkToken([
            'token'    =>  $request->token
        ]);

        //if error 
        if($getdata['message'] != ''){

            return response()->json($getdata['message'], $getdata['error']['status']);
        }

        //if success
        return response()->json($getdata['response'],200);
    } 

    //
    public function checkToken($request){

        try{

            $Config = new Config;
            $thistime = strtotime(date('Y-m-d', time()));
    
            $getdata = tblApiUserTokens::where([
                'token'             =>  $request['token'],
                'published'         =>  1,
                'status'            =>  1
            ])->first();

            //check publisehd
            if($getdata == null){

                $data = [
                    'message'       =>  'User Token not found!',
                    'error'         =>  [
                        'status'        =>  404
                    ]
                ];

                return $data;
            }

            //check expired
            if($thistime > $getdata->expired_time){
                $data = [
                    'message'       =>  'User Token expired!',
                    'error'         =>  [
                        'status'        =>  401
                    ]
                ];

                return $data;
            }
            

            //success
            $data = [
                'message'       =>  '',
                'response'      =>  [
                    'id'            =>  $getdata->id,
                    'expired'       =>  $getdata->expired_date,
                    'published'     =>  $getdata->published
                ]
            ];

            return $data;
            
        }
        catch(Exception $error){

            $data =[
                'message'       =>  $error,
                'error'         =>  [
                    'status'            =>  500
                ]
            ];

            return $data;
        }

        
    }
}