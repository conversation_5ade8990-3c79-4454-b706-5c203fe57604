<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ErrorService
{
    public function reportError(string $message, string $source = 'ripit.id'): bool
    {
        try {
            $response = Http::asForm()->post('https://n.gass.co.id/api.html', [
                'act'    => 'er_report',
                'site'   => $source,
                'err'    => $message,
            ]);

            if (!$response->successful()) {
                Log::error('ErrorService report failed. Status: ' . $response->status() . ' Body: ' . $response->body());
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('ErrorService exception: ' . $e->getMessage());
            return false;
        }
    }
}
