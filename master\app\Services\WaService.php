<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WaService
{
    public function sendWaToKonekwa(array $data): bool
    {
        try {
            $response = Http::post('https://api.konekwa.com/api.html', [
                'phone'    => $data['phone'],
                'caption'  => $data['caption'],
                // 'phone_cs' => '6281314128833',
                'phone_cs' => '628885082933',
                'file_url' => '',
                'type'     => 'text',
                'act'      => 'ripit_send_wa',
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Konekwa WA error: ' . $e->getMessage());
            return false;
        }
    }
}
