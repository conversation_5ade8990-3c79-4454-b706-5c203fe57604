 <?php
    $config = [
        'app_signature'      => 'ripit',
        'homepage'           => "http://{$subdomain}.localhost:8000",
        'sitename'           => "{$subdomain}.localhost:8000",
        'sitetagline'        => '',
        'https'              => true,
        'www'                => false,
        'redirect_single'    => false,
        'module_path'        => 'module_api',
        'link_login'         => "http://{$subdomain}.localhost:8000/api/masuk",
    ];
    // Simpan konfigurasi dalam file pian.php
    $server = 'localhost:8000';
    file_put_contents(base_path("app/config/{$subdomain}" . '.localhost' . '.php'), '<?php return ' . var_export($config, true) . ';');
