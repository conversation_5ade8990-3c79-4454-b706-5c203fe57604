<?php

use Symfony\Component\DependencyInjection\Container;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\manage;
use App\Http\Controllers\ApiController;

/** @var \Laravel\Lumen\Routing\Router $router */



$router->get('/api/users/{name}', 'ApiUserController@index');
$router->get('/env/{name}', 'ApiUserController@index');
// /test
$router->get('/test', function () use ($router) {
    return "Hello, from subdomain!";
});
// /env. 




//group middleware cekrequest
$router->group(['prefix' => 'api',  'middleware' => 'cekrequest'], function ($router) {
    //new access
    //masuk user method get (gunakan parameter token)
    $router->get('/masuk', 'access\manage@masuk');
    //daftar
    $router->post('/daftar', 'access\manage@daftar');
    //keluar
    $router->post('/keluar', 'access\manage@keluar');



    //access
    $router->post('/login', 'access\manage@login');
    //cek status udah log in atau belum
    $router->post('/signup', 'access\manage@signup');

    //registers success
    $router->get('/registers/success', 'account\manage@registersuccess');
    $router->post('/reverifaccount', 'account\manage@reverifaccount');

    //reset password
    $router->post('/resetpassword', 'access\manage@resetpassword');
    $router->get('/account/changepassword', 'account\index@getchangepassword');
    $router->post('/account/changepassword-out', 'account\manage@sendchangepassword');

    // verifcation
    $router->get('/account/verification', 'account\manage@verification');

    $router->post('/account/sendverification', 'account\manage@sendverification');

    // ADMIN OR PENGGUNA

    // GET PRODUCT ===========>
    // list on widget
    $router->get('/product/list/widget', 'products\lists@widget');
    // GET BULKING LIST ON WIDGET
    $router->get('/orders/bulking/listwg', 'bulkingpayment\manage@listwg');
});
// middleware cekrequest and key account
$router->group(['prefix' => 'api',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    // GET CUSTOMER ============>
    //list on widget
    $router->get('/customers/list/widget', 'customers\lists@widget');
    $router->get('/customers/address/list', 'customers\address@list');
    // ORDERS 
    //new
    $router->get('/orders/table', 'orders\table@main');
    $router->post('/orders/widget/new', 'orders\manage@new'); //new orders
    $router->post('/orders/widget/additem', 'models\orders@updateadditem');
    $router->post('/orders/destination/set', 'orders\manage@setaddress');
    $router->post('/orders/address/keepinorder', 'orders\manage@keepaddress');
    $router->post('/orders/courier/setcost', 'orders\manage@setcostcourier');

    //KEEP PAYMENT
    $router->post('/orders/metodepayment', 'orders\manage@metodePayment');

    //LIST METHOD PAYMENT ON MODAL CHECKOUT
    $router->get('/orders/metodepayment/list', 'orders\payment@metode');
    $router->get('/orders/metodepayment/list-new', 'orders\payment@selectPayment');


    $router->get('/orders/vdetail', 'orders\manage@viewdetail');
    $router->get('/orders/vcheck', 'orders\manage@viewcheck');
    $router->post('/orders/checkout', 'orders\widget@checkout');
    $router->post('/orders/setpayment', 'orders\widget@setpayment');
    $router->post('/orders/delete', 'orders\manage@delete');

    //ORDERS BULKING UPLOAD EXCEL
    $router->post('/orders-bulk-excel/create', 'orderbulkexcel\manage@create');
    $router->post('/orders-bulk-excel/upload-excel', 'orderbulkexcel\manage@uploadexcel');
    $router->post('/orders-bulk-excel/upload-bb', 'orderbulkexcel\manage@uploadbb');
    $router->post('/orders-bulk-excel/createitem', 'orderbulkexcel\manage@createitem');
    $router->post('/orders-bulk-excel/update', 'orderbulkexcel\manage@updateitem');
    $router->post('/orders-bulk-excel/delete-item', 'orderbulkexcel\manage@deleteitem');
    $router->post('/orders-bulk-excel/save', 'orderbulkexcel\manage@save');
    $router->get('/orders-bulk-excel/table', 'orderbulkexcel\table@main');
    $router->get('/orders-bulk-excel/list-payment', 'orderbulkexcel\manage@listpayment');
    $router->get('/orders-bulk-excel/view', 'orderbulkexcel\manage@getview');
    $router->post('/orders-bulk-excel/verif-payment', 'orderbulkexcel\manage@verifpayment');
    $router->post('/orders-bulk-excel/order-paid', 'orderbulkexcel\manage@paid');
    $router->get('/orders-bulk-excel/invoice', 'orderbulkexcel\manage@getinvoice');


    //check in verif orders
    $router->get('/orders/checkveriforders', 'orders\manage@checkveriforders');

    // ORDER STOCK
    $router->get('/orderstock/table', 'orderstock\table@main');

    // ORIGIN
    $router->get('/origin/list', 'origin\index@list');
    $router->post('/origin/set', 'origin\index@set');

    //update quantity
    $router->post('/orders/widget/updateqty', 'orders\manage@updateqty');
    $router->post('/orders/widget/delete/item', 'orders\manage@deleteitem');

    //cart
    $router->get('/orders/widget/cart', 'orders\manage@getcart');

    // PRODUCT ====>
    $router->get('/product/list/widgetmodal', 'products\lists@widgetmodal');
    $router->get('/product/list', 'products\lists@view');
    $router->get('/product/table', 'products\table@main');
    $router->get('/product/distributor-list', 'products\lists@distributor');
    $router->get('/product/view-list-distributor', 'products\lists@viewDistributor');
    $router->post('/product/create', 'products\manage@create');
    $router->get('/product/detail', 'products\manage@detail');

    //cek statu
    $router->get('/cekstatus', 'access\manage@cekstatus');


    // COURIER
    $router->get('/courier/list/widget', 'courier\index@list');
    $router->get('/courier/cost', 'courier\index@cost');
    $router->get('/courier/cost/single', 'courier\index@checking');

    //SRC KOTA dan KECAMATAN
    $router->get('/data/srckotakecamatan', 'data\kotakecamatan@list');

    // ADDRESS ====== >
    $router->get('/customers/address/single', 'customers\address@view');
    $router->get('/customers/table', 'customers\table@main');



    //PAYMENT
    $router->get('/bank/list', 'orders\payment@banklist');

    // VERIF ORDERS
    $router->post('/orders/upload', 'orders\manage@upload');
    $router->post('/orders/verification', 'orders\manage@verification');
    $router->get('/veriforder/table', 'veriforder\table@main');

    // SHIPING
    $router->get('/shiping/table', 'shiping\table@main');
    $router->post('/shiping/addnoresi', 'shiping\manage@addnoresi');
    $router->post('/shiping/pickup', 'shiping\manage@pickup');

    // BULKING PAYMENT
    $router->post('/orders/bulking/payments', 'bulkingpayment\manage@check');
    $router->get('/bulkingpayment/table', 'bulkingpayment\table@main');
    $router->post('/orders/bulking/deletelist', 'bulkingpayment\manage@deletelist');
    $router->get('/orders/bulking/metodepayment/list', 'bulkingpayment\manage@metodepayment');
    $router->post('/orders/bulking/setmetodepayment', 'bulkingpayment\manage@setmetodepayment');
    $router->get('/orders/bulking/vcheck', 'bulkingpayment\index@check');
    $router->post('/orders/bulking/delete', 'bulkingpayment\manage@delete');
    //upload bulking
    $router->post('/orders/uploadbulking', 'bulkingpayment\manage@upload');
    $router->post('/verifbulking/verification', 'verifbulking\manage@verification');


    // CUSTOMERS
    $router->post('/customers/manage/add', 'customers\manage@add');
    $router->post('/customers/manage/changeprogress', 'customers\manage@changeprogress');
    $router->post('/customers/manage/changetaging', 'customers\manage@changetaging');
    $router->post('/customers/manage/addnote', 'customers\manage@addnote');
    $router->get('/customers/detail', 'customers\manage@detail');
    $router->get('/customers/vs-edit', 'customers\manage@vshortedit');
    $router->post('/customers/s-edit', 'customers\manage@sedit');

    // EXPORT TO EXCEL ======>

    // BULKING VERIFICATION PAYMENT

    $router->get('/verifbulking/table', 'verifbulking\table@main');
    $router->get('/verifbulking/check', 'verifbulking\manage@check');

    // ADMIN
    $router->get('/admin/table', 'admin\table@main');
    $router->get('/admin/manage/view', 'admin\manage@view');
    $router->post('/admin/manage/changestatus', 'admin\manage@changestatus');
    $router->post('/admin/manage/resendverification', 'admin\manage@resendverification');
    $router->post('/admin/manage/create', 'admin\manage@create');
    $router->get('/admin/user-orders', 'admin\manage@userOrders');


    // PARTNER
    $router->get('/partner/table', 'partner\table@main');
    $router->post('/partner/create', 'partner\manage@create');
    $router->get('/partner/view', 'partner\manage@view');

    // SUNTING PARTNER
    $router->post('/partner/sunting/label', 'partner\manage@suntingLabel');
    $router->post('/partner/sunting/contact', 'partner\manage@suntingContact');
    $router->post('/partner/sunting/owner', 'partner\manage@suntingOwner');
    $router->post('/partner/sunting/address', 'partner\manage@suntingAddress');
    $router->post('/partner/sunting/admin', 'partner\manage@suntingAdmin');
    $router->post('/partner/sunting/chpricedistributor', 'partner\manage@createPrice');
    $router->post('/partner/sunting/delete-distprice', 'partner\manage@deletePriceDistributor');
    $router->get('/partner/sunting/getdistributor-pricelist', 'partner\manage@getlists');
    $router->post('/partner/sunting/product-distributor', 'partner\manage@CreatePDistributor');

    // MANAGE GLOBAL
    $router->get('/manage-global/detail', 'manageglobal\manage@detail');
    $router->post('/manage-global/sunting/bank', 'manageglobal\manage@bank');
    $router->post('/manage-global/sunting/gudang', 'manageglobal\manage@gudang');
    $router->post('/manage-global/sunting/delete-bank', 'manageglobal\manage@deletebank');
    $router->post('/manage-global/sunting/delete-gudang', 'manageglobal\manage@deletegudang');
    $router->post('/manage-global/sunting/chuniqnum', 'manageglobal\manage@changeuniqnum');
    $router->post('/manage-global/sunting/cod', 'manageglobal\manage@createCod');


    //customers address
    $router->post('/customers/address/create', 'customers\address@create');

    // EXPORT ORDERS
    $router->get('/export/orders', 'export\orders\index@main');
    $router->get('/export/bulkings', 'export\bulkings\index@main');

    // EXPORT ORDER FOR 3PARTY
    $router->get('/export/default', 'export\orders\index@everpro');
    $router->get('/export/everpro', 'export\orders\index@everpro');
    $router->get('/export/imezi', 'export\orders\index@imezi');

    // ACCOUNT
    // $router->get('/account/CheckRefreshtokenJWT', 'access\manage@CheckRefreshJWT');
    $router->post('/account/changepassword', 'account\manage@ChangePassword');
    $router->post('/account/exeprofile-bio', 'account\manage@ChangeBio');
    $router->post('/account/exeprofile-password', 'account\manage@ChangeUserPassword');


    //
    $router->get('/companies/list', 'companies\index@lists');

    //MAKLON
    $router->get('/maklon/list', 'maklon\manage@list');


    //NOTIFICATIONS
    //TEST
    $router->get('/notifications/add', 'notification\index@test');


    // ASIDE RIGHT
    $router->get('/getaside', 'config\aside@viewSingle');

    // PRINT
    $router->get('/shiping/print/token', 'tools\prints\orders@shiping');
    $router->get('/invoice/print/token', 'tools\prints\orders@invoice');

    //OUTER REQUEST GET
    $router->get('/data/distributor/list', 'data\partner\manage@list');
    $router->get('/data/company/list', 'data\company\manage@list');




    $router->get('/data/cs/list', 'data\cs\manage@list');
    $router->get('/config/aside/view', 'config\aside@view');

    $router->get('/data/partner/list', 'data\partner\manage@list');


    $router->get('/data/sublevel/list', 'data\account\manage@sublevel');

    //ALL DATA NEW
    $router->get('/data/partner/list-new', 'data\partner\manage@listview');
});

//upload csv
$router->group(['prefix' => 'api/upload/csv',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->post('/customers', 'tdparty\csv\index@customers');
});

//group middleware cekrequest and auth
$router->group(['prefix' => 'api', 'middleware' => ['cekrequest', 'auth']], function ($router) {
    $router->post('/logout', 'access\manage@logout');
    $router->get('/profile', 'access\manage@profile');

    // $router->get('/refresh', 'access\manage@refresh');

    //customers
    // $router->post('/customers/table', 'customers\table@main');
    // $router->post('/customers/manage/add', 'customers\manage@add');
    // $router->post('/customers/manage/changeprogress', 'customers\manage@changeprogress');
    // $router->post('/customers/manage/changetaging', 'customers\manage@changetaging');
    // $router->post('/customers/manage/addnote', 'customers\manage@addnote');

    // //customers address
    // $router->post('/customers/address/create', 'customers\address@create');

    // ====> ORDERS    
    // $router->post('/orders/checkout', 'orders\widget@checkout');
    // $router->post('/orders/setpayment', 'orders\widget@setpayment');
    // $router->post('/orders/delete', 'orders\manage@delete');

    // BULKING =======>
    // $router->post('/orders/bulking/payments', 'bulkingpayment\manage@check');

    // PENGGUNA  / ADMIN
    // $router->post('/admin/manage/create', 'admin\manage@create');

    // ACCOUNT
    $router->get('/account/refreshtoken', 'access\manage@refresh');

    //upload



    // //upload bulking
    // $router->post('/orders/uploadbulking', 'bulkingpayment\manage@upload');
    // $router->post('/verifbulking/verification', 'verifbulking\manage@verification');

    // export
    $router->post('/export/customers', 'export\customers\index@main');
});




// ROUTER FOR HOME PAGE
$router->group(['prefix' => 'api/home',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {

    //MAIN
    $router->get('/menu', 'home\menu\manage@getMenus');

    //ABSENSI
    $router->get('/absen/getabsen', 'home\absen\index@getAbsen');
    $router->post('/absen/sendAttendance', 'home\absen\index@sendAttendance');
    $router->get('/absen/location/print', 'home\absen\index@getAttendanceLocation');
    $router->get('/absen/screen', 'home\absen\index@getScreen');
    $router->get('/absen/infodate', 'home\absen\index@infodate');
    $router->post('/absen/setinfo', 'home\absen\index@setInfo');
    $router->get('/absen/viewsingle', 'home\absen\index@viewSingle');



    //CALENDAR
    $router->get('/calendar/attendance/employe', 'home\calendar\manage@attendance');

    //SCREEN
    $router->post('/absen/getQRdinamic', 'home\absen\index@getdinamic');

    //EMPLOYE
    $router->get('/employe/table', 'home\employes\table@main');
    $router->post('/employe/create-account', 'home\employes\manage@createAccount');
    $router->get('/employe/data/groups', 'home\employes\data@listgroup');


    // REPORT ABSEN
    $router->get('/absen/report/table', 'home\absen\report@main');


    //DATA =============== >
    $router->get('/data/employe/list', 'data\home\employe@list');


    //TASK
    $router->get('/task/table', 'home\task\table@main');
    $router->get('/task/verif', 'home\task\table@verif');
    $router->post('/task/create', 'home\task\manage@create');
    $router->get('/task/view', 'home\task\manage@view');
    $router->post('/task/verify', 'home\task\manage@verify');

    // CRM


    //INVENTORY
    $router->post('/inventory/material/create', 'home\inventory\materials\manage@create');
    $router->get('/inventory/material/table', 'home\inventory\materials\table@main');
    $router->get('/inventory/material/show', 'home\inventory\materials\manage@show');
    $router->get('/inventory/material/log/in-bb', 'home\inventory\materials\log\table@inbb');

    //INVENTORY PRODUCT
    $router->get('/inventory/product/table', 'home\inventory\product\table@main');

    // REQUEST PO BB
    $router->get('/inventory/material/rpobb/table', 'home\inventory\po\table@requestbb');
    $router->get('/inventory/material/rpobb/list-item', 'home\inventory\po\table@listitem');

    $router->post('/inventory/material/rpobb/add', 'home\inventory\po\manage@add');

    $router->post('/inventory/material/rpobb/updateitem', 'home\inventory\po\manage@updateitem');
    $router->post('/inventory/material/rpobb/updateprogress', 'home\inventory\po\manage@updatepo');

    $router->post('/inventory/material/rpobb/delete', 'home\inventory\po\manage@delete');

    //ORDER PO BB
    $router->get('/inventory/material/vpobb/table', 'home\inventory\orderpo\table@main');

    //NOTIFICATION
    $router->post('/notifications/read', 'home\notifications\manage@read');

    // SUPLIER
    $router->get('/inventory/suplier/table', 'home\inventory\suplier\table@main');
    $router->get('/inventory/suplier/show', 'home\inventory\suplier\manage@show');
});

//DATA =============== >
$router->group(['prefix' => 'api/home/<USER>',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->get('/employe/groups/lists', 'home\employes\data@groups');
    $router->get('/employe/list', 'home\employes\data@list');

    //inventory
    $router->get('/material/categories', 'home\inventory\materials\data@categories');
    $router->get('/material/units', 'home\inventory\materials\data@units');
    $router->get('/material/types', 'home\inventory\materials\data@types');
    $router->get('/material/suplier', 'home\inventory\materials\data@suplier');
    $router->get('/material/productbb', 'home\inventory\materials\data@productbb');


    //FORM DATA
    $router->get('/material/form-sbb', 'home\inventory\materials\data@formSbb');
    $router->get('/material/filter-sbb', 'home\inventory\materials\data@filtersbb');
    $router->get('/material/filter-login-sbb', 'home\inventory\materials\data@filterLogInSbb');
});


$router->get('/api/home/<USER>/view', 'home\absen\index@getView');




// VOUCHER
$router->group(['prefix' => 'api/voucher',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->get('/list', 'voucher\manage@list');
    $router->post('/set', 'voucher\manage@set');
    $router->post('/remove', 'voucher\manage@delete');
});

// DASHBOARD REPORT
$router->group(['prefix' => 'api/dashboard',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->get('/report', 'dashboard\report@main');
    $router->get('/report/export', 'export\orders\index@dashboard');
});

//REPORT HOME
$router->group(['prefix' => 'api/home/<USER>',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->get('/attendance', 'export\home\attendance@main');

    //TASK
    $router->get('/task/excel', 'export\home\task@excel');
});



// CLICK WA
$router->group(['prefix' => 'api/clickwa',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {

    $router->get('/orders/invoice', 'clickwa\orders\manage@invoice');
    $router->get('/orders/shiping', 'clickwa\orders\manage@shiping');
});


//CHECK TOKEN USER COMPANIES
$router->group(['prefix' => 'api/v2',  'middleware' => ['cekrequest', 'checkUserToken']], function ($router) {
    $router->get('/checktoken', function () {
        return response()->json(['message' => 'success'], 200);
    });
});


$router->group(['prefix' => 'api/upload',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->post('/bulking/customer', 'upload\customer\index@create');
    $router->post('/bulking/customer/get', 'upload\customer\index@show');
});


//HACKD
$router->group(['prefix' => 'api/hack',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {
    $router->get('/menu/create', 'home\menu\manage@createIn');
});



// TESTING
$router->group(['prefix' => 'testing',  'middleware' => ['cekrequest', 'cekKeyAccount']], function ($router) {

    $router->get('/test/notification/add', 'notification\index@add');

    // ====== TESTING AREA ====================== >
    $router->get('/test/viewprofile', 'account\index@viewprofile');

    // $router->post('/test', 'orders\manage@upload');
    $router->post('/testbulking', 'bulkingpayment\manage@check');

    $router->get('/testing', 'companies\manage@getpaymentlist');

    $router->get('/testingusers', 'testing\data\getdata@users');
    $router->get('/viewaside', 'config\aside@viewSingle');
    $router->get('/testingcreate', 'config\aside@test');

    $router->post('/testing/logcustomer-add', 'testing\log\customers@add');



    // DATA
    $router->get('/uploadjson', 'testing\data\sap@origin');

    //MENU HOME
    $router->get('/menu/home', 'home\menu\manage@createMenus');
    $router->get('/menu/employe', 'home\menu\manage@getMenus');
    $router->get('/att/count', 'testing\home\index@AttCount');
    $router->get('/att/view-time', 'testing\home\index@viewTime');
    $router->get('/att/countlate', 'testing\home\index@countLate');
    $router->get('/att/counttime', 'testing\home\index@cekcount');

    //
    $router->get('/absen/report', 'testing\home\reportatt@main');

    //
    $router->get('/vfunction', 'testing\index@viewfunction');

    //time ago
    $router->get('/timeago', 'testing\index@timeago');

    //
    //test read excel
    $router->get('/read-excel', 'testing\index@readexcel');

    $router->get('/read/csv', 'testing\readCsvFile@index');

    //TESTING CHECK 
    $router->post('/read/content', 'testing\index@getContent');
});
