<?php
namespace App\Http\Controllers\tdparty\s3;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Exception;

class index extends Controller
{
    //
    public function upload($request)
    {
        try{

            $curl = curl_init();
    
            $URL = $request['URL'];
            $type = $request['type'];
            $data = $request['datapost'];
            $headers = $request['headers'];
    
            curl_setopt_array($curl, array(
            CURLOPT_URL => $URL,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            // CURLOPT_SAFE_UPLOAD => false,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $type,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => $headers,
            ));
    
            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);
    
            if( $err )
            {
                return $data = [
                    'status'        =>  401,
                    'message'       =>  $err
                ];
            }
            else
            {
                return $data = [
                    'status'        =>  200,
                    'response'      =>  $response
                ];
            }
        }
        catch(Exception $error){
            return $data =[
                'message'       =>  $error
            ];
        }
    }
}