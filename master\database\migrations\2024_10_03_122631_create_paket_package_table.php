<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paket_package', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->decimal('harga', 10, 2);
            $table->timestamps();
        });
          DB::table('paket_package')->insert([
            ['name' => 'Essential', 'harga' => 99000],
            ['name' => 'Team', 'harga' => 239000],
            ['name' => 'Corporate', 'harga' => 349000],
            ['name' => 'Enterprise', 'harga' => 500000],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paket_package');
    }
};
