<?php
namespace App\Http\Controllers\home\inventory\materials;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\material_categories as tblMaterialCategories;
use App\material_units as tblMaterialUnits;
use App\material_types as tblMaterialTypes;
use App\supliers as tblSupliers;
use App\materials as tblMaterials;

class data extends Controller
{
    //
    public function categories(Request $request)
    {
        $data = [
            "message"       =>  "",
            "list"          =>  $this->dataCategori($request)
        ];
        
        return response()->json($data,200);
    }

    //
    public function units(Request $request)
    {

        $data = [
            "message"       =>  "",
            "list"          =>  $this->dataUnit($request)
        ];
        
        return response()->json($data,200);
    }

    //type
    public function types(Request $request)
    {
        $data = [
            "message"       =>  "",
            "list"          =>  $this->dataType($request)
        ];
        
        return response()->json($data,200);
    }

    //suplier
    public function suplier(Request $request)
    {
        $data = [
            "message"       =>  "",
            "list"          =>  $this->dataSuplier($request)
        ];
        
        return response()->json($data,200);
    }

    //DATA
    function dataSuplier($request)
    {
        $getdata = tblSupliers::from('supliers as s')
        ->select(
            "s.id", "s.name",
            "ct.alias as type_name"
        )
        ->leftJoin('corporate_types as ct', function($join)
        {
            $join->on('ct.id', '=', 's.type');
        })
        ->where([
            "s.status"        =>  1
        ])
        ->orderBy('s.name', 'asc')
        ->get();


        foreach($getdata as $row)
        {
            $list[] = [
                'id'            =>  $row->id,
                'name'          =>  ($row->type_name === null ? "" : $row->type_name . " ") . $row->name
            ];
        }

        return $list;
    }

    // TYPE
    function dataType($request)
    {
        $getdata = tblMaterialTypes::where([
            "status"        =>  1
        ])
        ->get();

        return $getdata;
    }

    // UNIT
    function dataUnit($request)
    {
        $getdata = tblMaterialUnits::where([
            "status"        =>  1
        ])
        ->get();

        return $getdata;
    }

    // CATEGORY
    function dataCategori($request)
    {
        $getdata = tblMaterialCategories::where([
            "status"        =>  1
        ])
        ->get();

        return $getdata;
    }

    //PRODUCT BAHAN BAKU
    public function productbb(Request $request)
    {
        $Config = new Config;
        //
        $getdata = tblMaterials::from("materials as m")
        ->select(
            "m.id", "m.name", "m.code", "m.stock",
            "s.name as suplier", "ct.alias as suplier_type",
            "mu.name as unit_name"
        )
        ->leftJoin("supliers as s", function($join)
        {
            $join->on("s.id", "=", "m.suplier");
        })
        ->leftJoin("corporate_types as ct", function($join)
        {
            $join->on("ct.id", "=", "s.type");
        })
        ->leftJoin("material_units as mu", function($join)
        {
            $join->on("mu.id", "=", "m.units");
        })
        ->where([
            "m.status"      =>  1
        ]);
        if($request->item != '')
        {
            $itm = explode(",", $request->item);
            $item = [];
            $item = $itm;

            $getdata = $getdata->whereNotIn('m.id', $item);
        }

        $count = $getdata->count();


        if( $count == 0)
        {
            $data = [
                "message"       =>  "Data tidak ditemukan"
            ];

            return response()->json($data, 404);
        }


        $gettable = $getdata->get();
        foreach($gettable as $row)
        {
            $list[] = [
                "id"            =>  $row->id,
                "kode"          =>  $row->code,
                "name"          =>  $row->name,
                "stock"         =>  $row->stock,
                "unit"          =>  $row->unit_name,
                "suplier"       =>  ($row->suplier_type === null ? "" : $row->suplier_type . " ") . $row->suplier            ];
        }


        $data = [
            "message"       =>  "",
            "list"          =>  $list,
            "item"          =>  $request->item
        ];

        return response()->json($data, 200);
        
    }

    // FORM BAHAN BAKU
    public function formSbb()
    {
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'suplier'           =>  $this->dataSuplier(""),
                'type'              =>  $this->dataType(""),
                'unit'              =>  $this->dataUnit(""),
                'category'          =>  $this->dataCategori("")
            ]
        ];

        return response()->json($data,200);
    }

    public function filtersbb()
    {
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'type'              =>  $this->dataType(""),
                'category'          =>  $this->dataCategori("")
            ]
        ];

        return response()->json($data,200);
    }


    public function filterLogInSbb()
    {
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'suplier'           =>  $this->dataSuplier(""),
                'category'          =>  $this->dataCategori("")
            ]
        ];

        return response()->json($data,200);
    }
}