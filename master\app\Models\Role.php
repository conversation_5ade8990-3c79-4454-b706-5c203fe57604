<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;
    protected $table = 'roles'; // Tentukan nama tabel yang sesuai
    protected $fillable = [
        'name',
        'alamat_lengkap',
    ];
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
