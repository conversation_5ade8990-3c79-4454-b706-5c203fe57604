<?php
namespace App\Http\Controllers\home\inventory\materials;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\materials as tblMaterials;
use App\Http\Controllers\config\index as Config;

class table extends Controller
{
    //
    public function main(Request $request)
    {
        $Config = new Config;

        $src = str_replace(";", "", trim($request->search));
        $src = "%" . $src . "%";
        $type = trim($request->type);
        $categori = trim($request->categori);
        $paging = trim($request->paging);

        //
        $getdata = tblMaterials::from("materials as m")
        ->select(
            "m.id", "m.token", "m.code", "m.name", "m.stock","m.limitstock", "m.description", "m.created_at", "m.stock", "m.buffer", "m.safety",
            "mt.name as type_name",
            "mc.name as categori_name",
            "mu.name as unit_name",
            "s.name as suplier_name",
            "u.name as username",
        )
        ->leftJoin("material_types as mt", function($join)
        {
            $join->on("mt.id", "=", "m.type");
        })
        ->leftJoin("material_categories as mc", function($join)
        {
            $join->on("mc.id", "=", "m.categories");
        })
        ->leftJoin("material_units as mu", function($join)
        {
            $join->on("mu.id", "=", "m.units");
        })
        ->leftJoin("supliers as s", function($join)
        {
            $join->on("s.id", "=", "m.suplier");
        })
        ->leftJoin("users as u", function($join)
        {
            $join->on("u.id", "=", "m.user_id");
        })
        ->where([
            ['m.search', 'like', $src]
        ]);
        if( $categori != "")
        {
            $getdata = $getdata->where([
                'm.categories'      =>  $categori
            ]);
        }
        if( $type != "")
        {
            $getdata = $getdata->where([
                'm.type'    =>  $type
            ]);
        }

        $count = $getdata->count();

        //null
        if( $count === 0)
        {
            $data = [
                'message'       =>  'Data Tidak ditemukan',
                'response'      =>  ''
            ];

            return response()->json($data, 404);
        }

        //
        $gettable = $getdata->take($Config->table(['paging'=>$paging])['paging_item'])
        ->skip($Config->table(['paging'=>$paging])['paging_limit'])
        ->get();

        //
        foreach($gettable as $row)
        {
            $list[] = [
                'id'                =>  $row->id,
                'code'              =>  $row->code,
                'token'             =>  $row->token,
                'name'              =>  $row->name,
                'suplier'           =>  $row->suplier_name,
                'categori'          =>  $row->categori_name,
                'type'              =>  $row->type_name,
                'unit'              =>  $row->unit_name,
                'user'              =>  $row->username,
                'stock'             =>  $row->stock,
                'limitstock'             =>  $row->limitstock,
                'buffer'            =>  $row->buffer,
                'safety'            =>  $row->safety,
                'date'              =>  $Config->timeago($row->created_at),
                'status'            =>  ($row->stock < $row->limitstock ? 'hold' : ($row->stock === $row->limitstock ? 'progress' : 'done') )
            ];
        }

        //
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'list'          =>  $list,
                'paging'        =>  $paging,
                'total'         =>  $count,
                'countpage'     =>  ceil($count / $Config->table(['paging'=>$paging])['paging_item'] )
            ]
        ];

        return response()->json($data,200);

    }
}