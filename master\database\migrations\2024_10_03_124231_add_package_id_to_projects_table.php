<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
              $table->unsignedBigInteger('package_id')->nullable()->after('endpoint');
            // Menambahkan foreign key jika ingin
            $table->foreign('package_id')->references('id')->on('paket_package')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
              $table->dropForeign(['package_id']); // Jika Anda menambahkan foreign key
            $table->dropColumn('package_id');
        });
    }
};
