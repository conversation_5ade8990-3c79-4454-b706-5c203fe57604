<?php
error_reporting(E_ERROR | E_WARNING | E_PARSE | E_NOTICE);
$keya = "*1!";
$setDB = array('host' => 'localhost', 'username' => 'ripit', 'password' => 'iCDpFfXwySDjK6AZ', 'db' => 'ripit', 'port' => 3306, 'charset' => 'utf8mb4');

$config = [
    'app_signature'      => 'ripit',
    'homepage'           => 'https://ripit.gass.co.id',
    'sitename'           => 'ripit.gass.co.id',
    'sitetagline'        => '',
    'https'              => true,
    'www'                => false,
    'redirect_single'    => false, // true = auto redirect aff link
    'module_path'         => 'module_api',
    'link_login' => 'https://ripit.gass.co.id/api/masuk',
];
$permalink['home'] = [
    "pattern"         => '/',
    "patternPage"     => '',
    "module"          => "index",
    "theme"           => "login.tpl",
    "theme_path"    => "theme/panel/",
    "title"           => $config["sitename"],
];

$permalink['api'] = [
    "pattern"       => '/api',
    "patternPage"   => '',
    "module"        => "api",
    "theme"         => "",
    "theme_path"    => "",
    "title"         => '',
];


$permalink['cron'] = [
    "pattern"       => '/cron.html',
    "patternPage"   => '',
    "module"        => "cron",
    "theme"         => "",
    "theme_path"    => "",
    "title"         => '',
];

$permalink['single'] = [
    "pattern"       => '/%title%.html',
    "patternPage"   => '',
    "module"        => "single",
    "theme"         => "",
    "theme_path"    => "theme/panel/",
    "title"         => '%title% - ' . $config["sitename"],
];

$permalink['404'] = [
    "pattern"       => '/%title%',
    "patternPage"   => '',
    "module"        => "404",
    "theme"         => "",
    "theme_path"    => "",
    "title"         => '',
];
