<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;


class BtApi extends Model {
    private $BT_KEY = "YDNPBySXKJfIF7O5x7XMMPQLoAXwb7Cf";
    private $BT_PANEL = "***************:40790"; // Ini IP dari server client
    private $dbuser = 'root';
    private $dbpass = '23e271ceafb74e20'; // Update dengan password yang benar


    // private $BT_KEY = "9jhHW7VvMXSkwYkDVBM8hghEkxxjuKcN"; // API secret key dari aapanel
    // private $BT_PANEL = "http://**************:32148"; // default aapanel port
    // private $dbuser = 'root';
    // private $dbpass = '891e956c1bec2f40';


    public function __construct($bt_panel = null, $bt_key = null, $dbuser = null, $dbpass = null) {
        if ($bt_panel) $this->BT_PANEL = $bt_panel;
        if ($bt_key) $this->BT_KEY = $bt_key;
        if ($dbuser) $this->dbuser = $dbuser;
        if ($dbpass) $this->dbpass = $dbpass;
    }

        function calculateLoadStatus($data)
    {
        $loadAvg = $data['load']['one']; // Extract one-minute load average
        $systemCapacity = $data['load']['limit']; // Extract system capacity
        $threshold = $data['load']['safe']; // Extract safe threshold
        if ($loadAvg < $systemCapacity * $threshold) {
            return "Normal";
        } elseif ($loadAvg >= $systemCapacity) {
            return "Overloaded";
        } else {
            return "Warning";
        }
    }
    // Function to calculate CPU usage percentage
    function calculateCpuUsage($data)
    {
        $cpuTimes = $data['cpu_times'];
        // $idle = $cpuTimes['idle'];
        // $total = array_sum($cpuTimes);
        // $usage = 100 * (1 - ($idle / $total));
        // return $usage;

        $cpuUsage = $cpuTimes['user'] +
            $cpuTimes['system'] +
            $cpuTimes['iowait'] +
            $cpuTimes['steal'];

        return $cpuUsage;
    }

    // Function to calculate memory usage percentage
    function calculateMemoryUsage($data)
    {
        $mem = $data['mem'];
        $memTotal = $mem['memTotal'];
        $memUsed = $memTotal - $mem['memFree'] - $mem['memBuffers'] - $mem['memCached'];
        $usage = 100 * ($memUsed / $memTotal);
        return $usage;
    }

public function get_performance()
{
    $url = $this->BT_PANEL . '/system?action=GetNetWork';
    $p_data = $this->GetKeyData();
    $result = $this->HttpPostCookie($url, $p_data);

    // Log hasil response dari API
    \Log::info('API Result: ' . $result);

    $data = json_decode($result, true);

    if (!isset($data['load'])) {
        return response()->json([
            'status' => false,
            'error' => 'Field `load` tidak ditemukan dalam response',
            'raw' => $data
        ], 500);
    }

    $ret["load_status"] = $this->calculateLoadStatus($data);
    $ret["cpu_usage"] = $this->calculateCpuUsage($data);
    $ret["mem_usage"] = $this->calculateMemoryUsage($data);
    $ret["raw"] = $data;

    return $ret;
}

    public function add_db_access_wildcard($dbname, $dbuser, $dbpass)
    {
        $dsn = "mysql:host=**************;port=3306;dbname=mysql"; // Ubah IP dan port sesuai server remote
        $rootUser = 'root';
        $rootPass = '891e956c1bec2f40';
    
        try {
            $pdo = new \PDO($dsn, $rootUser, $rootPass);
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
    
            // Buat user dengan akses wildcard %
            $pdo->exec("CREATE USER IF NOT EXISTS '{$dbuser}'@'%' IDENTIFIED BY '{$dbpass}'");
            $pdo->exec("GRANT ALL PRIVILEGES ON `{$dbname}`.* TO '{$dbuser}'@'%'");
            $pdo->exec("FLUSH PRIVILEGES");
    
            return ['success' => true, 'message' => 'Wildcard access granted'];
        } catch (\PDOException $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    public function add_db($dbname, $dbuser, $dbpass)
    {
        $url = $this->BT_PANEL . '/database?action=AddDatabase';
        $p_data = $this->GetKeyData();
        $p_data['name'] = $dbname;
        $p_data['db_user'] = $dbuser;
        $p_data['password'] = $dbpass;
        $p_data['dataAccess'] = '%';
        $p_data['address'] = '%';

        // $p_data['dataAccess'] = '127.0.0.1';
        // $p_data['address'] = '127.0.0.1';
        $p_data['sid'] = 0;
        $p_data['ps'] = $dbname;
        $p_data['active'] = false;
        $p_data['ssl'] = '';
        $p_data['codeing'] = 'utf8';
        $p_data['dtype'] = 'MySQL';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }



    public function get_db($limit)
    {
        $url = $this->BT_PANEL . '/data?action=getData';
        $p_data = $this->GetKeyData();
        $p_data['table'] = 'databases';
        $p_data['limit'] = $limit;
        $p_data['search'] = '';
        $p_data['p'] = 1;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        //{
        //            "where": "type = \"MySQL\"",
        //            "page": "<div><span class='Pcurrent'>1</span><span class='Pcount'>Total 2</span></div>",
        //            "data": [
        //                {
        //                    "id": 2,
        //                    "sid": 0,
        //                    "pid": 0,
        //                    "name": "gass_client_1",
        //                    "username": "gass_client_1",
        //                    "password": "jyfKPMhsDXR47cfC",
        //                    "accept": "127.0.0.1",
        //                    "ps": "gass_client_1",
        //                    "addtime": "2023-07-28 06:09:08",
        //                    "db_type": 0,
        //                    "conn_config": {},
        //                    "backup_count": 0,
        //                    "quota": {
        //                        "size": 0,
        //                        "used": 0
        //                    }
        //                }
        //            ],
        //            "search_history": []
        //        }
    }

// public function execute_cron_by_name($name)
// {
//     // Mulai buffer output
//     ob_start();

//     // Ambil semua cronjob yang ada
//     $cron_jobs = $this->get_cron();

//     // Cari cronjob berdasarkan nama
//     foreach ($cron_jobs as $cron_job) {
//         if ($cron_job['name'] == $name) {
//             // Eksekusi cronjob berdasarkan ID
//             $result = $this->execute_cron_by_id($cron_job['id']);
//             // Ambil output dari buffer
//             $output = ob_get_clean();
            
//             // Kembalikan hasilnya dalam JSON, termasuk output dari log
//             return response()->json([
//                 'status' => $result['status'],
//                 'msg' => $result['msg'],
//                 'output' => $output // Tambahkan output log di sini
//             ]);
//         }
//     }

//     // Jika cronjob dengan nama tersebut tidak ditemukan, akhiri buffer output
//     ob_end_clean();

//     // Kembalikan respons jika cronjob tidak ditemukan
//     return response()->json([
//         'status' => false,
//         'msg' => 'Cronjob not found!'
//     ]);
// }


// private function execute_cron_by_id($id)
// {
//     // Buat URL untuk mengeksekusi cronjob
//     $url = $this->BT_PANEL . '/crontab?action=StartTask';

//     // Siapkan data yang akan dikirimkan
//     $p_data = $this->GetKeyData();
//     $p_data['id'] = $id;

//     // Kirim permintaan ke API melalui POST
//     $result = $this->HttpPostCookie($url, $p_data);

//     // Decode hasil respons
//     $data = json_decode($result, true);

//     // Kembalikan hasilnya
//     return $data;
// }


public function execute_cron($name, $params = []) {
    // Construct the command to execute the cron job
    $command = "/usr/bin/php /www/wwwroot/cronjob/{$name}.php";

    // Build the parameter string
    if (!empty($params)) {
        // Assuming params is an associative array
        $paramString = http_build_query($params); // Converts array to query string
        $command .= " " . escapeshellarg($paramString);
    }

    // Execute the command
    $output = shell_exec($command);
    return $output; // Or handle the output as needed
}



public function execute_cron_by_name($name, $parameter = null)
{
    // Mulai buffer output
    ob_start();

    // Ambil semua cronjob yang ada
    $cron_jobs = $this->get_cron();

    // Cari cronjob berdasarkan nama
    foreach ($cron_jobs as $cron_job) {
        if ($cron_job['name'] == $name) {
            // Eksekusi cronjob berdasarkan ID dengan parameter tambahan
            $result = $this->execute_cron_with_parameter($cron_job['id'], 'itil');
            
            // Tangkap output dan bersihkan buffer
            $output = ob_get_clean();

            // Kembalikan hasil eksekusi termasuk output log
            return response()->json([
                'status' => $result['status'],
                'msg' => $result['msg'],
                'output' => $output
            ]);
        }
    }

    // Jika cronjob tidak ditemukan, kembalikan pesan yang sesuai
    return response()->json([
        'status' => false,
        'msg' => 'Cron job tidak ditemukan!'
    ]);
}

private function execute_cron_with_parameter($id, $parameter)
{
    // Buat URL untuk mengeksekusi cronjob dengan parameter tambahan
    $url = $this->BT_PANEL . '/crontab?action=StartTask';

    // Siapkan data yang akan dikirimkan
    $p_data = $this->GetKeyData();
    $p_data['id'] = $id;
    if ($parameter) {
        $p_data['parameter'] = $parameter; // Tambahkan parameter tambahan jika ada
    }

    // Kirim permintaan ke API melalui POST
    $result = $this->HttpPostCookie($url, $p_data);

    // Decode hasil respons
    $data = json_decode($result, true);

    // Kembalikan hasilnya
    return $data;
}

public function exec_cron_project($name, $siteName)
{
    // Mulai buffer output untuk menangkap pesan log
    ob_start();

    // Ambil semua cronjob yang ada
    $cron_jobs = $this->get_cron();

    // Cari cronjob berdasarkan nama
    foreach ($cron_jobs as $cron_job) {
        if ($cron_job['name'] == $name) {
            // Eksekusi cronjob berdasarkan ID dengan parameter situs
            $result = $this->execute_cron_with_parameter($cron_job['id'], $siteName);
            
            // Tangkap output dan bersihkan buffer
            $output = ob_get_clean();

            // Kembalikan hasil eksekusi termasuk output log
            return response()->json([
                'status' => $result['status'],
                'msg' => $result['msg'],
                'output' => $output
            ]);
        }
    }

    // Jika cronjob tidak ditemukan, bersihkan buffer dan kembalikan pesan yang sesuai
    ob_end_clean();
    return response()->json([
        'status' => false,
        'msg' => 'Cron job tidak ditemukan!'
    ]);
}


public function exec_cron_db_name($name, $dbname)
{
    // Mulai buffer output untuk menangkap pesan log dan error
    ob_start();

    // Ambil semua cronjob yang ada
    $cron_jobs = $this->get_cron();

    // Cari cronjob berdasarkan nama
    foreach ($cron_jobs as $cron_job) {
        if ($cron_job['name'] == $name) {
            // Eksekusi cronjob berdasarkan ID dengan parameter situs
            $result = $this->execute_cron_with_parameter($cron_job['id'], $dbname);
            
            // Tangkap semua output (termasuk error) dan bersihkan buffer
            $output = ob_get_clean();

            // Periksa apakah output kosong, dan jika kosong beri pesan default
            if (empty($output)) {
                $output = "No output captured from cron job.";
            }

            // Kembalikan hasil eksekusi termasuk output log secara lengkap
            return response()->json([
                'status' => $result['status'],
                'msg' => $result['msg'],
                'output' => $output
            ]);
        }
    }

    // Jika cronjob tidak ditemukan, bersihkan buffer dan kembalikan pesan yang sesuai
    ob_end_clean();
    return response()->json([
        'status' => false,
        'msg' => 'Cron job tidak ditemukan!',
        'output' => 'No output because cron job not found.'
    ]);
}



public function check_cronjob_exists($name, $targetFolder)
{
    // Ambil semua cronjob yang ada
    $cron_jobs = $this->get_cron();

    // Periksa apakah cronjob dengan nama yang diberikan ada
    foreach ($cron_jobs as $cron_job) {
        if ($cron_job['name'] == $name) {
            // Jika cronjob ditemukan, kembalikan true dan folder tujuan
            return [
                'exists' => true,
                'target_folder' => $targetFolder
            ];
        }
    }

    // Jika tidak ditemukan, kembalikan false dan folder tujuan
    return [
        'exists' => false,
        'target_folder' => $targetFolder
    ];
}






//   public function add_site($domain)
//     {

//         $url = $this->BT_PANEL . '/site?action=AddSite';
//         $p_data = $this->GetKeyData();
//         $p_data['webname'] = '{"domain":"' . $domain . '","domainlist":[],"count":0}';
//         $p_data['port'] = 80;
//         $p_data['type'] = 'PHP';
//         $p_data['ps'] = str_replace('.', '_', $domain);
//         $p_data['path'] = '/www/wwwroot/' . $domain;
//         $p_data['type_id'] = 0;
//         $p_data['version'] = 74; // Contoh: PHP 7.4

//         $p_data['ftp'] = false;
//         $p_data['sql'] = false;
//         $p_data['codeing'] = 'utf8';
//         $p_data['set_ssl'] = 1;
//         $p_data['force_ssl'] = 1;
//         $result = $this->HttpPostCookie($url, $p_data);
//         $data = json_decode($result, true);
//         return $data;
//     }
public function add_site($domain)
{
    $url = $this->BT_PANEL . '/site?action=AddSite';
    $p_data = $this->GetKeyData();
    $p_data['webname'] = '{"domain":"' . $domain . '","domainlist":[],"count":0}';
    $p_data['port'] = 80;
    $p_data['type'] = 'PHP';
    $p_data['ps'] = str_replace('.', '_', $domain);
    $p_data['path'] = '/www/wwwroot/' . $domain;
    $p_data['type_id'] = 0;
    $p_data['version'] = 74; // Example: PHP 7.4

    $p_data['ftp'] = false;
    $p_data['sql'] = false;
    $p_data['codeing'] = 'utf8';
    $p_data['set_ssl'] = 1;
    $p_data['force_ssl'] = 1;
    $result = $this->HttpPostCookie($url, $p_data);
    $data = json_decode($result, true);

    if ($data && isset($data['siteId'])) {
        // Site created successfully, now set the running directory to /public
        $this->set_site_directory($domain);
    }

    return $data;
}

public function update_running_directory_to_public($domain)
{
    // Path to the running directory
    $running_directory = '/public';

    // URL to update the site directory
    $url = $this->BT_PANEL . '/site?action=SetSiteDir';

    // Prepare the data for the request
    $p_data = $this->GetKeyData();
    $p_data['site_dir'] = '/www/wwwroot/' . $domain;
    $p_data['run_dir'] = $running_directory;
    $p_data['id'] = $this->get_site_id($domain); // Get the site ID based on the domain name

    // Send the request to update the running directory
    $result = $this->HttpPostCookie($url, $p_data);

    // Decode the response
    $data = json_decode($result, true);

    // Return the result
    return $data;
}



    private function check_dns_resolution($domain)
    {
        $ip_address = '***************'; // IP server client
        $dns_records = dns_get_record($domain, DNS_A);

        foreach ($dns_records as $record) {
            if ($record['type'] == 'A' && $record['ip'] == $ip_address) {
                return true;
            }
        }

        return false;
    }


//duplikate database 



    function add_site_json($subdomain)
    {
        if (is_file('config/subdomain.json')) {
            $jsonsubdomain = json_decode(file_get_contents('config/subdomain.json'), true);
            $ketemu = false;
            foreach ($jsonsubdomain as $key => $value) {
                if ($value == $subdomain) {
                    $ketemu = true;
                }
            }
            if ($ketemu == false) {
                array_push($jsonsubdomain, $subdomain);
                file_put_contents('config/subdomain.json', json_encode($jsonsubdomain));
            }
        } else {
            file_put_contents('config/subdomain.json', json_encode(array($subdomain)));
        }
    }

    public function update_site_path($domain)
    {
        $url = $this->BT_PANEL . '/site?action=SetPath';
        $p_data = $this->GetKeyData();
        $p_data['name'] = $domain;
        $p_data['path'] = '/www/wwwroot/' . $domain;
        $p_data['id'] = '';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }
    
    
    public function delete_domain_from_site($domain, $port = 80, $webname = "1ripit.gass.web.id", $id = 1) {
    $url = $this->BT_PANEL . '/site?action=DelDomain'; // Endpoint for deleting a domain
    $p_data = $this->GetKeyData();
    
    // Prepare the parameters for the request
    $p_data['domain'] = $domain;
    $p_data['webname'] = $webname;
    $p_data['id'] = $id;
    $p_data['port'] = $port; // Include the port parameter

    // Send the HTTP POST request to delete the domain
    $result = $this->HttpPostCookie($url, $p_data);
    $data = json_decode($result, true);

    return $data; // Return the response data
}

    
    public function add_domain_insite($domain, $webname="1ripit.gass.web.id", $id=1){
		$url = $this->BT_PANEL.'/site?action=AddDomain';
		$p_data = $this->GetKeyData();
		$p_data['domain'] = $domain;
		$p_data['webname'] = $webname;
		$p_data['id'] = $id;
		$result = $this->HttpPostCookie($url,$p_data);
		$data = json_decode($result,true);
      	return $data;
	}

    public function update_site_rewrite($domain)
    {
        $url = $this->BT_PANEL . '/files?action=SaveFileBody';
        $p_data = $this->GetKeyData();
        $p_data['data'] = 'location / {
  try_files $uri $uri/ /index.php?$query_string;
}';
        $p_data['path'] = '/www/server/panel/vhost/rewrite/' . $domain . '.conf';
        $p_data['encoding'] = 'utf-8';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);


        $p_data = $this->GetKeyData();
        $p_data['data'] = 'server
{
    listen 80;
		listen 443 ssl http2;
    server_name ' . $domain . ';
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/' . $domain . '/public;

    #SSL-START SSL related configuration, do NOT delete or modify the next line of commented-out 404 rules
    #error_page 404/404.html;
    ssl_certificate    /www/server/panel/vhost/cert/' . $domain . '/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/' . $domain . '/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    error_page 497  https://$host$request_uri;
		#SSL-END

    #ERROR-PAGE-START  Error page configuration, allowed to be commented, deleted or modified
    #error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP reference configuration, allowed to be commented, deleted or modified
    include enable-php-82.conf;
    #PHP-INFO-END

    #REWRITE-START URL rewrite rule reference, any modification will invalidate the rewrite rules set by the panel
    include /www/server/panel/vhost/rewrite/' . $domain . '.conf;
    #REWRITE-END

    # Forbidden files or directories
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    # Directory verification related settings for one-click application for SSL certificate
    location ~ \.well-known{
        allow all;
    }

    #Prohibit putting sensitive files in certificate verification directory
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log off;
    }

    location ~ .*\.(css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log off; 
    }
    access_log  /www/wwwlogs/' . $domain . '.log;
    error_log  /www/wwwlogs/' . $domain . '.error.log;
}';
        $p_data['path'] = '/www/server/panel/vhost/nginx/' . $domain . '.conf';
        $p_data['encoding'] = 'utf-8';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);

        return $data;
        // Array ( [siteStatus] => 1 [siteId] => 11 [ftpStatus] => [databaseStatus] => [ssl] => 1 [redirect] => 1 ) 
    }

    public function add_site_proxy($domain)
    {
        $url = $this->BT_PANEL . '/site?action=CreateProxy';
        $p_data = $this->GetKeyData();
        $p_data['subfilter'] = '[{"sub1":"","sub2":""},{"sub1":"","sub2":""},{"sub1":"","sub2":""}]';
        $p_data['type'] = 1;
        $p_data['proxyname'] = str_replace('.', '_', $domain);
        $p_data['cachetime'] = 1;
        $p_data['proxydir'] = '/';
        $p_data['proxysite'] = 'http://cta.gass.co.id/';
        $p_data['todomain'] = 'cta.gass.co.id';
        $p_data['cache'] = 0;
        $p_data['advanced'] = 0;
        $p_data['sitename'] = $domain;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        //Array ( [status] => 1 [msg] => Setup successfully! ) 
    }


    public function delete_site($id, $domain)
    {
        $url = $this->BT_PANEL . '/site?action=DeleteSite';
        $p_data = $this->GetKeyData();
        $p_data['id'] = $id;
        $p_data['webname'] = $domain;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        //{"status": true, "msg": "Successfully deleted site!"}
    }

    public function get_site($domain)
    {
        $url = $this->BT_PANEL . '/data?action=getData';
        $p_data = $this->GetKeyData();
        $p_data['table'] = 'sites';
        $p_data['limit'] = '';
        $p_data['search'] = '';
        $p_data['p'] = 1;
        $p_data['type'] = -1;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        //Array ( [status] => 1 [msg] => Setup successfully! ) 
    }

// public function get_site_by_sitename($sitename)
// {
//     // Fetch all sites
//     $sites = $this->get_list_site();

//     // Check if the sites were fetched successfully
//     if (!$sites || !isset($sites['data'])) {
//         return [
//             'status' => false,
//             'msg' => 'Unable to fetch sites or no sites found.'
//         ];
//     }

//     // Search for the site by its name
//     foreach ($sites['data'] as $site) {
//         if ($site['name'] === $sitename) {
//             return [
//                 'status' => true,
//                 'data' => $site
//             ];
//         }
//     }

//     // If no site was found with the given name, return an error message
//     return [
//         'status' => false,
//         'msg' => 'Site not found with the name: ' . $sitename
//     ];
// }

// private function get_site_by_sitename($sitename)
// {
//     // Fetch all sites
//     $sites = $this->get_list_site();

//     // Check if the sites were fetched successfully
//     if (!$sites || !isset($sites['data'])) {
//         return [
//             'status' => false,
//             'msg' => 'Unable to fetch sites or no sites found.'
//         ];
//     }

//     // Search for the site by its name
//     foreach ($sites['data'] as $site) {
//         if ($site['name'] === $sitename) {
//             return [
//                 'status' => true,
//                 'data' => $site
//             ];
//         }
//     }

//     // If no site was found with the given name, return an error message
//     return [
//         'status' => false,
//         'msg' => 'Site not found with the name: ' . $sitename
//     ];
// }


public function set_running_directory_to_public($sitename)
{
    // First, get the site details by its sitename
    $siteDetails = $this->get_site_by_sitename($sitename);

    // Check if the site was found successfully
    if (!$siteDetails['status']) {
        return [
            'status' => false,
            'msg' => 'Site not found: ' . $sitename
        ];
    }

    // Extract the site ID from the retrieved details
    $siteId = $siteDetails['data']['id'];

    // Define the running directory as /public
    $runningDirectory = '/public';

    // Prepare the data for the request
    $url = $this->BT_PANEL . '/site?action=SetSiteDir';
    $p_data = $this->GetKeyData();
    $p_data['id'] = $siteId;
    $p_data['site_dir'] = $siteDetails['data']['path'];
    $p_data['run_dir'] = $runningDirectory;

    // Send the request to update the running directory
    $result = $this->HttpPostCookie($url, $p_data);
    $data = json_decode($result, true);

    // Return the result of the operation
    return $data;
}




 public function get_list_site() {
        $url = $this->BT_PANEL . '/data?action=getData';
        $p_data = $this->GetKeyData();
        $p_data['table'] = 'sites';
        $p_data['limit'] = 100; // Atur limit sesuai kebutuhan Anda
        $p_data['search'] = '';
        $p_data['p'] = 1;
        $p_data['type'] = -1;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }


    /// cron------------------------------------

    public function add_cron($name, $menit, $urlhook)
    {
        $url = $this->BT_PANEL . '/crontab?action=AddCrontab';
        $p_data = $this->GetKeyData();
        $p_data['name'] = $name;
        $p_data['type'] = 'minute-n';
        $p_data['where1'] = $menit;
        $p_data['hour'] = '';
        $p_data['minute'] = '';
        $p_data['week'] = '';
        $p_data['sType'] = 'toUrl';
        $p_data['sBody'] = 'undefined';
        $p_data['sName'] = '';
        $p_data['backupTo'] = 'localhost';
        $p_data['save'] = '';
        $p_data['urladdress'] = $urlhook;
        $p_data['save_local'] = 0;
        $p_data['notice'] = 'undefined';
        $p_data['notice_channel'] = 'undefined';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        // {"status": true, "msg": "Setup successfully!"}
    }

    public function get_cron()
    {
        $url = $this->BT_PANEL . '/crontab?action=GetCrontab';
        $p_data = $this->GetKeyData();
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        //Array ( [status] => 1 [msg] => Setup successfully! ) 
    }



    public function delete_cron_name($name)
    {
        $status = false;
        $data = $this->get_cron();
        foreach ($data as $k => $v) {
            if ($v['name'] == $name) {
                $this->delete_cron($v['id']);
                $status = true;
            }
        }
        return $status;
    }

//update configuration
public function set_site_directory($sitename)
{
    $site_directory = '/www/wwwroot/' . $sitename;
    $running_directory = '/public';
    $url = $this->BT_PANEL . '/site?action=SetSiteRunPath';
    $site_id = $this->get_site_id($sitename);
    if (!$site_id) {
        return ['status' => false, 'msg' => 'Site ID not found for sitename: ' . $sitename];
    }

    $p_data = $this->GetKeyData();
    //$p_data['site_dir'] = $site_directory;
    $p_data['runPath'] = $running_directory;
    $p_data['id'] = $site_id;
    
    Log::info('Data yang dikirim ke API', $p_data);
    $result = $this->HttpPostCookie($url, $p_data);
    $data = json_decode($result, true);
    return $data;
}


private function get_site_id($domain)
{
    // Fetch all sites
    $sites = $this->get_list_site();

    // Check if the sites were fetched successfully
    if (!$sites || !isset($sites['data'])) {
        return null;
    }

    // Search for the site ID based on the domain
    foreach ($sites['data'] as $site) {
        if ($site['name'] === $domain) {
            return $site['id'];
        }
    }

    // Return null if site ID is not found
    return null;
}



    public function delete_cron($id)
    {
        $url = $this->BT_PANEL . '/crontab?action=DelCrontab';
        $p_data = $this->GetKeyData();
        $p_data['id'] = $id;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
        //{"status": true, "msg": "Successfully deleted site!"}
    }
    ////////////////// end cron

    public function GetLogs()
    {
        $url = $this->BT_PANEL . '/data?action=getData';
        $p_data = $this->GetKeyData();
        $p_data['table'] = 'logs';
        $p_data['limit'] = 10;
        $p_data['tojs'] = 'test';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }

    private function GetKeyData()
    {
        $now_time = time();
        $p_data = array(
            'request_token'    =>    md5($now_time . '' . md5($this->BT_KEY)),
            'request_time'    =>    $now_time
        );
        return $p_data;
    }
    
    public function delete_db($dbname)
{
    $url = $this->BT_PANEL . '/database?action=DelDatabase';
    $p_data = $this->GetKeyData();
    $p_data['name'] = $dbname;
    $result = $this->HttpPostCookie($url, $p_data);
    $data = json_decode($result, true);
    return $data;
}


    private function HttpPostCookie($url, $data, $timeout = 60)
    {
        $cookie_file = './' . md5($this->BT_PANEL) . '.cookie';
        if (!file_exists($cookie_file)) {
            //$fp = fopen($cookie_file,'w+');
            //fclose($fp);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_file);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_file);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
}
