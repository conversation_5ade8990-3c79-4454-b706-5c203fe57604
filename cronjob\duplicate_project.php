<?php
function duplicateProject() {
    // Folder proyek utama
    $sourceFolder = '/www/wwwroot/project_utama';

    // Cari folder client yang ada
    $rootFolder = '/www/wwwroot/';
    $folders = glob($rootFolder . '*client.ripit.id', GLOB_ONLYDIR);

    // Temukan folder dengan angka tertinggi
    $highestNumber = 0;
    foreach ($folders as $folder) {
        preg_match('/(\d+)client\.ripit\.id/', $folder, $matches);
        if (isset($matches[1]) && $matches[1] > $highestNumber) {
            $highestNumber = (int) $matches[1];
        }
    }

    // Tentukan folder tujuan baru berdasarkan angka tertinggi + 1
    $newNumber = $highestNumber ;
    $destinationFolder = $rootFolder . $newNumber . 'client.ripit.id';

    // Buat folder tujuan jika belum ada
    if (!file_exists($destinationFolder)) {
        if (!mkdir($destinationFolder, 0755, true)) {
            error_log("Gagal membuat folder tujuan: $destinationFolder");
            return;
        }
    }

    // Rekursif menyalin semua file dan folder dari sumber ke tujuan
    function recurseCopy($src, $dst) {
        $dir = opendir($src);
        if (!$dir) {
            error_log("Gagal membuka direktori sumber: $src");
            return;
        }
        @mkdir($dst, 0755, true);
        while (false !== ($file = readdir($dir))) {
            if (($file != '.') && ($file != '..')) {
                if (is_dir($src . '/' . $file)) {
                    // Mengatur izin direktori sebelum menyalin
                    chmod($src . '/' . $file, 0755);
                    recurseCopy($src . '/' . $file, $dst . '/' . $file);
                } else {
                    // Mengatur izin file sebelum menyalin
                    chmod($src . '/' . $file, 0644);
                    if (!@copy($src . '/' . $file, $dst . '/' . $file)) {
                        error_log("Gagal menyalin file: $src/$file ke $dst/$file, mengabaikan dan melanjutkan");
                    }
                }
            }
        }
        closedir($dir);
    }

    recurseCopy($sourceFolder, $destinationFolder);

    // Update file .env di folder tujuan
    $envFile = $destinationFolder . '/.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        if ($envContent === false) {
            error_log("Gagal membaca file .env di $envFile");
            return;
        }

        // Sesuaikan nilai DB_DATABASE, DB_USERNAME, dan DB_PASSWORD
        $newDatabaseName = str_replace('client.ripit.id', '_ripit', $newNumber . 'client.ripit.id');
        $newUsername = $newDatabaseName; // DB_USERNAME = DB_DATABASE sesuai permintaan
        $newPassword = $newDatabaseName . '123123123'; // DB_PASSWORD sesuai dengan format yang diminta
        
        // Ganti atau tambahkan variabel DB_DATABASE, DB_USERNAME, dan DB_PASSWORD
        $envContent = preg_replace('/^DB_DATABASE=.*$/m', 'DB_DATABASE=' . $newDatabaseName, $envContent);
        $envContent = preg_replace('/^DB_USERNAME=.*$/m', 'DB_USERNAME=' . $newUsername, $envContent);
        
        // Pastikan DB_PASSWORD terisi, bahkan jika tidak ada sebelumnya
        if (preg_match('/^DB_PASSWORD=.*$/m', $envContent)) {
            $envContent = preg_replace('/^DB_PASSWORD=.*$/m', 'DB_PASSWORD=' . $newPassword, $envContent);
        } else {
            $envContent .= "\nDB_PASSWORD=" . $newPassword;
        }

        if (file_put_contents($envFile, $envContent) === false) {
            error_log("Gagal menulis ke file .env di $envFile");
            return;
        }
    } else {
        error_log("File .env tidak ditemukan di $destinationFolder");
        return;
    }

    // Set permissions untuk folder storage dan bootstrap/cache
    chmod($destinationFolder . '/storage', 0755);
    chmod($destinationFolder . '/bootstrap/cache', 0755);

    // Set owner ke www-data (atau user web server yang sesuai)
    $owner = 'www-data'; // Sesuaikan dengan user yang digunakan web server Anda
    exec("chown -R $owner:$owner $destinationFolder/storage");
    exec("chown -R $owner:$owner $destinationFolder/bootstrap/cache");

    error_log("Proyek berhasil disalin ke $destinationFolder dengan permission yang benar.");
}

// Jalankan fungsi untuk menduplikasi proyek
duplicateProject();
