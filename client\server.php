<?php

// Mengekstrak subdomain dari URL
// $subdomain = explode('.', $_SERVER['HTTP_HOST'])[0];
// echo $_SERVER['HTTP_HOST'];

// // Mengekstrak parameter dari URL
// $parameter = $_GET['parameter'] ?? '';

// // Jika parameter adalah 'pian', 'newUser', 'wahyu', 'jono', atau nilai lainn<PERSON>, ubah alamat host
// if (!empty($parameter)) {
//     echo "Parameter is " . $parameter;
//     // $_SERVER['HTTP_HOST'] = $parameter . '.' . $subdomain . '.localhost:8080';
// }

// Memuat file index.php Laravel
// require_once __DIR__ . '/public/index.php';

//jika server jalan maka request akan diarahkan ke public/index.php
if (isset($_SERVER['HTTP_HOST'])) {
    $host = $_SERVER['HTTP_HOST'];
    $host = explode('.', $host);
    $host = $host[0];
    //get list nama config yang ada pada folder /app/config 
    $array = array_diff(scandir('app/config'), array('..', '.'));
    // ambil katan sebelum . 
    $array = array_map(function ($value) {
        return explode('.', $value)[0];
    }, $array);
    foreach ($array as $value) {
        if ($host == $value) {
            require_once __DIR__ . '/public/index.php';
            return;
        } else {
            echo "404";
        }
    }
}
