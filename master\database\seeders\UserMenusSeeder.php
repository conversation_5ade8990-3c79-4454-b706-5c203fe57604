<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserMenusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user_id = 4;

        // Insert data ke user_menus sesuai urutan menu yang telah didefinisikan
        DB::table('user_menus')->insert([
            ['user_id' => $user_id, 'menu_id' => 1, 'status' => 1],  // Akses ke Dashboard
            ['user_id' => $user_id, 'menu_id' => 2, 'status' => 1],  // Akses ke Orders
            ['user_id' => $user_id, 'menu_id' => 3, 'status' => 1],  // Akses ke Product
            ['user_id' => $user_id, 'menu_id' => 4, 'status' => 1],  // Akses ke Report
            ['user_id' => $user_id, 'menu_id' => 5, 'status' => 1],  // Akses ke Customers
            ['user_id' => $user_id, 'menu_id' => 6, 'status' => 1],  // Akses ke CS-Broadcast
            ['user_id' => $user_id, 'menu_id' => 7, 'status' => 1],  // Akses ke Customer Service
            ['user_id' => $user_id, 'menu_id' => 8, 'status' => 1],  // Akses ke Business
            ['user_id' => $user_id, 'menu_id' => 9, 'status' => 1],  // Akses ke Integration
            ['user_id' => $user_id, 'menu_id' => 10, 'status' => 1], // Akses ke Gudang
            ['user_id' => $user_id, 'menu_id' => 11, 'status' => 1], // Akses ke Discount
            ['user_id' => $user_id, 'menu_id' => 12, 'status' => 1], // Akses ke MasterApi
            ['user_id' => $user_id, 'menu_id' => 13, 'status' => 1], // Akses ke Manage Project
            ['user_id' => $user_id, 'menu_id' => 14, 'status' => 1], // Akses ke TIM
        ]);
    }
}
