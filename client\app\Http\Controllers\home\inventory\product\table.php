<?php
namespace App\Http\Controllers\home\inventory\product;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\products as tblProducts;
use App\Http\Controllers\config\index as Config;

class table extends Controller
{
    //
    public function main(Request $request)
    {
        $Config = new Config;
        //
        $src = str_replace(";", "", trim($request->search));
        $paging = trim($request->paging);
        $sort = trim($request->sort_name);

        $getdata = tblProducts::from("products as p")
        ->select(
            "p.id", "p.code", "p.name", "p.type", "p.price", "p.price_reseller", "p.weight", "p.weight_type",
            "uc.name as company_name", "p.created_at as date"
        )
        ->leftJoin("user_companies as uc", function($join)
        {
            $join->on("uc.id", '=', "p.company_id");
        })
        ->where([
            "p.status"      =>  1
        ]);

        $count = $getdata->count();

        if( $count === 0)
        {
            $data = [
                'message'       =>  'Data Tidak ditemukan',
                'response'      =>  ''
            ];

            return response()->json($data, 404);
        }

        //
        $gettable = $getdata->orderBy('p.name', $sort)
        ->take($Config->table(['paging'=>$paging])['paging_item'])
        ->skip($Config->table(['paging'=>$paging])['paging_limit'])
        ->get();

        foreach($gettable as $row)
        {
            $list[] = [
                'id'            =>  $row->id,
                'code'          =>  $row->code,
                'name'          =>  $row->name,
                'type'          =>  $row->type === 2 ? 'Maklon ' . $row->company_name : 'Produsen',
                'price'         =>  $row->price,
                'price_reseller'    =>  $row->price_reseller,
                'kode'          =>  '',
                'company'       =>  $row->company_name,
                'weight'        =>  $row->weight . $row->weight_type,
                'date'          =>  $Config->timeago($row->date)
            ];
            
        }

        $data = [
            'message'       =>  '',
            'response'      =>  [
                'list'          =>  $list,
                'paging'        =>  $paging,
                'total'         =>  $count,
                'countpage'     =>  ceil($count / $Config->table(['paging'=>$paging])['paging_item'] )
            ]
        ];

        return response()->json($data, 200);

    }
}