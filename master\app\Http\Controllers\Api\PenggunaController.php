<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\BaseController as BaseController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
// use Exception;
use App\Models\Project;
use App\Models\Profile;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
// use Illuminate\Support\Facades\Mail;
use App\Mail\InviteToProjectMail;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Illuminate\Support\Facades\View; // Tambahkan ini
use Aws\S3\S3Client;
use Illuminate\Support\Facades\Storage;
use Aws\S3\Exception\S3Exception;
use Illuminate\Support\Facades\Log;
use App\Models\PaketPackage;
use App\Services\WaService;
use App\Services\ErrorService;
use App\Services\ActivityService;
class PenggunaController extends BaseController
{
    /**
     * Display a listing of the resource.
     */
   

   

public function index(Request $request): JsonResponse
{
    // Mengambil token dari header Authorization
    $token = $request->bearerToken();
    
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $user_id = $userToken->user_id;

    // Mengambil user beserta project yang terkait
    $user = User::with('projects')->find($user_id);

    if (!$user || !$user->projects || $user->projects->isEmpty()) {
        return response()->json([
            'code' => 0,
            'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
            'result' => []
        ], 200);
    }

    // Ambil project_id dari data project pertama yang dimiliki user
    // $projectId = $user->projects->first()->pivot->project_id;
    $project = DB::table('projects')
    ->whereRaw('BINARY project_key = UNHEX(?)', [$request->project_key])
    ->first();
    if($project == null){
        return response()->json([
            'message' => 'data project tidak ditemukan',
            'code' => 1,
            'response' => []
            ]);
    }
        $projectId = $project->id;

    // Ambil semua user_id yang terkait dengan project_id ini
    $userIds = DB::table('user_projects')
                ->where('project_id', $projectId)
                ->pluck('user_id');

   $search = $request->input('src', '');

    // Query untuk mendapatkan pengguna yang terkait dengan project_id
    $query = DB::table('users')
                ->leftJoin('profile', 'users.id', '=', 'profile.user_id')
                ->leftJoin('roles', 'users.role_id', '=', 'roles.id') // Join ke tabel roles
                ->whereIn('users.id', $userIds)
                ->select(
                    'users.id',
                    'users.name', 
                    'users.email', 
                    'profile.phone', 
                    'users.is_online as status', 
                    'profile.image', 
                    'roles.name as role_name' // Ambil role name dari tabel roles
                );

    // Jika ada parameter src, kita tambahkan kondisi pencarian
    if (!empty($search)) {
        $query->where(function($q) use ($search) {
            $q->where('users.name', 'like', '%' . $search . '%')
              ->orWhere('profile.phone', 'like', '%' . $search . '%')
              ->orWhere('users.email', 'like', '%' . $search . '%')
              ->orWhere('roles.name', 'like', '%' . $search . '%')
              ;
        });
    }

    // Pagination dengan 10 item per halaman
    $pengguna = $query->paginate(10);
    return response()->json([
        'code' => 1,
        'message' => 'Pengguna berhasil diambil.',
        'result' => $pengguna
    ], 200);
}




    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
     
    private $apiKey = '**************************************************';
    private $domain = 'email.ripit.id';

    /**
     * Private function to send email using Mailgun
     */
    private function sendEmail($to, $subject, $text, $html = null, $from = '<EMAIL>')
    {
        // Mailgun API endpoint
        $url = "https://api.mailgun.net/v3/{$this->domain}/messages";

        // Prepare the email data
        $postData = [
            'from' => $from,
            'to' => $to,
            'subject' => $subject,
            'text' => $text,
        ];

        // Add HTML content if provided
        if ($html) {
            $postData['html'] = $html;
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$this->apiKey}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        // Send the request and capture the response
        $result = curl_exec($ch);
        $error = curl_error($ch);

        // Close cURL session
        curl_close($ch);

        // Check for errors
        if ($error) {
            return "cURL Error: $error";
        }

        return $result;
    }
//new
public function store(Request $request): JsonResponse
{
    Log::info('Store user process started');

    $input = $request->all();
    $validator = Validator::make($input, [
        'name'          => 'required',
        'role'          => 'required',
        'email'         => 'required|email',
        'username'      => 'required',
        'jenis_kelamin' => 'required',
        'phone'         => 'required'
    ]);

    if ($validator->fails()) {
        Log::info('Validation failed', $validator->errors()->toArray());
        return response()->json(['message' => 'Validation Error', 'errors' => $validator->errors()], 422);
    }

    // Bersihkan phone hanya angka
    $cleanedPhone = preg_replace('/[^0-9]/', '', $request->phone);
    if (strlen($cleanedPhone) < 10) {
        Log::info('Phone number validation failed: less than 10 digits', ['phone' => $cleanedPhone]);
        return response()->json(['message' => 'Nomor telepon kurang dari 10 digit'], 422);
    }

    // Cari project berdasarkan project_key (binary)
    $project = DB::table('projects')
        ->whereRaw('BINARY project_key = UNHEX(?)', [$request->project_key])
        ->first();

    if ($project == null) {
        Log::info('Project not found', ['project_key' => $request->project_key]);
        return response()->json([
            'message' => 'data project tidak ditemukan',
            'code' => 1,
            'response' => []
        ]);
    }
    $project_id = $project->id;

    // Validasi token Bearer
    $token = $request->bearerToken();
    if (!$token) {
        Log::info('Bearer token missing');
        return response()->json(['message' => 'Unauthorized'], 401);
    }
    $tokenHash = hash("sha256", $token, true);
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();
    if (!$userToken) {
        Log::info('User token not found or invalid', ['tokenHash' => bin2hex($tokenHash)]);
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $user_id = $userToken->user_id;
    
    $appKey = config("app.key");

    // Cari user existing berdasar email
    $existingUser = User::where('email', $request->email)->first();

    if ($existingUser) {
        // User sudah ada, cek apakah sudah punya relasi project
        $hasProject = DB::table('user_projects')
            ->where('user_id', $existingUser->id)
            ->where('project_id', $project_id)
            ->exists();

        if ($hasProject) {
            Log::info('User already registered in project', ['email' => $request->email, 'project_id' => $project_id]);
            return response()->json(['message' => 'Email telah terdaftar di project Anda'], 422);
        }

        // Update data profile phone & jenis_kelamin jika perlu
        $profile = Profile::firstOrNew(['user_id' => $existingUser->id]);
        $profile->phone = $cleanedPhone;
        $profile->jenis_kelamin = $request->jenis_kelamin;
        $profile->save();

        // Insert relasi project
        DB::table('user_projects')->insert([
            'user_id' => $existingUser->id,
            'project_id' => $project_id,
        ]);

        Log::info('Existing user added to new project', ['user_id' => $existingUser->id, 'project_id' => $project_id]);

        // Kirim email undangan project dengan password baru (buat password random baru)
        $passwordview = Str::random(8);
        $pass = hex2bin(hash('sha256', $appKey . ";" . $request->email . ";" . $passwordview));

        // Update password user
        $existingUser->password = $pass;
        $existingUser->save();

        // Email invitation
        $dummyData = [
            'projectName' => $project->project_name,
            'email' => $request->email,
            'password' => $passwordview
        ];

        $emailContent = View::make('emails.invite', $dummyData)->render();
        $to = $request->email;
        $subject = 'Undangan Project ' . $dummyData['projectName'];
        $text = 'Anda diundang untuk bergabung dengan proyek ' . $dummyData['projectName'];
        $html = $emailContent;

        Log::info('Preparing to send email (existing user)', ['email' => $to]);

        // Kirim email
        $response = $this->sendEmail($to, $subject, $text, $html);

        Log::info('Email sent successfully (existing user)');
        $sendActivity = [
                        'users_nama' => $existingUser->name,
                        'users_email' => $existingUser->email,
                        'users_activity' => "Add Team",
                        'users_role' => 'member', 
                        'users_project_name' => $project->project_name
                    ];
                    
                    $result = $this->activityService->sendActivity($sendActivity);


        return response()->json([
            'message' => 'Berhasil Menambahkan Tim, Cek Email TIM untuk melihat password',
            'code' => 1,
            'response' => $response,
        ], 200);
    }

    // Jika user baru
    try {
        $user_key = hex2bin(md5($appKey . ";" . $request->input("email")));
        $passwordview = Str::random(8);
        $pass = hex2bin(hash('sha256', $appKey . ";" . $request->email . ";" . $passwordview));
        
        if ($request->role == 4) {
            $use_team_crm = $this->UpdateUseCRM($user_id,'add');
            if (!$use_team_crm) {
                return response()->json([
                    'message' => 'Jumlah Tim Anda melebihi batas, silahkan hapus beberapa Team CRM',
                    'code' => 0
                ], 500);
            }
        }
        $user = new User();
        $user->name = $request->name;
        $user->username = $request->username;
        $user->email = $request->email;
        $user->password = $pass;
        $user->role_id = $request->role;
        $user->user_key = $user_key;
        $user->verification = 1;
        $user->save();

        Log::info('User saved', ['user_id' => $user->id]);

        DB::table('user_projects')->insert([
            'user_id' => $user->id,
            'project_id' => $project_id,
        ]);

        Log::info('User project relation saved', ['user_id' => $user->id, 'project_id' => $project_id]);

        // Handle image upload jika ada
        $imageUrl = null;
        if ($request->hasFile('image')) {
            Log::info('Uploading image for user', ['user_id' => $user->id]);
            $image = $request->file('image');
            $imageName = str_replace(' ', '_', $image->getClientOriginalName());
            $bucket = "gss";
            $filePath = $imageName;
            $extension = strtolower($image->getClientOriginalExtension());
            $mimeType = match ($extension) {
                'jpg', 'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                default => 'application/octet-stream',
            };

            try {
                $result = $this->s3->putObject([
                    'Bucket' => $bucket,
                    'Key' => $filePath,
                    'Body' => fopen($image->getPathname(), 'r'),
                    'ContentType' => $mimeType,
                    'ACL' => 'public-read',
                ]);
                $imageUrl = $result['ObjectURL'];
                Log::info('Image uploaded', ['url' => $imageUrl]);
            } catch (S3Exception $e) {
                Log::error('S3 upload error: ' . $e->getMessage());
                return response()->json(['error' => $e->getMessage()], 500);
            }
        } else {
            Log::info('No image uploaded');
        }

        $profile = new Profile();
        $profile->user_id = $user->id;
        $profile->phone = $cleanedPhone;
        $profile->jenis_kelamin = $request->jenis_kelamin;
        $profile->image = $imageUrl;
        $profile->save();

        Log::info('Profile saved', ['user_id' => $user->id]);

        // Load project relation untuk user
        $user_project = User::with('projects')->find($user->id);
        if (!$user_project || !$user_project->projects || $user_project->projects->isEmpty()) {
            Log::info('No project found for user', ['user_id' => $user->id]);
            return response()->json([
                'code' => 0,
                'message' => 'Tidak ada proyek yang ditemukan untuk pengguna ini.',
                'result' => []
            ], 200);
        }

        $user_project->projects->each(fn($project) => $project->project_key = bin2hex($project->project_key));

        $dummyData = [
            'projectName' => $user_project->projects[0]->project_name,
            'email'       => $request->email,
            'password'    => $passwordview,
        ];

        $emailContent = View::make('emails.invite', $dummyData)->render();

        $to = $request->email;
        $subject = 'Undangan Project ' . $dummyData['projectName'];
        $text = 'Anda diundang untuk bergabung dengan proyek ' . $dummyData['projectName'];
        $html = $emailContent;

        Log::info('Preparing to send email (new user)', ['email' => $to]);

        // Sync team dan kirim email berdasarkan role
        $project_key = bin2hex($user_project->projects[0]->project_key);
        $client_id = $user_project->projects[0]->id;
        $endpoint = $this->endpoint . '/sync-team?project_key=' . $project_key . '&client_id=' . $client_id;

        $data_cs = [
            'user_id'       => $user->id,
            'name'          => $request->name,
            'username'      => $request->username,
            'password'      => $passwordview,
            'email'         => $request->email,
            'phone'         => $cleanedPhone,
            'jenis_kelamin' => $request->jenis_kelamin,
            'role_id'       => $request->role,
        ];

        Log::info('Sync team data', ['role' => $request->role, 'endpoint' => $endpoint, 'data' => $data_cs]);

        $responseSync = Http::post($endpoint, $data_cs);

        if ($responseSync->failed()) {
            Log::error('Failed to send sync-team data to client', ['role' => $request->role]);
            throw new \Exception('Gagal mengirim data ke client.');
        }

        if ($request->role == 4) {
            $generate = $this->generateMenuCs($user->id);
            
        } elseif ($request->role == 2) {
            $generate = $this->generateMenuAdvertiser($user->id);
        } else {
            Log::error('Role tidak dikenali atau tidak ditangani', ['role' => $request->role]);
            return response()->json([
                'message' => 'Gagal Menambahkan Tim, Role tidak dikenali',
                'code' => 1,
                'response' => [],
            ], 500);
        }

        $responseEmail = $this->sendEmail($to, $subject, $text, $html);
        Log::info('Email sent successfully', ['role' => $request->role]);
        
          $sendActivity = [
                        'users_nama' => $request->name,
                        'users_email' => $request->email,
                        'users_activity' => "Add Team",
                        'users_role' => 'member', 
                        'users_project_name' => $user_project->projects[0]->project_name
                    ];
                    
                    $result = $this->activityService->sendActivity($sendActivity);
        
        
        return response()->json([
            'message' => 'Berhasil Menambahkan Tim, Cek Email TIM untuk melihat password',
            'code' => 1,
            'response' => $responseEmail,
        ], 200);

    } catch (\Exception $e) {
        Log::error('Exception caught in store function: ' . $e->getMessage());
        return response()->json([
            'message' => 'Gagal Menambahkan Tim, Silahkan Coba Lagi',
            'code' => 1,
            'response' => [],
        ], 500);
    }
}


  public function getRole(Request $request): JsonResponse
    {
        // Query the roles table to find roles where the name is not 'owner'
        $roles = DB::table('roles')
                    ->where('name', '!=', 'owner')
                    ->get();

        if ($roles->isEmpty()) {
            return $this->sendResponse(null, 'No roles found other than owner', 404);
        } else {
            return $this->sendResponse($roles, 'Roles successfully retrieved');
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request,string $id)
    {

        $token = $request->bearerToken();
        $tokenHash = hash("sha256", $token, true);
        $user_id = $id;
        $project_id = DB::table('user_projects')->where('user_id',$id)->first()->project_id;
        $usersWithProfile = DB::table('users')
        ->join('profile', 'users.id', '=', 'profile.user_id')
        ->leftJoin('roles', 'users.role_id', '=', 'roles.id')
        ->where('users.id', $id) // Exclude user with ID 1
        ->select(
            'users.id as id',
            'profile.image',
            'users.name as name',
            'profile.phone',
            'profile.jenis_kelamin',
            'users.email',
            'users.username',
            'roles.name as role_name',
            'roles.id as role_id'
            )->first(); // Paginate the results, 10 per page
    return response()->json([
        'message' => 'Data Pengguna Berhasil Didapatkan',
        'code' => 1,
        'result' => $usersWithProfile,
    ]);        
    }

public function update(Request $request, $id)
{
        $token = $request->bearerToken();
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }
    
    $input = $request->all();

    // Validasi input
    $validator = Validator::make($input, [
        'name' => 'required|string|max:255',
        'username' => 'required|string|max:255',
        'jenis_kelamin' => 'required|string|max:255',
        'phone' => 'required',
        'role' => 'required|integer|exists:roles,id', // Validasi role harus ada di table roles
        'email' => 'required|email|max:255',
    ]);

    if ($validator->fails()) {
        return response()->json(['message' => 'Validation Error.', 'errors' => $validator->errors()], 400);
    }

    $cleanedPhone = preg_replace('/[^0-9]/', '', $input['phone']);
    // Temukan user berdasarkan ID
    $user = User::find($id);
    if (!$user) {
        return response()->json(['message' => 'User not found.'], 404);
    }
    $cek_phone = DB::table('profile')->where(['user_id' => $id, 'phone' => $cleanedPhone])->first();
    if($cek_phone){
         return response()->json(['message' => 'Phone Sudah Terdaftar, gunakan nomer lain', 'code' => 0, 'response' => []], 500);
    }

    $cek = DB::table('users')->where('id', $id)->first()->role_id;
    if($cek == 1){
        return response()->json([
            'message' => 'Tidak dapat update roles Owner',
            'code' => 0,
            'result' => []
            ], 422);
        
    }
    // Perbarui data pengguna
    $user->name = $input['name'];
    $user->role_id = $input['role'];
    $user->email = $input['email'];
    $user->username = $input['username'];
    $user->save();

    // Temukan profil berdasarkan user_id
    $profile = Profile::where('user_id', $id)->first();
    if (!$profile) {
        return response()->json(['message' => 'Profile not found.'], 404);
    }

    // Perbarui data profil
    $profile->phone = $cleanedPhone;
    $profile->jenis_kelamin = $input['jenis_kelamin'];

    // Jika ada gambar baru yang di-upload
    if ($request->hasFile('image')) {
        $image = $request->file('image');
        
        // Periksa apakah nama file adalah "blob"
        if ($image->getClientOriginalName() !== 'blob') {
            // Jika nama file bukan "blob", lanjutkan proses unggah
            $imageName = str_replace(' ', '_', $image->getClientOriginalName());
            $bucket = "gss";  // Pastikan nama bucket benar
            $filePath = $imageName;

            $extension = strtolower($image->getClientOriginalExtension());
            $mimeType = 'application/octet-stream'; // Default tipe file bila tidak dikenal
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    $mimeType = 'image/jpeg';
                    break;
                case 'png':
                    $mimeType = 'image/png';
                    break;
                case 'gif':
                    $mimeType = 'image/gif';
                    break;
            }

            try {
                // Upload file ke S3
                $result = $this->s3->putObject([
                    'Bucket' => $bucket,
                    'Key' => $filePath,
                    'Body' => fopen($image->getPathname(), 'r'),
                    'ContentType' => $mimeType,
                    'ACL' => 'public-read',  // Akses publik
                ]);

                // URL dari file yang diupload
                $imageUrl = $result['ObjectURL'];

                // Simpan URL gambar baru di profil jika nama file berbeda
                $profile->image = $imageUrl;

            } catch (S3Exception $e) {
                // Tangani error
                return response()->json(['error' => 'Failed to upload image to S3: ' . $e->getMessage()], 500);
            }
        }
    }

    // Simpan profil yang diperbarui tanpa menyentuh kolom 'image' jika nama file "blob"
    $profile->save();

    return response()->json([
        'message' => 'Data Pengguna Berhasil Diperbarui',
        'code' => 1,
        'result' => [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $cleanedPhone,
            'role_id' => $request->role,
            'image' => $profile->image  // Mengambil URL gambar terbaru atau lama
        ]
    ], 200);
}


    /**
     * Remove the specified resource from storage.
     */
    //  public function update()
    public function destroy(Request $request, $id)
    {
    
    $token = $request->bearerToken();
    // Menghasilkan hash dari token menggunakan SHA-256
    $tokenHash = hash("sha256", $token, true);
    
    // Cari user berdasarkan token hash
    $userToken = DB::table('user_tokens')->where('token', $tokenHash)->first();

    if (!$userToken) {
        return response()->json([
            'code' => 0,
            'message' => 'Token tidak valid atau pengguna tidak ditemukan.',
            'result' => []
        ], 404);
    }

    $project = DB::table('projects')
    ->whereRaw('BINARY project_key = UNHEX(?)', [$request->project_key])
    ->first();
    if($project == null){
        return response()->json([
            'message' => 'data project tidak ditemukan',
            'code' => 1,
            'response' => []
            ]);
    }
    $project_id = $project->id;
    
    $cek = DB::table('user_projects')->where([
        'user_id' => $id,
        'project_id' => $project_id
        ])->first();
    if($cek == null){
        return response()->json([
            'message' => "Tidak ada Tim dalam Project Anda",
            'code' => 0,
            'result' => []
            
        ],422);
    }
        // Eksekusi delete dari user_projects berdasarkan user_id dan project_id
    $users_CRM = DB::table('users')
      ->where([
          'id' => $id,
          'role_id' => 4
          ])->first();
    //delete
    if ($users_CRM) {
            $use_team_crm = $this->UpdateUseCRM($id,'delete');
            if (!$use_team_crm) {
                return response()->json([
                    'message' => 'Jumlah Tim Anda melebihi batas, silahkan hapus beberapa Team CRM',
                    'code' => 0
                ], 500);
            }
    }
    $user = DB::table('users')
      ->where('id', $id)->first();
     $sendActivity = [
                        'users_nama' => $user->name,
                        'users_email' => $user->email,
                        'users_activity' => "Delete Team",
                        'users_role' => 'member', 
                        'users_project_name' => $project->project_name
                    ];
                    
    $result = $this->activityService->sendActivity($sendActivity);
    
    
    DB::table('users')
      ->where('id', $id)
      ->delete();
    
    
    // Return response setelah berhasil menghapus data
    return response()->json([
        'message' => 'Anggota berhasil dihapus dari project.',
        'code' => 1,
        'result' => []
    ], 200);
    }
    
      public function getNohp(Request $request)
    {

        // Fetch the profile based on no_hp
        $profile = Profile::where('no_hp', $request->no_hp)->first();
                
        if ($profile) {
            $user = User::where('id', $profile->user_id)->first();
            
            return response()->json([
                'id' => $user->id,
                'nick_name' => $user->name,
                'name' => $profile->nama_lengkap,
                'jenis_kelamin' => $profile->jenis_kelamin,
                'no_hp' => $profile->no_hp,
                'email' => $user->email
            ], 200);
        } else {
            return response()->json(['message' => 'Profile not found'], 404);
        }
    }

 public function getEmail(Request $request)
    {
        // Fetch the user based on email
        $user = User::where('email', $request->email)->first();

        if ($user) {
            // Fetch the profile associated with the user
            $profile = Profile::where('user_id', $user->id)->first();

            return response()->json([
                'id' => $user->id,
                'nick_name' => $user->name,
                'name' => $profile ? $profile->nama_lengkap : null,
                'jenis_kelamin' => $profile ? $profile->jenis_kelamin : null,
                'no_hp' => $profile ? $profile->no_hp : null,
                'email' => $user->email,
            ], 200);
        } else {
            return response()->json(['message' => 'User not found'], 404);
        }
    }
    
   

public function syncTeam(Request $request)
{
    $user = User::find($request->user_id);

    if (!$user) {
        return response()->json(['message' => 'user tidak ditemukan'], 400);
    }

    $profile = Profile::where('user_id', $user->id)->first();

    // Ambil project
    $projects = DB::table('projects')
        ->join('user_projects', 'projects.id', '=', 'user_projects.project_id')
        ->where('user_projects.user_id', $user->id)
        ->select('projects.*')
        ->get()
        ->map(function ($project) {
            $project->project_key = bin2hex($project->project_key); // aman untuk JSON
            return $project;
        });

    $appKey = config("app.key");
    $passwordview = Str::random(8);
    // $cleanedPhone = preg_replace('/[^0-9]/', '', $profile->phone ?? '');

    // Hasil log sinkronisasi endpoint
    $sync_results = [];

    foreach ($projects as $project) {

        $project_key = bin2hex($project->project_key);
        $client_id = $project->id;
        $endpoint = $this->endpoint . '/sync-team?project_key=' . $project_key . '&client_id=' . $client_id;
        
        $fullEndpoint = $endpoint;

        try {
            $data_cs = [
                'user_id' => $user->id,
                'name' => $user->name,
                'password' => $passwordview,
                'username' => $user->username,
                'email' => $user->email,
                'phone' => $profile->phone,
                'jenis_kelamin' => $profile->jenis_kelamin ?? null,
                'role_id' => $user->role_id,
            ];

            $response_cs = Http::post($fullEndpoint, $data_cs);

            $sync_results[] = [
                'endpoint' => $endpoint,
                'status' => $response_cs->successful() ? 'success' : 'failed',
                'response' => $response_cs->body()
            ];
        } catch (\Exception $e) {
            $sync_results[] = [
                'endpoint' => $endpoint,
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    return response()->json([
        'profile' => $profile,
        'projects' => $projects,
        'sync_status' => $sync_results
    ]);
}


    protected $s3;
    protected $endpoint;
    
     public function __construct(ActivityService $activityService)
    {
        $this->activityService = $activityService;
        // Konfigurasi kredensial AWS
        $this->s3 = new S3Client([
            'version' => 'latest',
            'region' => 'us-east-1', // Sesuaikan dengan region Anda
            'endpoint' => 'https://objects-us-east-1.dream.io',
            'credentials' => [
                'key' => 'DHTHCUQ7LA7HVHCFP3GC',
                'secret' => '1BUJHjr__UCwThLOfSLVjagnPod9J3E-dTL9WNee',
            ],
        ]);
        $this->endpoint = config('app.endpoint_client');
    }

  public function uploadImage(Request $request)
    {
        // Validasi token permintaan

        Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ])->validate();

        $image = $request->file('image');
        $imageName = $image->getClientOriginalName();
        $bucket = "gss";
        // Path di S3 tempat Anda ingin menyimpan gambar
        $filePath =  $imageName;
        // Coba untuk upload gambar ke S3
        try {
            // Upload file ke S3
            $result = $this->s3->putObject([
                'Bucket' => $bucket,
                'Key' => $filePath,
                'Body' => fopen($image, 'r'),
                'ContentType' => 'image/jpeg',
                'ACL' => 'public-read', // Tentukan hak akses objek
            ]);
            $imageUrl = $result['ObjectURL'];
            return $imageUrl;
        } catch (S3Exception $e) {
            // Tangani kesalahan jika gagal mengunggah gambar
            return $e->getMessage();
        }
    }



public function getUserMenu(Request $request)
{
    $user_id = $request->id;
    // cek
    $cek = DB::table('users')->where([
        'role_id' => 1,
        'id' => $user_id,
        ])->first();
    if($cek){
        return response()->json([
            'message' => "Owners Memiliki Hak Akses Penuh pada aplikasi",
            'code' => 0,
            'result'=>[]
            
            ],400);
    }

    // Ambil semua menu (semua akan ditampilkan, meskipun statusnya 0)
    $menus = DB::table('menus')
        ->get(['menus.id as menu_id', 'menus.status as menu_status', 'menus.name as menu_name', 'menus.link']);

    // Ambil sub-menu untuk setiap menu
    foreach ($menus as &$menu) {
        // Ambil sub-menu berdasarkan menu_id
        $subMenus = DB::table('sub_menus')
            ->where('menu_id', $menu->menu_id)
            ->get(['sub_menus.id as sub_menu_id', 'sub_menus.name as sub_menu_name', 'sub_menus.link', 'sub_menus.status as sub_menu_status']);

        // Cek apakah user memiliki akses ke menu (melalui user_menus)
        $userMenu = DB::table('user_menus')
            ->where('user_id', $user_id)
            ->where('menu_id', $menu->menu_id)
            ->first();

        // Tentukan menu_status berdasarkan akses user
        $menu->menu_status = $userMenu ? $userMenu->status : 0;

        foreach ($subMenus as &$subMenu) {
            // Cek apakah user memiliki akses ke sub-menu (melalui user_sub_menus)
            $userSubMenu = DB::table('user_sub_menus')
                ->where('user_id', $user_id)
                ->where('sub_menu_id', $subMenu->sub_menu_id)
                ->first();

            // Tentukan sub_menu_status berdasarkan akses user
            $subMenu->sub_menu_status = $userSubMenu ? $userSubMenu->status : 0;
        }

        // Tambahkan sub-menu ke menu
        $menu->sub_menus = $subMenus;
    }

    // Return response dalam format JSON
    return response()->json([
        'message' => "Data Menu pada Users Berhasil didapatkan",
        'code'    => 1,
        'result'  => ['menus' => $menus],
    ]);
}





public function updateUserMenuSubMenu(Request $request)
{
    // Ambil `id` user dari request
    $id = $request->id;

    // Validasi apakah user ada di database
    $cek_users = User::find($id);
    if (!$cek_users) {
        return response()->json([
            'message' => 'Data User Tidak ditemukan!',
            'code' => 0,
            'result' => []
        ], 404);
    }

    // Validasi input data
    $validator = Validator::make($request->all(), [
        'menus' => 'required|array',  // Harus ada array menus
        'menus.*.menu_id' => 'required|exists:menus,id',  // Validasi menu_id setiap menu
        'menus.*.menu_status' => 'required|in:0,1',  // Status menu harus 0 atau 1
        'menus.*.sub_menus' => 'array',  // Sub-menu harus array
        'menus.*.sub_menus.*.sub_menu_id' => 'required|exists:sub_menus,id',  // Validasi sub_menu_id setiap sub-menu
        'menus.*.sub_menus.*.sub_menu_status' => 'required|in:0,1',  // Status sub-menu harus 0 atau 1
    ]);

    // Jika validasi gagal, kembalikan error
    if ($validator->fails()) {
        return response()->json([
            'message' => $validator->errors()
        ], 422); // Status code 422 untuk unprocessable entity
    }

    // Loop untuk setiap menu yang dikirim
    foreach ($request->menus as $menuData) {
        // Update status untuk user-menu berdasarkan `menu_status`
        $userMenu = DB::table('user_menus')
            ->where('user_id', $id)
            ->where('menu_id', $menuData['menu_id'])
            ->first();

        if ($userMenu) {
            // Jika user sudah memiliki menu ini, update `menu_status`
            DB::table('user_menus')
                ->where('user_id', $id)
                ->where('menu_id', $menuData['menu_id'])
                ->update(['status' => $menuData['menu_status']]); // Update `menu_status`
        } else {
            // Jika belum ada, tambahkan entry baru untuk menu
            DB::table('user_menus')->insert([
                'user_id' => $id,
                'menu_id' => $menuData['menu_id'],
                'status' => $menuData['menu_status'], // Set `menu_status` sesuai request
            ]);
        }

        // Loop untuk setiap sub-menu dalam menu ini
        foreach ($menuData['sub_menus'] as $subMenuData) {
            // Cek apakah user sudah memiliki akses ke sub-menu ini
            $userSubMenu = DB::table('user_sub_menus')
                ->where('user_id', $id)
                ->where('sub_menu_id', $subMenuData['sub_menu_id'])
                ->first();

            if ($userSubMenu) {
                // Jika sudah ada, update `sub_menu_status`
                DB::table('user_sub_menus')
                    ->where('user_id', $id)
                    ->where('sub_menu_id', $subMenuData['sub_menu_id'])
                    ->update(['status' => $subMenuData['sub_menu_status']]); // Update `sub_menu_status`
            } else {
                // Jika belum ada, tambahkan entry baru untuk sub-menu
                DB::table('user_sub_menus')->insert([
                    'user_id' => $id,
                    'sub_menu_id' => $subMenuData['sub_menu_id'],
                    'status' => $subMenuData['sub_menu_status'], // Set `sub_menu_status` sesuai request
                ]);
            }
        }
    }

    return response()->json([
        'message' => 'Berhasil mengatur akses menu dan sub-menu.',
        'code' => 1
    ]);
}
public function testGenerate(Request $request)
{
    $user_id = $request->user_id;
    $project_id = $request->project_id;

    // Validasi user
    $users = DB::table('users')->where('id', $user_id)->first();
    if (!$users) {
        return response()->json(['error' => 'User tidak ditemukan.'], 404);
    }

    // Validasi profile
    $profile = DB::table('profile')->where('user_id', $user_id)->first();
    if (!$profile) {
        return response()->json(['error' => 'Profile tidak ditemukan.'], 404);
    }

    // Validasi project
    $project = DB::table('projects')->where('id', $project_id)->first();
    if (!$project || !$project->project_key) {
        return response()->json(['error' => 'Project tidak ditemukan atau project_key kosong.'], 404);
    }

    // Konversi project_key dari binary ke hex (tanpa prefix "0x")
    $binaryKey = is_resource($project->project_key)
        ? stream_get_contents($project->project_key)
        : $project->project_key;

    if (!$binaryKey) {
        return response()->json(['error' => 'project_key tidak bisa dibaca.'], 500);
    }

    $project_key = bin2hex($binaryKey);

    // Data CS untuk dikirim ke client
    $appKey = config("app.key");
    $user_key = hex2bin(md5($appKey . ";" . $users->email));
    $pass = hex2bin(hash('sha256', $appKey . ";" . $users->email . ";" . $users->name));
    $passwordview = Str::random(8);

    $data_cs = [
        'user_id'        => $users->id,
        'name'           => $users->name,
        'password'       => $passwordview,
        'username'       => $users->username,
        'email'          => $users->email,
        'phone'          => $profile->phone,
        'jenis_kelamin'  => $profile->jenis_kelamin,
        'role_id'        => $users->role_id,
    ];

    $client_id = $project_id;

    // Kirim ke endpoint client
    $endpoint = $this->endpoint . '/sync-team?project_key=' . $project_key . '&client_id=' . $client_id;
    // return response()->json($endpoint);
    $response_cs = Http::post($endpoint, $data_cs);

    if ($response_cs->failed()) {
        return response()->json(['error' => 'Gagal mengirim data ke client.'], 500);
    }

    return response()->json(['message' => 'berhasil']);
}

private function generateMenuCs($id)
{
    // Data array menu dan sub-menu
   $arrayVar = [
    "menus" => [
        [
            "menu_id" => 1,
            "menu_status" => 1,
            "menu_name" => "Dashboard",
            "link" => "/dashboard",
            "sub_menus" => [
                [
                    "sub_menu_id" => 1,
                    "sub_menu_name" => "Get Product",
                    "link" => "/dashboard/analysis/getProduct",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 2,
                    "sub_menu_name" => "Get Timeframe",
                    "link" => "/dashboard/analysis/getTimeframe",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 3,
                    "sub_menu_name" => "Get Province",
                    "link" => "/dashboard/analysis/getProvince",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 4,
                    "sub_menu_name" => "Analysis",
                    "link" =>
                        "/dashboard/analysis?timeframe=&startDate=&endDate=&product=All&province=All",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 2,
            "menu_status" => 1,
            "menu_name" => "Orders",
            "link" => "/orders",
            "sub_menus" => [
                [
                    "sub_menu_id" => 5,
                    "sub_menu_name" => "Product Table",
                    "link" =>
                        "/product/table?src=&sort=asc&status=1&pg=1&type=0",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 6,
                    "sub_menu_name" => "Table",
                    "link" =>
                        "/orders/table?pg=&sortdate=&start_date=&end_date=&status=-1",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 7,
                    "sub_menu_name" => "Create",
                    "link" => "/orders/create",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 8,
                    "sub_menu_name" => "Orders Verified",
                    "link" =>
                        "/orders/table?pg=&sortdate=&start_date=&end_date=&status=1",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 9,
                    "sub_menu_name" => "Orders Unverified",
                    "link" =>
                        "/orders/table?pg=&sortdate=&start_date=&end_date=&status=0",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 10,
                    "sub_menu_name" => "New Order (Widget)",
                    "link" => "/orders/widget/new",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 11,
                    "sub_menu_name" => "Update Price (Widget)",
                    "link" => "/orders/widget/updateprice",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 12,
                    "sub_menu_name" => "Update Qty (Widget)",
                    "link" => "/orders/widget/updateqty",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 13,
                    "sub_menu_name" => "Add Item (Widget)",
                    "link" => "/orders/widget/additem",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 14,
                    "sub_menu_name" => "Checkout",
                    "link" => "/orders/checkout",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 15,
                    "sub_menu_name" => "Submit",
                    "link" => "/orders/submit",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 16,
                    "sub_menu_name" => "Courier List (Widget)",
                    "link" => "/courier/list/widget",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 17,
                    "sub_menu_name" => "Courier Cost",
                    "link" =>
                        "/courier/cost?otype=&origin=&destination=&weight=&courier=&order_id=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 18,
                    "sub_menu_name" => "Set Courier Cost",
                    "link" => "/orders/courier/setcost",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 19,
                    "sub_menu_name" => "Payment Methods (List New)",
                    "link" =>
                        "/orders/metodepayment/list-new?courier_id=&type=&comp_id=&oid=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 20,
                    "sub_menu_name" => "Payment Methods",
                    "link" => "/orders/metodepayment",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 21,
                    "sub_menu_name" => "View Detail",
                    "link" => "/orders/vdetail?id=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 22,
                    "sub_menu_name" => "Approve Order",
                    "link" => "/orders/approve",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 3,
            "menu_status" => 1,
            "menu_name" => "Product",
            "link" => "/product",
            "sub_menus" => [
                [
                    "sub_menu_id" => 23,
                    "sub_menu_name" => "Create New Product",
                    "link" => "/product/create?type=new",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 24,
                    "sub_menu_name" => "Add Product Variant",
                    "link" => "/product/variant/add?id=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 25,
                    "sub_menu_name" => "Get Product Variant",
                    "link" => "/product/variant/get?id=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 26,
                    "sub_menu_name" => "Edit Product",
                    "link" => "/product/create?type=edit",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 27,
                    "sub_menu_name" => "Show Product",
                    "link" => "/product/show",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 28,
                    "sub_menu_name" => "Delete Product",
                    "link" => "/product/create?type=delete",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 29,
                    "sub_menu_name" => "Product Table",
                    "link" =>
                        "/product/table?src=&sort=asc&status=1&pg=1&type=0",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 30,
                    "sub_menu_name" => "Get Gudang",
                    "link" => "/product/gudang",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 31,
                    "sub_menu_name" => "Product Bulking",
                    "link" => "/product/bulking",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 32,
                    "sub_menu_name" => "Import Product",
                    "link" => "/product/import",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 33,
                    "sub_menu_name" => "Product Mapping",
                    "link" => "/product/mapping",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 34,
                    "sub_menu_name" => "Preview Product",
                    "link" => "/product/preview",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 35,
                    "sub_menu_name" => "Store Product",
                    "link" => "/product/store",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 4,
            "menu_status" => 0,
            "menu_name" => "Report",
            "link" => "/report",
            "sub_menus" => [
                [
                    "sub_menu_id" => 36,
                    "sub_menu_name" => "Advertiser Report List",
                    "link" => "/report/list?platform=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 37,
                    "sub_menu_name" => "Dashboard Product Analysis",
                    "link" => "/dashboard/analysis/getProduct",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 38,
                    "sub_menu_name" => "Advertising Accounts List",
                    "link" => "/advertising_accounts/list",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 39,
                    "sub_menu_name" => "Get Advertising Account ID",
                    "link" => "/advertising_accounts/getID",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 40,
                    "sub_menu_name" => "Create Report",
                    "link" => "/report/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 41,
                    "sub_menu_name" => "Update Report",
                    "link" => "/report/update",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 42,
                    "sub_menu_name" => "Delete Report",
                    "link" => "/report/delete",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 43,
                    "sub_menu_name" => "Report Metriks",
                    "link" => "/report/matrix",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 44,
                    "sub_menu_name" => "Table",
                    "link" => "/report/matrix/table",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 45,
                    "sub_menu_name" => "Get Platform",
                    "link" => "/report/matrix/getPlatform",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 46,
                    "sub_menu_name" => "Get Metric",
                    "link" => "/report/matrix/getMetric",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 47,
                    "sub_menu_name" => "Create",
                    "link" => "/report/matrix/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 48,
                    "sub_menu_name" => "Get ID",
                    "link" => "/report/matrix/getid",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 49,
                    "sub_menu_name" => "Update",
                    "link" => "/report/matrix/update",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 50,
                    "sub_menu_name" => "Delete",
                    "link" => "/report/matrix/delete",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 118,
                    "sub_menu_name" => "Delete All Metric",
                    "link" => "/report/matrix/delete-all-metric",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 5,
            "menu_status" => 1,
            "menu_name" => "Customers",
            "link" => "/customers",
            "sub_menus" => [
                [
                    "sub_menu_id" => 51,
                    "sub_menu_name" => "Export Customers Table",
                    "link" => "/customers/table/export",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 52,
                    "sub_menu_name" => "Customers Bulking",
                    "link" => "/customers/bulking",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 53,
                    "sub_menu_name" => "Get Segment",
                    "link" => "/customers/segment",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 54,
                    "sub_menu_name" => "Filter Segment",
                    "link" =>
                        "/customers/segment-filter?pg=1&sort=desc&segment_name=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 55,
                    "sub_menu_name" => "Get Lokasi",
                    "link" => "/customers/lokasi",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 56,
                    "sub_menu_name" => "Filter Lokasi",
                    "link" =>
                        "/customers/lokasi-filter?pg=1&sort=desc&kecamatan_name=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 57,
                    "sub_menu_name" => "Add Customer",
                    "link" => "/customers/manage/add?type=add",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 58,
                    "sub_menu_name" => "View/Edit Customer",
                    "link" => "/customers/vs-edit",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 59,
                    "sub_menu_name" => "Edit Customer",
                    "link" => "/customers/manage/add?type=edit",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 60,
                    "sub_menu_name" => "Delete Customer",
                    "link" => "/customers/delete",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 61,
                    "sub_menu_name" => "Customer Detail",
                    "link" => "/customers/detail",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 62,
                    "sub_menu_name" => "Import",
                    "link" => "/customers/import",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 63,
                    "sub_menu_name" => "Mapping",
                    "link" => "/customers/mapping",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 64,
                    "sub_menu_name" => "Preview",
                    "link" => "/customers/preview",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 65,
                    "sub_menu_name" => "Store",
                    "link" => "/customers/store",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 66,
                    "sub_menu_name" => "Cohort Analysis (Date Range)",
                    "link" => "/dashboard/cohort-analysis?start=&end=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 67,
                    "sub_menu_name" => "Cohort Analysis (Product)",
                    "link" => "/dashboard/cohort-analysis?start=&end=&product=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 128,
                    "sub_menu_name" => "Customers Segmentation",
                    "link" => "/dashboard/rfm-analysis/segment?groupBy=All",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 6,
            "menu_status" => 1,
            "menu_name" => "CS-Broadcast",
            "link" => "/cs/whatsapp",
            "sub_menus" => [
                [
                    "sub_menu_id" => 68,
                    "sub_menu_name" => "Get Phone",
                    "link" => "/cs/whatsapp?act=ripit_cs_getPhone",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 69,
                    "sub_menu_name" => "Get Tags",
                    "link" => "/cs/whatsapp?act=ripit_cs_getTags",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 70,
                    "sub_menu_name" => "Get Segment",
                    "link" => "/cs/whatsapp?act=ripit_cs_getSegment",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 71,
                    "sub_menu_name" => "Broadcast Add",
                    "link" =>
                        "/cs/whatsapp?filter_type=&filter_name=&act=ripit_bc_add",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 72,
                    "sub_menu_name" => "Broadcast Get",
                    "link" => "/cs/whatsapp?act=ripit_bc_get",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 73,
                    "sub_menu_name" => "Broadcast edit",
                    "link" => "/cs/whatsapp?act=ripit_bc_edit",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 74,
                    "sub_menu_name" => "Broadcast update",
                    "link" => "/cs/whatsapp?act=ripit_bc_update",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 7,
            "menu_status" => 1,
            "menu_name" => "Customer Service",
            "link" => "/cs/whatsapp",
            "sub_menus" => [
                [
                    "sub_menu_id" => 75,
                    "sub_menu_name" => "CS Setting",
                    "link" => "/cs/whatsapp?act=ripit_cs_setting",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 76,
                    "sub_menu_name" => "Add CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_add",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 77,
                    "sub_menu_name" => "CS Status",
                    "link" => "/cs/whatsapp?act=ripit_cs_status",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 78,
                    "sub_menu_name" => "Delete CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_delete",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 79,
                    "sub_menu_name" => "CS Pairing",
                    "link" => "/cs/whatsapp?act=ripit_cs_pairing",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 80,
                    "sub_menu_name" => "CS Reload",
                    "link" => "/cs/whatsapp?act=ripit_cs_reload",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 81,
                    "sub_menu_name" => "Get CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_get",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 82,
                    "sub_menu_name" => "Get CS Detail",
                    "link" => "/cs/whatsapp?act=ripit_cs_get_detail",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 83,
                    "sub_menu_name" => "Edit CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_edit",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 8,
            "menu_status" => 0,
            "menu_name" => "Business",
            "link" => "/business",
            "sub_menus" => [
                [
                    "sub_menu_id" => 84,
                    "sub_menu_name" => "Edit Business",
                    "link" => "/business/edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 85,
                    "sub_menu_name" => "Save Business",
                    "link" => "/business/save",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 86,
                    "sub_menu_name" => "Update VAT",
                    "link" => "/business/update-vat",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 9,
            "menu_status" => 0,
            "menu_name" => "Integration",
            "link" => "/integration",
            "sub_menus" => [
                [
                    "sub_menu_id" => 87,
                    "sub_menu_name" => "Get Public Token",
                    "link" => "/project/get-public-token",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 88,
                    "sub_menu_name" => "Generate Token",
                    "link" => "/project/generate-token",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 89,
                    "sub_menu_name" => "Send Orders",
                    "link" => "/project/send-orders",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 10,
            "menu_status" => 0,
            "menu_name" => "Gudang",
            "link" => "/project/gudang",
            "sub_menus" => [
                [
                    "sub_menu_id" => 90,
                    "sub_menu_name" => "Create Gudang",
                    "link" => "/project/gudang/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 91,
                    "sub_menu_name" => "Get Gudang ID",
                    "link" => "/project/gudang/getID",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 92,
                    "sub_menu_name" => "Update Gudang",
                    "link" => "/project/gudang/update",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 93,
                    "sub_menu_name" => "Delete Gudang",
                    "link" => "/project/gudang/delete",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 11,
            "menu_status" => 1,
            "menu_name" => "Discount",
            "link" => "/discount",
            "sub_menus" => [
                [
                    "sub_menu_id" => 94,
                    "sub_menu_name" => "Discount List",
                    "link" => "/discount/list",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 95,
                    "sub_menu_name" => "Discount Status",
                    "link" => "/discount/status",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 96,
                    "sub_menu_name" => "Create Discount",
                    "link" => "/discount/create",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 12,
            "menu_status" => 0,
            "menu_name" => "MasterApi",
            "link" => "https://api.ripit.id/api/auth/project",
            "sub_menus" => [
                [
                    "sub_menu_id" => 97,
                    "sub_menu_name" => "Get Provinsi",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/getProvinsi",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 98,
                    "sub_menu_name" => "Get City",
                    "link" => "https://api.ripit.id/api/auth/project/getCity",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 99,
                    "sub_menu_name" => "Get Kecamatan",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/getKecamatan",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 100,
                    "sub_menu_name" => "Update Address",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/updateAddress",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 101,
                    "sub_menu_name" => "Create Project",
                    "link" => "https://api.ripit.id/api/auth/project",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 13,
            "menu_status" => 0,
            "menu_name" => "Manage Project",
            "link" => "https://api.ripit.id/api/auth/project/table",
            "sub_menus" => [
                [
                    "sub_menu_id" => 102,
                    "sub_menu_name" => "Get Project by ID",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/edit?id={id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 103,
                    "sub_menu_name" => "Update Project",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/update?id={id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 104,
                    "sub_menu_name" => "Delete Project",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/delete?id={id}",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 14,
            "menu_status" => 0,
            "menu_name" => "TIM",
            "link" => "https://api.ripit.id/api/auth/pengguna",
            "sub_menus" => [
                [
                    "sub_menu_id" => 105,
                    "sub_menu_name" => "Get Role",
                    "link" => "https://api.ripit.id/api/auth/pengguna/get-role",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 106,
                    "sub_menu_name" => "Create Role",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/create-role",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 107,
                    "sub_menu_name" => "Pengguna Page 1",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna?page=1&src=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 108,
                    "sub_menu_name" => "Edit Pengguna ID",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/{id}/edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 109,
                    "sub_menu_name" => "Update Pengguna",
                    "link" => "https://api.ripit.id/api/auth/pengguna/{id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 110,
                    "sub_menu_name" => "Delete Pengguna",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/delete/{id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 111,
                    "sub_menu_name" => "Setting Pengguna ",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/getMenus/{id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 112,
                    "sub_menu_name" => "Add Menu Pengguna ",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/addMenus/{id}",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 15,
            "menu_status" => 1,
            "menu_name" => "Bank",
            "link" => "/bank",
            "sub_menus" => [
                [
                    "sub_menu_id" => 113,
                    "sub_menu_name" => "Bank Table",
                    "link" => "/bank/table?src&pg=1",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 114,
                    "sub_menu_name" => "Bank Create",
                    "link" => "/bank/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 115,
                    "sub_menu_name" => "Bank GetID",
                    "link" => "/bank/getID",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 116,
                    "sub_menu_name" => "Bank Update",
                    "link" => "/bank/update",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 117,
                    "sub_menu_name" => "Bank Delete",
                    "link" => "/bank/delete",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
    ],
];

    // Insert menu
    foreach ($arrayVar['menus'] as $menu) {
        // Menyisipkan data menu ke tabel user_menus
        $userMenuData = [
            'user_id' => $id,
            'menu_id' => $menu['menu_id'],
            'status' => $menu['menu_status'],
        ];

        $userMenu = DB::table('user_menus')->where('user_id', $id)
            ->where('menu_id', $menu['menu_id'])->first();

        if ($userMenu) {
            // Update jika sudah ada
            DB::table('user_menus')
                ->where('user_id', $id)
                ->where('menu_id', $menu['menu_id'])
                ->update($userMenuData);
        } else {
            // Insert jika belum ada
            DB::table('user_menus')->insert($userMenuData);
        }

        // Insert sub-menus
        foreach ($menu['sub_menus'] as $subMenu) {
            // Menyisipkan data sub-menu ke tabel user_sub_menus
            $userSubMenuData = [
                'user_id' => $id,
                'sub_menu_id' => $subMenu['sub_menu_id'],
                'status' => $subMenu['sub_menu_status'],
            ];

            $userSubMenu = DB::table('user_sub_menus')->where('user_id', $id)
                ->where('sub_menu_id', $subMenu['sub_menu_id'])->first();

            if ($userSubMenu) {
                // Update jika sudah ada
                DB::table('user_sub_menus')
                    ->where('user_id', $id)
                    ->where('sub_menu_id', $subMenu['sub_menu_id'])
                    ->update($userSubMenuData);
            } else {
                // Insert jika belum ada
                DB::table('user_sub_menus')->insert($userSubMenuData);
            }
        }
    }

    return response()->json([
        'message' => 'Menu dan sub-menu berhasil diatur.',
        'code' => 1
    ]);
}

private function generateMenuAdvertiser($id)
{
    // Data array menu dan sub-menu
   $arrayVar = [
    "menus" => [
        [
            "menu_id" => 1,
            "menu_status" => 1,
            "menu_name" => "Dashboard",
            "link" => "/dashboard",
            "sub_menus" => [
                [
                    "sub_menu_id" => 1,
                    "sub_menu_name" => "Get Product",
                    "link" => "/dashboard/analysis/getProduct",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 2,
                    "sub_menu_name" => "Get Timeframe",
                    "link" => "/dashboard/analysis/getTimeframe",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 3,
                    "sub_menu_name" => "Get Province",
                    "link" => "/dashboard/analysis/getProvince",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 4,
                    "sub_menu_name" => "Analysis",
                    "link" =>
                        "/dashboard/analysis?timeframe=&startDate=&endDate=&product=All&province=All",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 2,
            "menu_status" => 0,
            "menu_name" => "Orders",
            "link" => "/orders",
            "sub_menus" => [
                [
                    "sub_menu_id" => 5,
                    "sub_menu_name" => "Product Table",
                    "link" =>
                        "/product/table?src=&sort=asc&status=1&pg=1&type=0",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 6,
                    "sub_menu_name" => "Table",
                    "link" =>
                        "/orders/table?pg=&sortdate=&start_date=&end_date=&status=-1",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 7,
                    "sub_menu_name" => "Create",
                    "link" => "/orders/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 8,
                    "sub_menu_name" => "Orders Verified",
                    "link" =>
                        "/orders/table?pg=&sortdate=&start_date=&end_date=&status=1",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 9,
                    "sub_menu_name" => "Orders Unverified",
                    "link" =>
                        "/orders/table?pg=&sortdate=&start_date=&end_date=&status=0",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 10,
                    "sub_menu_name" => "New Order (Widget)",
                    "link" => "/orders/widget/new",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 11,
                    "sub_menu_name" => "Update Price (Widget)",
                    "link" => "/orders/widget/updateprice",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 12,
                    "sub_menu_name" => "Update Qty (Widget)",
                    "link" => "/orders/widget/updateqty",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 13,
                    "sub_menu_name" => "Add Item (Widget)",
                    "link" => "/orders/widget/additem",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 14,
                    "sub_menu_name" => "Checkout",
                    "link" => "/orders/checkout",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 15,
                    "sub_menu_name" => "Submit",
                    "link" => "/orders/submit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 16,
                    "sub_menu_name" => "Courier List (Widget)",
                    "link" => "/courier/list/widget",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 17,
                    "sub_menu_name" => "Courier Cost",
                    "link" =>
                        "/courier/cost?otype=&origin=&destination=&weight=&courier=&order_id=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 18,
                    "sub_menu_name" => "Set Courier Cost",
                    "link" => "/orders/courier/setcost",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 19,
                    "sub_menu_name" => "Payment Methods (List New)",
                    "link" =>
                        "/orders/metodepayment/list-new?courier_id=&type=&comp_id=&oid=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 20,
                    "sub_menu_name" => "Payment Methods",
                    "link" => "/orders/metodepayment",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 21,
                    "sub_menu_name" => "View Detail",
                    "link" => "/orders/vdetail?id=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 22,
                    "sub_menu_name" => "Approve Order",
                    "link" => "/orders/approve",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 3,
            "menu_status" => 0,
            "menu_name" => "Product",
            "link" => "/product",
            "sub_menus" => [
                [
                    "sub_menu_id" => 23,
                    "sub_menu_name" => "Create New Product",
                    "link" => "/product/create?type=new",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 24,
                    "sub_menu_name" => "Add Product Variant",
                    "link" => "/product/variant/add?id=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 25,
                    "sub_menu_name" => "Get Product Variant",
                    "link" => "/product/variant/get?id=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 26,
                    "sub_menu_name" => "Edit Product",
                    "link" => "/product/create?type=edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 27,
                    "sub_menu_name" => "Show Product",
                    "link" => "/product/show",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 28,
                    "sub_menu_name" => "Delete Product",
                    "link" => "/product/create?type=delete",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 29,
                    "sub_menu_name" => "Product Table",
                    "link" =>
                        "/product/table?src=&sort=asc&status=1&pg=1&type=0",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 30,
                    "sub_menu_name" => "Get Gudang",
                    "link" => "/product/gudang",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 31,
                    "sub_menu_name" => "Product Bulking",
                    "link" => "/product/bulking",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 32,
                    "sub_menu_name" => "Import Product",
                    "link" => "/product/import",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 33,
                    "sub_menu_name" => "Product Mapping",
                    "link" => "/product/mapping",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 34,
                    "sub_menu_name" => "Preview Product",
                    "link" => "/product/preview",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 35,
                    "sub_menu_name" => "Store Product",
                    "link" => "/product/store",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 119,
                    "sub_menu_name" => "Get Variant By ID",
                    "link" => "/product/variant/get-varian-byID?variant_id=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 120,
                    "sub_menu_name" => "Update Variant",
                    "link" => "/product/variant/update?variant_id=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 121,
                    "sub_menu_name" => "Delete Variant",
                    "link" => "/product/variant/delete?id=",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 4,
            "menu_status" => 1,
            "menu_name" => "Report",
            "link" => "/report",
            "sub_menus" => [
                [
                    "sub_menu_id" => 36,
                    "sub_menu_name" => "Advertiser Report List",
                    "link" => "/report/list?platform=",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 37,
                    "sub_menu_name" => "Dashboard Product Analysis",
                    "link" => "/dashboard/analysis/getProduct",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 38,
                    "sub_menu_name" => "Advertising Accounts List",
                    "link" => "/advertising_accounts/list",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 39,
                    "sub_menu_name" => "Get Advertising Account ID",
                    "link" => "/advertising_accounts/getID",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 40,
                    "sub_menu_name" => "Create Report",
                    "link" => "/report/create",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 41,
                    "sub_menu_name" => "Update Report",
                    "link" => "/report/update",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 42,
                    "sub_menu_name" => "Delete Report",
                    "link" => "/report/delete",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 43,
                    "sub_menu_name" => "Report Metriks",
                    "link" => "/report/matrix",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 44,
                    "sub_menu_name" => "Table",
                    "link" => "/report/matrix/table",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 45,
                    "sub_menu_name" => "Get Platform",
                    "link" => "/report/matrix/getPlatform",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 46,
                    "sub_menu_name" => "Get Metric",
                    "link" => "/report/matrix/getMetric",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 47,
                    "sub_menu_name" => "Create",
                    "link" => "/report/matrix/create",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 48,
                    "sub_menu_name" => "Get ID",
                    "link" => "/report/matrix/getid",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 49,
                    "sub_menu_name" => "Update",
                    "link" => "/report/matrix/update",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 50,
                    "sub_menu_name" => "Delete",
                    "link" => "/report/matrix/delete",
                    "sub_menu_status" => 1,
                ],
                [
                    "sub_menu_id" => 118,
                    "sub_menu_name" => "Delete All Metric",
                    "link" => "/report/matrix/delete-all-metric",
                    "sub_menu_status" => 1,
                ],
            ],
        ],
        [
            "menu_id" => 5,
            "menu_status" => 0,
            "menu_name" => "Customers",
            "link" => "/customers",
            "sub_menus" => [
                [
                    "sub_menu_id" => 51,
                    "sub_menu_name" => "Export Customers Table",
                    "link" => "/customers/table/export",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 52,
                    "sub_menu_name" => "Customers Bulking",
                    "link" => "/customers/bulking",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 53,
                    "sub_menu_name" => "Get Segment",
                    "link" => "/customers/segment",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 54,
                    "sub_menu_name" => "Filter Segment",
                    "link" =>
                        "/customers/segment-filter?pg=1&sort=desc&segment_name=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 55,
                    "sub_menu_name" => "Get Lokasi",
                    "link" => "/customers/lokasi",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 56,
                    "sub_menu_name" => "Filter Lokasi",
                    "link" =>
                        "/customers/lokasi-filter?pg=1&sort=desc&kecamatan_name=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 57,
                    "sub_menu_name" => "Add Customer",
                    "link" => "/customers/manage/add?type=add",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 58,
                    "sub_menu_name" => "View/Edit Customer",
                    "link" => "/customers/vs-edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 59,
                    "sub_menu_name" => "Edit Customer",
                    "link" => "/customers/manage/add?type=edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 60,
                    "sub_menu_name" => "Delete Customer",
                    "link" => "/customers/delete",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 61,
                    "sub_menu_name" => "Customer Detail",
                    "link" => "/customers/detail",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 62,
                    "sub_menu_name" => "Import",
                    "link" => "/customers/import",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 63,
                    "sub_menu_name" => "Mapping",
                    "link" => "/customers/mapping",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 64,
                    "sub_menu_name" => "Preview",
                    "link" => "/customers/preview",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 65,
                    "sub_menu_name" => "Store",
                    "link" => "/customers/store",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 66,
                    "sub_menu_name" => "Cohort Analysis (Date Range)",
                    "link" => "/dashboard/cohort-analysis?start=&end=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 67,
                    "sub_menu_name" => "Cohort Analysis (Product)",
                    "link" => "/dashboard/cohort-analysis?start=&end=&product=",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 6,
            "menu_status" => 0,
            "menu_name" => "CS-Broadcast",
            "link" => "/cs/whatsapp",
            "sub_menus" => [
                [
                    "sub_menu_id" => 68,
                    "sub_menu_name" => "Get Phone",
                    "link" => "/cs/whatsapp?act=ripit_cs_getPhone",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 69,
                    "sub_menu_name" => "Get Tags",
                    "link" => "/cs/whatsapp?act=ripit_cs_getTags",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 70,
                    "sub_menu_name" => "Get Segment",
                    "link" => "/cs/whatsapp?act=ripit_cs_getSegment",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 71,
                    "sub_menu_name" => "Broadcast Add",
                    "link" =>
                        "/cs/whatsapp?filter_type=&filter_name=&act=ripit_bc_add",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 72,
                    "sub_menu_name" => "Broadcast Get",
                    "link" => "/cs/whatsapp?act=ripit_bc_get",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 73,
                    "sub_menu_name" => "Broadcast edit",
                    "link" => "/cs/whatsapp?act=ripit_bc_edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 74,
                    "sub_menu_name" => "Broadcast update",
                    "link" => "/cs/whatsapp?act=ripit_bc_update",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 7,
            "menu_status" => 0,
            "menu_name" => "Customer Service",
            "link" => "/cs/whatsapp",
            "sub_menus" => [
                [
                    "sub_menu_id" => 75,
                    "sub_menu_name" => "CS Setting",
                    "link" => "/cs/whatsapp?act=ripit_cs_setting",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 76,
                    "sub_menu_name" => "Add CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_add",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 77,
                    "sub_menu_name" => "CS Status",
                    "link" => "/cs/whatsapp?act=ripit_cs_status",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 78,
                    "sub_menu_name" => "Delete CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_delete",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 79,
                    "sub_menu_name" => "CS Pairing",
                    "link" => "/cs/whatsapp?act=ripit_cs_pairing",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 80,
                    "sub_menu_name" => "CS Reload",
                    "link" => "/cs/whatsapp?act=ripit_cs_reload",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 81,
                    "sub_menu_name" => "Get CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_get",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 82,
                    "sub_menu_name" => "Get CS Detail",
                    "link" => "/cs/whatsapp?act=ripit_cs_get_detail",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 83,
                    "sub_menu_name" => "Edit CS",
                    "link" => "/cs/whatsapp?act=ripit_cs_edit",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 8,
            "menu_status" => 0,
            "menu_name" => "Business",
            "link" => "/business",
            "sub_menus" => [
                [
                    "sub_menu_id" => 84,
                    "sub_menu_name" => "Edit Business",
                    "link" => "/business/edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 85,
                    "sub_menu_name" => "Save Business",
                    "link" => "/business/save",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 86,
                    "sub_menu_name" => "Update VAT",
                    "link" => "/business/update-vat",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 9,
            "menu_status" => 0,
            "menu_name" => "Integration",
            "link" => "/integration",
            "sub_menus" => [
                [
                    "sub_menu_id" => 87,
                    "sub_menu_name" => "Get Public Token",
                    "link" => "/project/get-public-token",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 88,
                    "sub_menu_name" => "Generate Token",
                    "link" => "/project/generate-token",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 89,
                    "sub_menu_name" => "Send Orders",
                    "link" => "/project/send-orders",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 10,
            "menu_status" => 0,
            "menu_name" => "Gudang",
            "link" => "/project/gudang",
            "sub_menus" => [
                [
                    "sub_menu_id" => 90,
                    "sub_menu_name" => "Create Gudang",
                    "link" => "/project/gudang/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 91,
                    "sub_menu_name" => "Get Gudang ID",
                    "link" => "/project/gudang/getID",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 92,
                    "sub_menu_name" => "Update Gudang",
                    "link" => "/project/gudang/update",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 93,
                    "sub_menu_name" => "Delete Gudang",
                    "link" => "/project/gudang/delete",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 11,
            "menu_status" => 0,
            "menu_name" => "Discount",
            "link" => "/discount",
            "sub_menus" => [
                [
                    "sub_menu_id" => 94,
                    "sub_menu_name" => "Discount List",
                    "link" => "/discount/list",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 95,
                    "sub_menu_name" => "Discount Status",
                    "link" => "/discount/status",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 96,
                    "sub_menu_name" => "Create Discount",
                    "link" => "/discount/create",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 12,
            "menu_status" => 0,
            "menu_name" => "MasterApi",
            "link" => "https://api.ripit.id/api/auth/project",
            "sub_menus" => [
                [
                    "sub_menu_id" => 97,
                    "sub_menu_name" => "Get Provinsi",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/getProvinsi",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 98,
                    "sub_menu_name" => "Get City",
                    "link" => "https://api.ripit.id/api/auth/project/getCity",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 99,
                    "sub_menu_name" => "Get Kecamatan",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/getKecamatan",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 100,
                    "sub_menu_name" => "Update Address",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/updateAddress",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 101,
                    "sub_menu_name" => "Create Project",
                    "link" => "https://api.ripit.id/api/auth/project",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 13,
            "menu_status" => 0,
            "menu_name" => "Manage Project",
            "link" => "https://api.ripit.id/api/auth/project/table",
            "sub_menus" => [
                [
                    "sub_menu_id" => 102,
                    "sub_menu_name" => "Get Project by ID",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/edit?id={id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 103,
                    "sub_menu_name" => "Update Project",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/update?id={id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 104,
                    "sub_menu_name" => "Delete Project",
                    "link" =>
                        "https://api.ripit.id/api/auth/project/delete?id={id}",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 14,
            "menu_status" => 0,
            "menu_name" => "TIM",
            "link" => "https://api.ripit.id/api/auth/pengguna",
            "sub_menus" => [
                [
                    "sub_menu_id" => 105,
                    "sub_menu_name" => "Get Role",
                    "link" => "https://api.ripit.id/api/auth/pengguna/get-role",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 106,
                    "sub_menu_name" => "Create Role",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/create-role",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 107,
                    "sub_menu_name" => "Pengguna Page 1",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna?page=1&src=",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 108,
                    "sub_menu_name" => "Edit Pengguna ID",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/{id}/edit",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 109,
                    "sub_menu_name" => "Update Pengguna",
                    "link" => "https://api.ripit.id/api/auth/pengguna/{id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 110,
                    "sub_menu_name" => "Delete Pengguna",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/delete/{id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 111,
                    "sub_menu_name" => "Setting Pengguna ",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/getMenus/{id}",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 112,
                    "sub_menu_name" => "Add Menu Pengguna ",
                    "link" =>
                        "https://api.ripit.id/api/auth/pengguna/addMenus/{id}",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
        [
            "menu_id" => 15,
            "menu_status" => 0,
            "menu_name" => "Bank",
            "link" => "/bank",
            "sub_menus" => [
                [
                    "sub_menu_id" => 113,
                    "sub_menu_name" => "Bank Table",
                    "link" => "/bank/table?src&pg=1",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 114,
                    "sub_menu_name" => "Bank Create",
                    "link" => "/bank/create",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 115,
                    "sub_menu_name" => "Bank GetID",
                    "link" => "/bank/getID",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 116,
                    "sub_menu_name" => "Bank Update",
                    "link" => "/bank/update",
                    "sub_menu_status" => 0,
                ],
                [
                    "sub_menu_id" => 117,
                    "sub_menu_name" => "Bank Delete",
                    "link" => "/bank/delete",
                    "sub_menu_status" => 0,
                ],
            ],
        ],
    ],
];

    // Insert menu
    foreach ($arrayVar['menus'] as $menu) {
        // Menyisipkan data menu ke tabel user_menus
        $userMenuData = [
            'user_id' => $id,
            'menu_id' => $menu['menu_id'],
            'status' => $menu['menu_status'],
        ];

        $userMenu = DB::table('user_menus')->where('user_id', $id)
            ->where('menu_id', $menu['menu_id'])->first();

        if ($userMenu) {
            // Update jika sudah ada
            DB::table('user_menus')
                ->where('user_id', $id)
                ->where('menu_id', $menu['menu_id'])
                ->update($userMenuData);
        } else {
            // Insert jika belum ada
            DB::table('user_menus')->insert($userMenuData);
        }

        // Insert sub-menus
        foreach ($menu['sub_menus'] as $subMenu) {
            // Menyisipkan data sub-menu ke tabel user_sub_menus
            $userSubMenuData = [
                'user_id' => $id,
                'sub_menu_id' => $subMenu['sub_menu_id'],
                'status' => $subMenu['sub_menu_status'],
            ];

            $userSubMenu = DB::table('user_sub_menus')->where('user_id', $id)
                ->where('sub_menu_id', $subMenu['sub_menu_id'])->first();

            if ($userSubMenu) {
                // Update jika sudah ada
                DB::table('user_sub_menus')
                    ->where('user_id', $id)
                    ->where('sub_menu_id', $subMenu['sub_menu_id'])
                    ->update($userSubMenuData);
            } else {
                // Insert jika belum ada
                DB::table('user_sub_menus')->insert($userSubMenuData);
            }
        }
    }

    return response()->json([
        'message' => 'Menu dan sub-menu berhasil diatur.',
        'code' => 1
    ]);
}
        
//COUNTOTAL team
private function UpdateUseCRM($userId, $action = 'add')
{
    $owner = User::find($userId);
    if (!$owner) {
        return false;
    }

    $plan = PaketPackage::find($owner->paket_id);
    if (!$plan) {
        return false;
    }

    $planDetail = is_string($plan->detail) ? json_decode($plan->detail, true) : $plan->detail;
    $planUsage = is_string($owner->usage_paket) ? json_decode($owner->usage_paket, true) : $owner->usage_paket;

    if (!is_array($planDetail) || !is_array($planUsage)) {
        return false;
    }

    // Ambil limit 'Team CRM'
    $planLimitRaw = collect($planDetail)->firstWhere('fitur', 'Team CRM')['value'] ?? '0';
    if (strtolower($planLimitRaw) === 'unlimited') {
        return true;
    }
    $planLimit = (int) $planLimitRaw;

    // Ambil semua project yang dimiliki oleh owner
    $projectIds = DB::table('user_projects')
        ->where('user_id', $userId)
        ->pluck('project_id');

    if ($projectIds->isEmpty()) {
        $currentCount = 0;
    } else {
        // Hitung jumlah users lain (selain owner) yang ikut di project yang sama dan role_id = 4
        $currentCount = DB::table('user_projects')
            ->join('users', 'users.id', '=', 'user_projects.user_id')
            ->whereIn('user_projects.project_id', $projectIds)
            ->where('users.id', '!=', $userId)
            ->where('users.role_id', 4)
            ->distinct()
            ->count('users.id');
    }

    $newCount = $currentCount + ($action === 'add' ? 1 : -1);
    $newCount = max($newCount, 0);

    // Validasi limit
    if ($newCount > $planLimit) {
        return false;
    }

    // Update usage
    $updated = false;
    foreach ($planUsage as &$usage) {
        if ($usage['fitur'] === 'Team CRM') {
            if ((int) $usage['usage'] !== $newCount) {
                $usage['usage'] = $newCount;
                $updated = true;
            }
            break;
        }
    }

    if ($updated) {
        DB::table('users')
            ->where('id', $userId)
            ->update(['usage_paket' => json_encode($planUsage)]);
    }

    return true;
}



}
