<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

          return [
            'project_id' => $this->project_id,
            'project_key' => bin2hex($this->project_key),
            'user_id' => $this->user_id,
            'project_name' => $this->project_name,
            'endpoint' => $this->endpoint,
        ];
    }
    
}
