<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMaterialsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('materials', function (Blueprint $table) {
            $table->integer('id')->primary();
            $table->string('token');
            $table->string('code');
            $table->integer('type');
            $table->integer('categories');
            $table->text('search');
            $table->string('name');
            $table->string('units');
            $table->integer('stock');
            $table->integer('limitstock');
            $table->integer('buffer');
            $table->integer('safety');
            $table->text('description');
            $table->integer('suplier');
            $table->bigInteger('user_id');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('materials');
    }
}
