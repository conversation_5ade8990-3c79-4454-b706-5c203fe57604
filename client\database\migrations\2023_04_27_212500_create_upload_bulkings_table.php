<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUploadBulkingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('upload_bulkings', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->integer('type');
            $table->string('token');
            $table->string('name');
            $table->string('extention');
            $table->integer('rows');
            $table->string('path');
            $table->bigInteger('user_id');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('upload_bulkings');
    }
}
