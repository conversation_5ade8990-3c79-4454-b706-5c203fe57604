<?php
namespace App\Http\Controllers\home\inventory\po;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\po_requests as tblPoRequests;
use App\po_items as tblPoItems;
use App\materials as tblMaterials;

class manage extends Controller
{
    //
    public function add(Request $request)
    {

        //CHECK
        $user_id = trim($request->user_id);


        $check = tblPoRequests::where([
            'user_id'       =>  $user_id,
            'progress'      =>  0,
            'status'        =>  1
        ])->first();


        if( $check == null )
        {
           //add
           $newadd = new \App\Http\Controllers\models\materials;
           $newadd = $newadd->rpobb($request);

           $rpoid = $newadd['id'];
           //
           $status = "new";
        }
        else
        {
            $rpoid = $check->id;

            $status = "edit";
        }


        $res = $this->additempobb([
            "id"     =>  $rpoid,
            "item"   =>  $request->item
        ]);

        //
        $data = [
            "message"       =>  ""
        ];

        return response()->json($data, 200);
    }

    //add item request po
    public function additempobb($request)
    {
        $item = explode(",", $request['item']);

        foreach($item as $row => $value)
        {
            $product = tblMaterials::where([
                "id"            =>  $value
            ])
            ->first();

            $dataadd = [
                'rpoid'                 =>  $request['id'],
                'product_id'            =>  $product->id,
                'suplier_id'            =>  $product->suplier,
            ];

            $addnew = new \App\Http\Controllers\models\materials;
            $addnew = $addnew->itemsrpobb($dataadd);
        }

    }

    public function delete(Request $request)
    {

        $id = trim($request->id);
        $rpoid = trim($request->rpoid);

        tblPoItems::where([
            'id'        =>  $id
        ])
        ->update([
            'status'        =>  0
        ]);

        $countitems = tblPoItems::where([
            'req_poid'      =>  $rpoid,
            'status'        =>  1
        ])->count();

        if( $countitems == 0)
        {
            tblPoRequests::where([
                'id'        =>  $rpoid
            ])
            ->update([
                'status'    =>  0
            ]);
        }


        $data = [
            'message'       =>  ''
        ];

        return response()->json($data, 200);
    }

    //update item
    public function updateitem(Request $request)
    {
        
        $id = trim($request->id);
        $field = trim($request->field);
        $value = trim($request->value);

        
        tblPoItems::where([
            'id'        =>  $id
        ])
        ->update([
            $field      =>  $value
        ]);

        
        $data = [
            "message"       =>  ""
        ];

        return response()->json($data, 200);
    }


    //update progress
    public function updatepo(Request $request)
    {

        $id = trim($request->rpoid);
        $type = trim($request->progress);

        tblPoRequests::where([
            'id'        =>  $id
        ])
        ->update([
            'progress'      =>  $type
        ]);

        //
        $data = [
            'message'       =>  ''
        ];


        return response()->json($data,200);
    }
}