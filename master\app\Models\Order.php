<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    protected $table = 'orders';

protected $fillable = [
    'order_id',
    'user_id',
    'invoice',
    'paket_id',
    'IsUpgrade',
    'paket_type',
    'name',
    'phone',
    'email',
    'nominal',
    'status',
    'link_url',
    'link_pay',
    'site',               
    'link_id',            
    'date',               
    'type',             
    'redirect',          
    'callback',         
    'data',              
    'respon_confirm',    
    'tanggal_approve',    
    'tanggal_expired',
    'transaction_status', 
    'external_id',      
    'voucher',
    'status_paket'
];

}
