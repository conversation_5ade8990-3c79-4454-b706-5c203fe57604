<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use HasFactory;
     protected $table = 'projects'; // Tentukan nama tabel yang sesuai
     protected $fillable = [
        'project_id',
        'project_key',
        'project_name',
        'project_type',
        'endpoint'
    ];
    
    // Relasi banyak-ke-banyak dengan User melalui tabel user_projects
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_projects', 'project_id', 'user_id')
                    ->withTimestamps();
    }
    
    
}
