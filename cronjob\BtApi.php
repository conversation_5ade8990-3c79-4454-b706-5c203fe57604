<?php

class BtApi {
    private $BT_KEY = "zJg2lN20GC6lnNBDntHNqMEVSKIlrFB9";
    private $BT_PANEL = "http://***************:32148"; // Ini IP dari server client
    private $dbuser = 'root';
    private $dbpass = '891e956c1bec2f40'; // Update dengan password yang benar

    public function __construct($bt_panel = null, $bt_key = null, $dbuser = null, $dbpass = null) {
        if ($bt_panel) $this->BT_PANEL = $bt_panel;
        if ($bt_key) $this->BT_KEY = $bt_key;
        if ($dbuser) $this->dbuser = $dbuser;
        if ($dbpass) $this->dbpass = $dbpass;
    }

        function calculateLoadStatus($data)
    {
        $loadAvg = $data['load']['one']; // Extract one-minute load average
        $systemCapacity = $data['load']['limit']; // Extract system capacity
        $threshold = $data['load']['safe']; // Extract safe threshold
        if ($loadAvg < $systemCapacity * $threshold) {
            return "Normal";
        } elseif ($loadAvg >= $systemCapacity) {
            return "Overloaded";
        } else {
            return "Warning";
        }
    }
    // Function to calculate CPU usage percentage
    function calculateCpuUsage($data)
    {
        $cpuTimes = $data['cpu_times'];
        // $idle = $cpuTimes['idle'];
        // $total = array_sum($cpuTimes);
        // $usage = 100 * (1 - ($idle / $total));
        // return $usage;

        $cpuUsage = $cpuTimes['user'] +
            $cpuTimes['system'] +
            $cpuTimes['iowait'] +
            $cpuTimes['steal'];

        return $cpuUsage;
    }

    // Function to calculate memory usage percentage
    function calculateMemoryUsage($data)
    {
        $mem = $data['mem'];
        $memTotal = $mem['memTotal'];
        $memUsed = $memTotal - $mem['memFree'] - $mem['memBuffers'] - $mem['memCached'];
        $usage = 100 * ($memUsed / $memTotal);
        return $usage;
    }

    public function get_performance()
    {
        // $url = $this->BT_PANEL.'/config?action=get_config';
        // $p_data = $this->GetKeyData();
        // $config = $this->HttpPostCookie($url,$p_data);

        $url = $this->BT_PANEL . '/system?action=GetNetWork';
        $p_data = $this->GetKeyData();
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        $ret["load_status"] = $this->calculateLoadStatus($data);
        $ret["cpu_usage"] = $this->calculateCpuUsage($data);
        $ret["mem_usage"] = $this->calculateMemoryUsage($data);
        $ret["raw"] = json_decode($result, true);
        //$ret["config"] = json_decode($config,true);
        return $ret;
    }
    public function add_site($domain)
    {
        $url = $this->BT_PANEL . '/site?action=AddSite';
        $p_data = $this->GetKeyData();
        $p_data['webname'] = '{"domain":"' . $domain . '","domainlist":[],"count":0}';
        $p_data['port'] = 80;
        $p_data['type'] = 'PHP';
        $p_data['ps'] = str_replace('.', '_', $domain);
        $p_data['path'] = '/www/wwwroot/' . $domain;
        $p_data['type_id'] = 0;
        $p_data['version'] = 74; // Contoh: PHP 7.4

        $p_data['ftp'] = false;
        $p_data['sql'] = false;
        $p_data['codeing'] = 'utf8';
        $p_data['set_ssl'] = 1;
        $p_data['force_ssl'] = 1;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }

    public function get_site($domain)
    {
        $url = $this->BT_PANEL . '/data?action=getData';
        $p_data = $this->GetKeyData();
        $p_data['table'] = 'sites';
        $p_data['limit'] = '';
        $p_data['search'] = '';
        $p_data['p'] = 1;
        $p_data['type'] = -1;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }

    public function get_db($limit)
    {
        $url = $this->BT_PANEL . '/data?action=getData';
        $p_data = $this->GetKeyData();
        $p_data['table'] = 'databases';
        $p_data['limit'] = $limit;
        $p_data['search'] = '';
        $p_data['p'] = 1;
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }

    public function add_db($dbname, $dbuser, $dbpass)
    {
        $url = $this->BT_PANEL . '/database?action=AddDatabase';
        $p_data = $this->GetKeyData();
        $p_data['name'] = $dbname;
        $p_data['db_user'] = $dbuser;
        $p_data['password'] = $dbpass;
        $p_data['dataAccess'] = '127.0.0.1';
        $p_data['address'] = '127.0.0.1';
        $p_data['sid'] = 0;
        $p_data['ps'] = $dbname;
        $p_data['active'] = false;
        $p_data['ssl'] = '';
        $p_data['codeing'] = 'utf8';
        $p_data['dtype'] = 'MySQL';
        $result = $this->HttpPostCookie($url, $p_data);
        $data = json_decode($result, true);
        return $data;
    }

    private function GetKeyData()
    {
        $now_time = time();
        $p_data = array(
            'request_token'    =>    md5($now_time . '' . md5($this->BT_KEY)),
            'request_time'    =>    $now_time
        );
        return $p_data;
    }

    private function HttpPostCookie($url, $data, $timeout = 60)
    {
        $cookie_file = './' . md5($this->BT_PANEL) . '.cookie';
        if (!file_exists($cookie_file)) {
            // $fp = fopen($cookie_file,'w+');
            // fclose($fp);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_file);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_file);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
}
