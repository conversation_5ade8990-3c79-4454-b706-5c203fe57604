<?php
namespace App\Http\Controllers\tdparty\csv;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\Http\Controllers\config\errors as ConfigErrors;
use Exception;

class index extends Controller
{
    //
    public function customers(Request $request){

        $Config = new Config;
        $ConfigErrors = new ConfigErrors;

        try{

            
            // read csv
            $read = $Config->readCSV([
                'file'      =>  'testing2.csv'
            ]);

            //show items data
            if($read){

                
                $page = trim($request->page);
                $page_size = 1;
                $offset = ($page - 1) * $page_size;
                $vdata = $read['items'];
                $vdata = array_slice($vdata, $offset, $page_size);

                //
                foreach($vdata as $row){
                    $items[] = [
                        'no'        =>  (int)$row[0],
                        'name'      =>  $row[1],
                        'phone'     =>  (int)$row[2]
                    ];
                }
    
                //
                $data = [
                    'header'        =>  $read['header'],
                    'items'         =>  $items,
                    'total'         =>  $read['count']
                ];
    
                return response()->json($data,200);
            }

            // if row not found
            $data = $ConfigErrors->main([
                'code'          =>  404
            ]);

            return response()->json($data,500);
        }
        catch(Exception $err){
            
            $data = $ConfigErrors->main([
                'message'       =>  $err->getMessage(),
                'code'          =>  500
            ]);

            return response()->json($data,500);
        }
    }


}