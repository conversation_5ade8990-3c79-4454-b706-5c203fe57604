{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^7.3|^8.0", "guzzlehttp/guzzle": "^7.8", "illuminate/session": "^8.83", "laravel/lumen-framework": "^8.0", "tymon/jwt-auth": "^1.0@dev"}, "require-dev": {"fakerphp/faker": "^1.9.1", "league/flysystem": "^1.1", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}