<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePoItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('po_items', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->integer('product_id');
            $table->integer('req_poid');
            $table->integer('po_id');
            $table->integer('suplier_id');
            $table->integer('categori_id');
            $table->integer('price');
            $table->integer('quantity');
            $table->integer('total');
            $table->integer('quantity_fix');
            $table->integer('total_fix');
            $table->integer('progress');
            $table->bigInteger('user_progress');
            $table->string('date_progress');
            $table->integer('inventory_status');
            $table->string('inventory_date');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('po_items');
    }
}
