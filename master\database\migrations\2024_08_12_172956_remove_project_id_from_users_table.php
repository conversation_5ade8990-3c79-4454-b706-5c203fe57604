<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
   Schema::table('users', function (Blueprint $table) {
            // Drop the foreign key constraint if it exists
            $table->dropForeign(['project_id']);
            
            // Drop the project_id column
            $table->dropColumn('project_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
  Schema::table('users', function (Blueprint $table) {
            // Add the project_id column back
            $table->unsignedBigInteger('project_id')->nullable();

            // Add the foreign key constraint back
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
        });
    }
};
