<?php
namespace App\Http\Controllers\testing;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\obe_upload_excels as tblObeUploadExcels;
use Excel;

class index extends Controller
{
    //VIEW
    public function viewfunction()
    {
        $Config = new Config;

        $num = 1200;
        $length = 3;

        $numberFZero = $Config->numberFZero([$num,$length]);

        return response()->json($numberFZero,200);
    }

    //TIME AGO
    public function timeago()
    {
        // $ptime = '2022-03-08 16:10:19';
        $ptime = '2022-03-08 11:21:54';

        $days = date('w', strtotime($ptime));
        $thisday = date('w', time());


        $Config = new Config;

        $gettime = strtotime($ptime);

        $estimate_time = time() - $gettime;
        if( $estimate_time < 1 )
        {
            return '1d lalu';
        }

        $condition = [ 
            12 * 30 * 24 * 60 * 60  =>  'thn',
            30 * 24 * 60 * 60       =>  'bln',
            24 * 60 * 60            =>  'hari',
            60 * 60                 =>  'j',
            60                      =>  'm',
            1                       =>  'd'
        ];

        foreach( $condition as $secs => $str )
        {
            $d = $estimate_time / $secs;

            $r = round($d);

            if( $d >= 1 )
            {
                    // $r = round( $d );
                // return ' ' . $r . $str;
                
                if( $str == 'm' || $str == 'd')
                {   
                    return $r . $str . ' lalu';
                }
                elseif( $str == 'j' )
                {
                    if( $r < 4 )
                    {
                        return $r . $str . ' lalu';
                    }
                    else
                    {

                        if( $days < $thisday)
                        {
                            return 'Kemarin, ' . date('H.i', $gettime);
                        }
                        else
                        {
                            return date('H.i', $gettime);
                        }
                    }
                }
                elseif( $str == 'hari' && $r < 7)
                {
                    if( $r > 1 )
                    {
                        return $Config->namahari($ptime) . ', ' . date('H:i', $gettime);
                    }
                    else
                    {
                        return 'Kemarin, ' . date('H.i', $gettime);
                    }
                    
                }
                else{

                    return date('d/m/Y', $gettime);

                }

            }
        }
    }

    //READ EXCEL
    public function readexcel(Request $request)
    {
        $id = trim($request->id);

        $getdata = tblObeUploadExcels::where([
            'id'        =>  $id
        ])->first();

        $path = $getdata->path;

        $data = Excel::load($path .'.xlsx', function($reader) {})->get();

        $data = [
            'message'       =>  '',
            'path'          =>  $path
        ];

        return response()->json($data, 200);
    }


    //
    public function getContent(Request $request){

        $Config = new Config;

        $dataFilter = $Config->roleFilterString($request);

        $request = $dataFilter;
    
        $data = [
            'data'      =>  $request,
        ];

        return response()->json($data, 200);
    }
}