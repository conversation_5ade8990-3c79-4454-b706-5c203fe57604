<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class user_configs extends Model
{
    protected $table = 'user_configs';
    protected $fillable = ['user_id', 'config_id', 'value'];
    public $timestamps = false;
    public function user()
    {
        return $this->belongsTo('App\User');
    }
    public function config()
    {
        return $this->belongsTo('App\Config');
    }
}
