<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON>\Lumen\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        AuthorizationException::class,
        HttpException::class,
        ModelNotFoundException::class,
        ValidationException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Throwable  $exception
     * @return void
     *
     * @throws \Exception
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {

        //hendle if method not exist and return 500 error
        if ($exception instanceof MethodNotAllowedHttpException) {
            // …
            $data = [
                'message'           => 'Permintaan ' . $request->method(). ' pada URL /' . $request->path() . ' tidak ditemukan'
            ];

            return response()->json($data, 500);
        }

        if ($exception instanceof NotFoundHttpException) {
            // …
            $data = [
                'message'           => 'Permintaan ' . $request->method(). ' pada URL /' . $request->path() . ' tidak ditemukan'
            ];

            return response()->json($data, 500);
        }
        
        return parent::render($request, $exception);
    }
}
