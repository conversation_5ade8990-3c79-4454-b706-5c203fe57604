<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePoRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('po_requests', function (Blueprint $table) {
            $table->integer('id')->primary();
            $table->integer('type');
            $table->string('token');
            $table->string('code');
            $table->bigInteger('user_id');
            $table->integer('progress');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('po_requests');
    }
}
