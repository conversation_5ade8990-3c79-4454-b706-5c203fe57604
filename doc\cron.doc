# CRON JOBS DOCUMENTATION - RIPIT PROJECT

## Table of Contents
1. Overview
2. Current Cron Job Setup
3. Files Where Cron Jobs Are Defined
4. Recommended Cron Jobs
5. Implementation Guide
6. System Cron Configuration
7. Monitoring and Maintenance

## 1. OVERVIEW

This document provides comprehensive documentation for cron jobs in the Ripit project. The project consists of two main applications:
- **Client**: Lumen application
- **Master**: Laravel application

Both applications require proper cron job configuration for optimal performance and maintenance automation.

## 2. CURRENT CRON JOB SETUP

### Client Directory (Lumen)
- **Location**: `client/app/Console/Kernel.php`
- **Current Status**: Minimal setup with empty schedule method
- **Available Commands**: 
  - `ClearViewCache` - Clears compiled view files
  - `dbcreate` - Creates MySQL database
  - `ControllerMakeCommand` and `ModelMakeCommand` - Development utilities

### Master Directory (Laravel)
- **Location**: `master/routes/console.php`
- **Current Status**: Only default `inspire` command scheduled hourly
- **No custom Console directory or commands**

## 3. FILES WHERE CRON JOBS ARE DEFINED

### 3.1 Client Application
**File**: `client/app/Console/Kernel.php`

```php
protected function schedule(Schedule $schedule)
{
    // Cron jobs should be defined here
}
```

### 3.2 Master Application
**File**: `master/routes/console.php`

```php
// Current content
Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();
```

## 4. RECOMMENDED CRON JOBS

### 4.1 Essential Production Cron Jobs

#### Queue Processing (Critical)
- **Frequency**: Every minute
- **Command**: `queue:work --stop-when-empty --max-jobs=100`
- **Purpose**: Process background jobs and queued tasks
- **Importance**: High - Without this, background jobs won't execute

#### Cache Management
- **View Cache Cleanup**: Weekly (Sundays at 2:00 AM)
  - **Command**: `view:clear`
  - **Purpose**: Prevent cache bloat and disk space issues

- **Application Cache Cleanup**: Daily
  - **Command**: `cache:clear`
  - **Purpose**: Refresh application cache periodically

#### Queue Maintenance
- **Failed Job Retry**: Hourly
  - **Command**: `queue:retry all`
  - **Purpose**: Automatically retry failed jobs

- **Failed Job Cleanup**: Daily
  - **Command**: `queue:flush 7`
  - **Purpose**: Remove failed jobs older than 7 days

#### Database Maintenance
- **Database Optimization**: Monthly
  - **Command**: `db:optimize` (custom command)
  - **Purpose**: Optimize database tables for better performance

- **Session Cleanup**: Daily
  - **Command**: `sessions:clean` (custom command)
  - **Purpose**: Remove expired session data

### 4.2 Monitoring and Health Check Cron Jobs

#### System Health Check
- **Frequency**: Every 5 minutes
- **Command**: `health:check` (custom command)
- **Purpose**: Monitor system health and alert on issues

#### Log Cleanup
- **Frequency**: Weekly
- **Command**: `logs:cleanup` (custom command)
- **Purpose**: Remove old log files to prevent disk space issues

#### Database Backup
- **Frequency**: Daily
- **Command**: `backup:database` (custom command)
- **Purpose**: Create daily database backups

## 5. IMPLEMENTATION GUIDE

### 5.1 Adding Cron Jobs to Client Application

**File**: `client/app/Console/Kernel.php`

```php
protected function schedule(Schedule $schedule)
{
    // Process queued jobs every minute (essential for background processing)
    $schedule->command('queue:work --stop-when-empty --max-jobs=100')
             ->everyMinute()
             ->withoutOverlapping();
    
    // Clear view cache weekly to prevent cache bloat
    $schedule->command('view:clear')
             ->weekly()
             ->sundays()
             ->at('2:00');
    
    // Retry failed jobs hourly
    $schedule->command('queue:retry all')
             ->hourly()
             ->withoutOverlapping();
    
    // Clean up failed jobs daily (older than 7 days)
    $schedule->command('queue:flush 7')
             ->daily()
             ->withoutOverlapping();
    
    // Database optimization monthly
    $schedule->command('db:optimize')
             ->monthly()
             ->withoutOverlapping();
}
```

### 5.2 Adding Cron Jobs to Master Application

**File**: `master/routes/console.php`

```php
// Keep the existing inspire command
Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Add these new commands:

// Process queued jobs every minute
Artisan::command('queue:work', function () {
    $this->call('queue:work', [
        '--stop-when-empty' => true,
        '--max-jobs' => 100
    ]);
})->purpose('Process queued jobs');

// Database backup daily
Artisan::command('backup:database', function () {
    // Implementation for database backup
    $this->info('Database backup completed');
})->purpose('Backup database daily')->daily();

// Clean old logs weekly
Artisan::command('logs:cleanup', function () {
    // Implementation for log cleanup
    $this->info('Old logs cleaned up');
})->purpose('Clean old log files')->weekly();

// System health check
Artisan::command('health:check', function () {
    // Implementation for health check
    $this->info('System health check completed');
})->purpose('Check system health')->everyFiveMinutes();

// Cache cleanup daily
Artisan::command('cache:cleanup', function () {
    $this->call('cache:clear');
    $this->info('Cache cleared successfully');
})->purpose('Clear application cache')->daily();
```

### 5.3 Required Custom Commands

#### Database Optimization Command
**File**: `client/app/Console/Commands/DbOptimize.php`

```php
<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DbOptimize extends Command
{
    protected $signature = 'db:optimize';
    protected $description = 'Optimize database tables';

    public function handle()
    {
        $tables = DB::select('SHOW TABLES');
        foreach ($tables as $table) {
            $tableName = reset($table);
            DB::statement("OPTIMIZE TABLE {$tableName}");
        }
        $this->info('Database optimization completed');
    }
}
```

#### Session Cleanup Command
**File**: `client/app/Console/Commands/CleanSessions.php`

```php
<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanSessions extends Command
{
    protected $signature = 'sessions:clean';
    protected $description = 'Clean expired sessions';

    public function handle()
    {
        $expired = DB::table('sessions')
                    ->where('last_activity', '<', time() - (7 * 24 * 60 * 60))
                    ->delete();
        $this->info("Cleaned {$expired} expired sessions");
    }
}
```

## 6. SYSTEM CRON CONFIGURATION

### 6.1 System Cron Entry
Add the following entries to the system crontab:

```bash
# Client application cron jobs
* * * * * cd /path/to/ripit/client && php artisan schedule:run >> /dev/null 2>&1

# Master application cron jobs
* * * * * cd /path/to/ripit/master && php artisan schedule:run >> /dev/null 2>&1
```

### 6.2 Cron Job Monitoring
- **Log Location**: Check application logs in `storage/logs/`
- **Monitoring**: Set up alerts for cron job failures
- **Performance**: Monitor execution time and resource usage

## 7. MONITORING AND MAINTENANCE

### 7.1 Key Metrics to Monitor
1. **Queue Processing Time**: Ensure jobs are processed promptly
2. **Failed Job Count**: Monitor for recurring failures
3. **Disk Space Usage**: Check cache and log file sizes
4. **Database Performance**: Monitor query execution times
5. **System Health**: Check application responsiveness

### 7.2 Troubleshooting Common Issues

#### Cron Jobs Not Running
1. Verify system cron is configured correctly
2. Check file permissions for artisan commands
3. Ensure PHP CLI is properly configured
4. Check application logs for errors

#### Queue Processing Issues
1. Check queue worker status
2. Monitor failed jobs table
3. Verify database connection
4. Check memory limits

#### Performance Issues
1. Review database optimization schedules
2. Check cache cleanup frequency
3. Monitor log file sizes
4. Review session cleanup effectiveness

### 7.3 Best Practices
1. **Use `withoutOverlapping()`** to prevent multiple instances
2. **Set appropriate time limits** for long-running jobs
3. **Monitor resource usage** to prevent server overload
4. **Implement proper error handling** in custom commands
5. **Regular review** of cron job effectiveness and necessity

## 8. SECURITY CONSIDERATIONS

1. **File Permissions**: Ensure cron files have appropriate permissions
2. **Environment Variables**: Store sensitive data in environment files
3. **Command Validation**: Validate all inputs in custom commands
4. **Logging**: Log cron job activities for audit purposes
5. **Access Control**: Restrict access to cron job configuration files

## 9. BACKUP AND RECOVERY

1. **Regular Backups**: Schedule database and file backups
2. **Test Restores**: Regularly test backup restoration procedures
3. **Retention Policy**: Implement backup retention policies
4. **Offsite Storage**: Store backups in secure offsite locations

---

**Last Updated**: August 15, 2025
**Author**: System Administrator
**Version**: 1.0
