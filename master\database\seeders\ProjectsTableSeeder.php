<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;



class ProjectsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         DB::table('projects')->insert([
            'project_key' => random_bytes(16), // Generate a random binary key
            'project_name' => 'Toko Kelontong',
            'endpoint' => '1client.ripit.id',
        ]);
        
    }
}
