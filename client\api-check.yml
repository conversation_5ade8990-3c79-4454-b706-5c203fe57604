product/list/widgetmodal
    product/list
    product/table
    product/distributor-list
    product/view-list-distributor
    product/create
    product/detail

=======CUSTOMERS========
customers/manage/add
customers/manage/changeprogress
customers/manage/changetaging
customers/manage/addnote
customers/detail
customers/vs-edit
customers/s-edit

    customers list on widget :
    customers/list/widget
    customers/address/list

    // Customers ADDRESS ====== >
    customers/address/single
    customers/table

    //customers address
customers/address/create
export/customers
testing/logcustomer-add
customers

   //api-customers-error//
   /customers/manage/add
   /customers/list/widget
   /customers/detail
   /customers/vs-edit
   /customers/table
   /customers/crm/table

   //api-product-error//
   product/table
  product/list/widgetmodal
  product/view-list-distributor
  product/detail


/api.ripit.id/api/product/table?src=&type=2&compid=100000001&sort=asc&status=-1&pg=1&_=1709380505588

   xxxcb8655ee82e61e37c21071e4f13d3c70

   om,,api yang diberikan ini nanti semua di pakai parameter nya / url nya soalnya ini saya list untu api /product sendiri ada banyak,,misal crud udah ada tapi ada lagi untuk /listwidget /listview dan lain sebagainya