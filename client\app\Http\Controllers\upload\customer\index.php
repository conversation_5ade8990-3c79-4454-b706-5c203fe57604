<?php
namespace App\Http\Controllers\upload\customer;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Exception;
use App\Http\Controllers\config\index as Config;
use App\Http\Controllers\config\errors as ConfigErrors;
use App\upload_bulkings as tblUploadBulkings;
use App\customer_uploads as tblCustomerUploads;
use DB;

class index extends Controller
{
    //SAVE
    public function create(Request $request){

        $Config = new Config;
        $ConfigErrors = new ConfigErrors;


        try{

            // REQUEST 
            $user_id = trim($request->user_id);
            $file = $request->file('file');

            
            // CONFIG ATTRIBUTE FILE
            $ConfigFile = $Config->configFile($file);
            $file_ext = $configFile['extension'];
            $file_name = $configFile['name'];

            //UPLOAD
            $newid = $Config->createnewidnew([
                'value'         =>  tblUploadBulkings::count(),
                'length'        =>  15
            ]);
    
            $token = md5($newid);
            $file = $request->file('file');
            $namefile = $token . '.' . $file_ext;
            
            $base = $Config->path_storage();
            $path = '/upload/bulking/customer/';

            $pathAll = $base . '/app' . $path . $namefile;
    
            
            //
            $addnew                 =   new tblUploadBulkings;
            $addnew->id             =   $newid;
            $addnew->type           =   1; //customer
            $addnew->token          =   $token;
            $addnew->name           =   $file_name;
            $addnew->extention      =   $file_ext;
            $addnew->rows           =   0;   
            $addnew->path           =   $pathAll;
            $addnew->user_id        =   0; //trim($request->user_id);
            $addnew->status         =   1;
            $addnew->save();
    
    
            //data upload
            $dataupload = [
                'name'          =>  $namefile,
                'file'          =>  $file,
                'path'          =>  $path,
                "URL"           =>  $Config->apps()["URL"]["STORAGE"] . "/s3/upload/file"
            ];
    
            $upload = new \App\Http\Controllers\tdparty\s3\herbindo;
            $upload = $upload->transfer($dataupload);
            

            //READ THIS FILE
            $read = $Config->readCSV([
                'file'      =>  $pathAll
            ]);

            //IF ROWS FOUND
            if($read){

                $no = 0;
                $countheader = count($read['header']) - 1;
                $headers = $read['header'];
                $items = $read['items'];
            
                foreach( $items as &$item )
                {
                    foreach( $headers as $key => $rowx )
                    {

                        //change type data string to integer
                        // if($rowx=='id' || $rowx == 'quantity' || $rowx == 'total'){
                        //     $item[$key] = (int)$item[$key];
                        // }

                        $item[ strtolower($rowx) ] = $item[$key];
                        unset($item[$key]);
                    }
                }

                
            
                //ADD TABLE CUSTOMER UPLOADS
                $this->addCustomerBulk([
                    'item'          =>  $items,
                    'user_id'       =>  $user_id,
                    'upload_id'     =>  $newid
                ]);

                //UPDATE TOTAL ROWS
            
                $data = [
                    // 'header'        =>  $read['header'],
                    'items'          =>  $items,
                    // 'list'          =>  $list
                    // 'count'         =>  $read['count'],
                    // 'count_header'  =>  $countheader
                ];
    
                return response()->json($data,200);
            }

            // IF ROWS NOT FOUND
            $data = $ConfigErrors->main([
                'code'          =>  404
            ]);

            return response()->json($data,404);
        }
        catch(Exception $error){
            $data = $ConfigErrors->main([
                'message'       =>  $error->getMessage(),
                'code'          =>  500
            ]);

            return response()->json($data,500);
        }
        

    }


    private function addCustomerBulk($request){
        $Config = new Config;


        $items = $request['item'];

        // foreach($items as $key => $row){
        //     $list[] = $items[$key]['no'];
        // }

        foreach($items as $key => $row){

            $newid = $Config->createnewidnew([
                'value'     =>  DB::table('customer_uploads')->count(),
                'length'        =>  15
            ]);
            
    
            //INSERT TO TABLE CUSTOMER UPLOAD
            $addCustomer = DB::table('customer_uploads')
            ->insert([
                'id'        =>  $newid,
                'upload_id' =>  $request['upload_id'],
                'name'      =>  $items[$key]['name'],
                'phone'     =>  $items[$key]['phone'],
                'email'     =>  $items[$key]['email'],
                'address'   =>  $items[$key]['address'],
                'user_id'       =>  $request['user_id'],
                'progress'      =>  0,
                'progress_date' =>  '',
                'progress_user_id'  =>  0,
                'status'        =>  1,
            ]);

        }
        //
    }

    //SHOW
    public function show(Request $request){
        $Config = new Config;

        try{
            $getdata = tblUploadBulkings::where([
                'token' =>  $request->q,
                'status'    =>  1
            ]);

            //IF NOT FOUND
            if($getdata->count() == 0){
                $data = [
                    'message'       =>  $Config->messageError(404)
                ];

                return response()->json($data, 404);
            }


            

        }
        catch(Exception $error){        

        }
    }
}