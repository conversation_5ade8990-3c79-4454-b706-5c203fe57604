<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
public function up(): void
{
    Schema::table('orders', function (Blueprint $table) {
        $table->string('site')->nullable(); // Nama situs
        $table->integer('link_id')->nullable(); // ID link
        $table->datetime('date')->nullable(); // Tanggal terkait order/transaksi
        $table->string('type')->nullable(); // Tipe transaksi/pembayaran
        $table->string('redirect')->nullable(); // URL redirect setelah transaksi
        $table->string('callback')->nullable(); // URL callback
        $table->string('data')->nullable(); // Data tambahan dalam format JSON
        $table->string('respon_confirm')->nullable(); // Respons konfirmasi
        $table->datetime('tanggal_approve')->nullable(); // Tanggal approve
        $table->string('transaction_status')->nullable(); // Status transaksi
        $table->string('external_id')->nullable(); // ID eksternal
        $table->string('voucher')->nullable(); // Kode voucher
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
};
