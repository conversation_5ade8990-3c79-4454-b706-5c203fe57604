<?php
namespace App\Http\Controllers\home\inventory\orderpo;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\po_requests as tblPoRequests;
use App\po_items as tblPoItems;
use App\Http\Controllers\dashboard\report as Report;

class table extends Controller
{
    //
    public function main(Request $request)
    {
        $Config = new Config;
        $Report = new Report;

        $src = str_replace(";", "", trim($request->search));
        $src = "%" . $src . "%";
        $status = trim($request->status);
        $date = trim($request->date);
        $paging = trim($request->paging);
        $user_id = trim($request->user_id);
        
        //
        $getdata = tblPoRequests::from("po_requests as p")
        ->select(
            "p.id", "p.code", "p.created_at as date", "p.progress",
            "u.name as username"
        )
        ->leftJoin("users as u", function($join)
        {
            $join->on('u.id', '=', 'p.user_id');
        })
        ->where([
            // ["p.progress", ">", 0],
            ['p.code', 'like', $src],
            ["p.status", '=', 1]
        ]);
        if( $status != '' )
        {
            $getdata = $getdata->where([
                ["p.progress", "=", $status]
            ]);
        }
        if( $date != '')
        {
            // $date = explode("_", $date);
            $date = $Report->changeDate($request->date);
            $getdata = $getdata->whereBetween('p.created_at', [$date['startDate'], $date['endDate']]);
        }

        $count = $getdata->count();

        if( $count === 0)
        {
            $data = [
                'message'       =>  'Data Tidak ditemukan',
                'response'      =>  ''
            ];

            return response()->json($data, 404);
        }

        //
        $gettable = $getdata->orderBy('p.id', 'desc')
        ->take($Config->table(['paging'=>$paging])['paging_item'])
        ->skip($Config->table(['paging'=>$paging])['paging_limit'])
        ->get();

        foreach($gettable as $row)
        {
            $getitem = tblPoItems::from('po_items as pi')
            ->select(
                'pi.suplier_id as suplier_id',
                's.name as suplier'
            )
            ->leftJoin('supliers as s', function($join)
            {
                $join->on('s.id', '=', 'pi.suplier_id');
            })
            ->where([
                'pi.req_poid'         =>  $row->id,
                'pi.status'     =>  1
            ])
            ->groupBy('pi.suplier_id')
            ->get();

            $item = [];
            foreach($getitem as $rowx)
            {
                $getlist = tblPoItems::from('po_items as pi')
                ->select(
                    'pi.id', 'pi.price', 'pi.quantity', 'pi.total',
                    'm.name',
                    'mu.name as unit'
                )
                ->leftJoin('materials as m', function($join)
                {
                    $join->on('m.id', '=', 'pi.product_id');
                })
                ->leftJoin('material_units as mu', function($join)
                {
                    $join->on('mu.id', '=', 'm.units');
                })
                ->where([
                    'pi.req_poid'         =>  $row->id,
                    'pi.suplier_id'       =>  $rowx->suplier_id,
                    'pi.status'           =>  1
                ])
                ->get();

                $item[] =[
                    'id'        =>  $rowx->suplier_id,
                    'name'      =>  $rowx->suplier,
                    'item'      =>  $getlist
                ];
            }

            $list[] = [
                'id'                =>  $row->id,
                'code'              =>  $row->code,
                'user'              =>  $row->username,
                'progress'          =>  $row->progress,
                'date'              =>  $Config->timeago($row->date),
                'items'              =>  $item
            ];
        }

        //
        $data = [
            'message'       =>  '',
            'response'      =>  [
                'list'          =>  $list,
                'paging'        =>  $paging,
                'total'         =>  $count,
                'countpage'     =>  ceil($count / $Config->table(['paging'=>$paging])['paging_item'] )
            ]
        ];

        //
        return response()->json($data, 200);
    }
}