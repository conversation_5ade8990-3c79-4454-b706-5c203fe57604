<?php
function getMaxEnvFile() {
    // Folder root tempat file .env berada
    $rootFolder = '/www/wwwroot/ripit_client/';
    
    // Pola pencarian untuk file .env
    $envPattern = $rootFolder . '.env.*client.ripit.id';
    $envFiles = glob($envPattern, GLOB_NOSORT);
    
    // Temukan file .env dengan angka tertinggi
    $highestNumber = 0;
    $highestEnvFile = '';
    
    foreach ($envFiles as $file) {
        // Ambil angka dari nama file .env
        preg_match('/\.(\d+)client\.ripit\.id$/', basename($file), $matches);
        
        if (isset($matches[1]) && (int)$matches[1] > $highestNumber) {
            $highestNumber = (int)$matches[1];
            $highestEnvFile = basename($file); // Nama file .env dengan angka tertinggi
        }
    }
    
    // Tampilkan nama file .env dengan angka tertinggi
    if ($highestEnvFile) {
        $nextNumber = $highestNumber + 1; // Tambahkan 1 ke angka tertinggi
        $nextEnvFile = $rootFolder . ".env." . $nextNumber . "client.ripit.id"; // Buat nama file baru
        
        // Dapatkan konten dari file .env yang ada
        $content = file_get_contents($rootFolder . $highestEnvFile);
        
        // Ganti isi dengan angka baru
        $content = preg_replace('/DB_DATABASE=\d+_ripit/', 'DB_DATABASE=' . $nextNumber . '_ripit', $content);
        $content = preg_replace('/DB_USERNAME=\d+_ripit/', 'DB_USERNAME=' . $nextNumber . '_ripit', $content);
        $content = preg_replace('/DB_PASSWORD=\d+_ripit123123123/', 'DB_PASSWORD=' . $nextNumber . '_ripit123123123', $content);
        
        // Buat file baru dengan konten yang telah dimodifikasi
        file_put_contents($nextEnvFile, $content);
        
        // Ubah pemilik file menjadi www
        chown($nextEnvFile, 'www'); // Ubah pemilik file
        chgrp($nextEnvFile, 'www'); // Ubah grup file
        
        echo "File .env dengan angka tertinggi adalah: " . $highestEnvFile . "\n";
        echo "Nama file .env yang berikutnya adalah: " . basename($nextEnvFile) . "\n"; // Tampilkan nama file baru
        echo "File baru telah dibuat: " . basename($nextEnvFile) . "\n";
    } else {
        echo "Tidak ada file .env yang ditemukan.\n";
    }
}

// Panggil fungsi
getMaxEnvFile();
?>
