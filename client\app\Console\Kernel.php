<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use <PERSON><PERSON>\Lumen\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
        Commands\ControllerMakeCommand::class,
        Commands\ModelMakeCommand::class,
        'App\Console\Commands\ClearViewCache',
        'App\Console\Commands\dbcreate'
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //
    }
}
