<?php
namespace App\Http\Controllers\models\po;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\po_request as tblPoRequest;
use App\Http\Controllers\config\index as Config;

class permintaan extends Controller
{
    //
    public function main($requset)
    {
        $Config = new Config;

        $newid = $Config->createnewidnew([
            'value'         =>  tblPoRequest::count(),
            'length'        =>  9
        ]);

        $addnew->id                 =   $newid;
        $addnew->type               =   $requset->type;
        $addnew->token              =   md5($newid);
        $addnew->code               =   "RPO" . date('Ymd', time()) . tblPoRequest::count();
        $addnew->user_id            =   $requset->user_id;
        $addnew->progress           =   0;
        $addnew->status             =   1;
        $addnew->save();
    }
}