<?php
namespace App\Http\Controllers\home\inventory\materials;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\materials as tblMaterials;
use App\Http\Controllers\config\index as Config;

class manage extends Controller
{
    //
    public function create(Request $request)
    {
        $Config = new Config;


        if($request->type == 'new')
        {
            //add
            $add = $this->add($request);

            return $add;
        }

        //edit
        $edit = $this->edit($request);
        return $edit;
        
    }

    public function add($request)
    {

        $check = tblMaterials::where([
            'code'      =>  trim($request->kode)
        ])->count();

        if( $check > 0)
        {
            $data = [
                "message"       =>  "Kode bahan baku sudah terdaftar",
                "focus"           =>  "kode",
                "type"          =>  "text"
            ];

            return response()->json($data,401);
        }

        $checkname = tblMaterials::where([
            'name'          =>  trim($request->name),
            'suplier'       =>  trim($request->suplier_selected)
        ])
        ->count();

        if( $checkname > 0)
        {
            $data = [
                "message"       =>  "Nama Bahan Baku sudah ada pada suplier yang sama",
                "focus"         =>  "name",
                "type"          =>  "text"
            ];

            return response()->json($data,401);
        }


        //add bahan baku
        $add = new \App\Http\Controllers\models\materials;
        $add = $add->sbb($request);

        $data = [
            "message"       =>  "Data berhasil disimpan"
        ];

        return response()->json($data,200);
    }


    public function edit($request)
    {
        $Config = new Config;

        //CHECK NAME
        $checkname = tblMaterials::where([
            ['id', '<>', $request->id],
            ['name', '=', trim($request->name)],
            ['suplier', '=', trim($request->suplier_selected)]
        ])
        ->count();

        if( $checkname > 0)
        {
            $data = [
                "message"       =>  "Nama Bahan Baku sudah ada pada suplier yang sama",
                "focus"         =>  "name",
                "type"          =>  "text"
            ];

            return response()->json($data,401);
        }

        //
        $updata = tblMaterials::where([
            "id"            =>  trim($request->id)
        ])
        ->update([
            "type"          =>  trim($request->type_selected),
            "categories"    =>  trim($request->categori_selected),
            "search"        =>  trim($request->name) . ";" . trim($request->kode_edit),
            "name"          =>  trim($request->name),
            "units"         =>  trim($request->unit_selected),
            "stock"         =>  $Config->numbers(trim($request->stock)),
            "limitstock"    =>  $Config->numbers(trim($request->limit_stock)),
            "buffer"        =>  trim($request->buffer_stock),
            "safety"        =>  trim($request->safety_stock),
            "description"   =>  trim($request->description),
            "suplier"       =>  trim($request->suplier_selected)
        ]);


        $data = [
            "message"           =>  "Data berahasil diperbaharui"
        ];

        return response()->json($data,200);

    }


    //show
    public function show(Request $request)
    {
        $Config = new Config;

        //
        $id = trim($request->id);

        //
        $getdata = tblMaterials::from("materials as m")
        ->select(
            "m.id", "m.token", "m.code", "m.name", "m.type", "m.categories", "m.units", "m.stock","m.limitstock", "m.description", "m.created_at", "m.stock", "m.buffer", "m.safety", "m.suplier"
        )
        ->where([
            ['m.id', '=', $id]
        ])
        ->first();

        $list = [
            "id"        =>  $getdata->id,
            "code"      =>  $getdata->code,
            "date"      =>  $Config->timeago($getdata->created_at),
            "name"      =>  $getdata->name,
            "description"   =>  $getdata->description,
            "unit"          =>  $getdata->units,
            "categori"      =>  $getdata->categories,
            "type"          =>  $getdata->type,
            "stock"         =>  $getdata->stock,
            "limitstock"    =>  $getdata->limitstock,
            "buffer"        =>  $getdata->buffer,
            "safety"        =>  $getdata->safety,
            "suplier"       =>  $getdata->suplier
        ];

        $data = [
            "message"       =>  "",
            "response"      =>  $list
        ];

        return response()->json($data, 200);
    }
}