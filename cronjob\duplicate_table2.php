<?php
$servername = "***************";
$username = "ripit";
$password = "client_ripit";
$conn = new mysqli($servername, $username, $password);

// Cek koneksi
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Query untuk mendapatkan semua database yang berakhiran dengan '_ripit'
$sql = "SHOW DATABASES LIKE '%\_ripit'";
$result = $conn->query($sql);

$max_number = 0;

if ($result->num_rows > 0) {
    while ($row = $result->fetch_array()) {
        $db_name = $row[0];
        
        // Ekstrak angka sebelum '_ripit' menggunakan regex
        if (preg_match('/(\d+)_ripit$/', $db_name, $matches)) {
            $number = (int)$matches[1];

            // Cari angka terbesar
            if ($number > $max_number) {
                $max_number = $number;
            }
        }
    }
}

// Set database tujuan sebagai angka terbesar berikutnya
$db_tujuan = ($max_number) . '_ripit';

// Define database master and client names
$dbmaster = "master";  // Nama database master
$dbclient = $db_tujuan;    // Nama database client dari parameter

// Buat koneksi ke database master
$conn_master = new mysqli($servername, $username, $password, $dbmaster);

// Cek koneksi ke database master
if ($conn_master->connect_error) {
    die("Connection to master failed: " . $conn_master->connect_error);
} else {
    echo "Connected to master successfully\n";
}

// Buat koneksi ke database client
$conn_client = new mysqli($servername, $username, $password, $dbclient);

// Cek koneksi ke database client
if ($conn_client->connect_error) {
    die("Connection to client failed: " . $conn_client->connect_error);
} else {
    echo "Connected to client successfully\n";
}

// Ambil daftar tabel dari database master
$sql = "SHOW TABLES";
$result = $conn_master->query($sql);

if ($result->num_rows > 0) {
    while ($row = $result->fetch_array()) {
        $table = $row[0];
        
        // Drop table jika sudah ada di database client, kecuali tabel yang dikecualikan
        if (!in_array($table, ['app_origin_provinsis', 'app_origin_cities', 'app_origin_kecamatans', 'wa_providers', 'app_bank_lists', 'app_courier_lists', 'app_metode_payments', 'app_courier_configs'])) {
            $conn_client->query("DROP TABLE IF EXISTS $dbclient.$table");
        }
        
        // Duplicate struktur tabel
        $conn_client->query("CREATE TABLE IF NOT EXISTS $dbclient.$table LIKE $dbmaster.$table");
        
        // Copy data only for specified tables
        if (in_array($table, ['app_origin_provinsis', 'app_origin_cities', 'app_origin_kecamatans', 'wa_providers', 'app_bank_lists', 'app_courier_lists', 'app_metode_payments', 'app_courier_configs'])) {
            // Clear existing data and copy from master
            $conn_client->query("DELETE FROM $dbclient.$table");
            $conn_client->query("INSERT INTO $dbclient.$table SELECT * FROM $dbmaster.$table");
            echo "Data for table $table duplicated successfully\n";
        } else {
            echo "Structure for table $table duplicated successfully (without data)\n";
        }
    }
} else {
    echo "No tables found in master database.";
}

$conn_master->close();
$conn_client->close();
?>
