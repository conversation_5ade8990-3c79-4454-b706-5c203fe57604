<?php
namespace App\Http\Controllers\testing;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Exception;
use App\Http\Controllers\config\index as Config;
use App\Http\Controllers\config\errors as ConfigErrors;


class readCsvFile extends Controller
{


    public function index(){
        $Config = new Config;
        $ConfigErrors = new ConfigErrors;

        try{

            // read csv
            $read = $Config->readCSVTest([
                'file'      =>  'testing3.csv'
            ]);

            //show items data
            if($read){

                $no = 0;
                $countheader = count($read['header']) - 1;
                $headers = $read['header'];
                $items = $read['items'];
            
                foreach( $items as &$item )
                {
                    foreach( $headers as $key => $rowx )
                    {

                        //change type data string to integer
                        if($rowx=='id' || $rowx == 'quantity' || $rowx == 'total'){
                            $item[$key] = (int)$item[$key];
                        }

                        $item[$rowx] = $item[$key];
                        unset($item[$key]);
                    }
                }

                // array_walk($items, function (& $item) use (&$headers) {
                //     $item['new_key'] = $item[$key];
                //     unset($item['old_key']);
                // });

                // $data = [
                //     'header'    =>  $headers,
                //     'items'     =>  $items
                // ];

                // dd($data);
                // $data = [
                //     'list'      =>  $items
                // ];

                // return response()->json($data, 200);
                // dd($test[0]);

                // foreach ($headers as $key) {
                //     $items[] = $key;
                // }

                // for($i=0;$i<=$countheader;$i++){
                //     $
                // }
    
                //
                // dd($read['header']);
                $data = [
                    'header'        =>  $read['header'],
                    'items'          =>  $items,
                    'count'         =>  $read['count'],
                    'count_header'  =>  $countheader,
                    'dir'           =>  $read['dir']
                ];
    
                return response()->json($data,200);
            }

            // if row not found
            $data = $ConfigErrors->main([
                'code'          =>  404
            ]);

            return response()->json($data,500);
        }
        catch(Exception $error){

            $data = $ConfigErrors->main([
                'message'       =>  $error->getMessage(),
                'code'          =>  500
            ]);

            return response()->json($data,500);
        }
        
    }

        //
    public function readCSV($csvFile, $array)
    {
        $file_handle = fopen($csvFile, 'r');
        while (!feof($file_handle)) {
            $line_of_text[] = fgetcsv($file_handle, 0, $array['delimiter']);
            }
            fclose($file_handle);
            return $line_of_text;
    }

}