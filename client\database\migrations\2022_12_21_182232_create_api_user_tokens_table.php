<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApiUserTokensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('api_user_tokens', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->integer('type');
            $table->string('token');
            $table->integer('company_id');
            $table->integer('level');
            $table->integer('sublevel');
            $table->integer('published');
            $table->integer('expired');
            $table->string('expired_date');
            $table->string('expired_time');
            $table->timestamps();
            $table->integer('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('api_user_tokens');
    }
}
