<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class RabbitTestController extends Controller
{
   
   public function send(Request $request)
{
    try {
        $connection = new AMQPStreamConnection(
            '127.0.0.1',  // Host RabbitMQ
            5672,               // Port AMQP
            'ripit',            // Username
            'Ripit123456',      // Password
            'ripit'             // Virtual host
        );

        $channel = $connection->channel();

        $exchange = 'amq.direct';  // Exchange yang dipakai
        $routingKey = 'ripit';     // Routing key sesuai binding queue

        // Buat pesan dummy
        $data = ['message' => 'Test kirim pesan dari Laravel'];

        $msg = new AMQPMessage(json_encode($data));

        // Publish pesan
        $channel->basic_publish($msg, $exchange, $routingKey);

        $channel->close();
        $connection->close();

        return response()->json([
            'status' => 'success',
            'message' => 'Pesan berhasil dikirim ke RabbitMQ',
            'data' => $data
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage()
        ], 500);
    }
}

}
