<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Broadcast;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
class BroadcastController extends Controller
{
 public function sendBroadcast(Request $request)
{
    try {
        $data = $request->validate([
            'act' => 'required|in:ripit_send_wa',
            'phone' => 'required|string',
            'phone_cs' => 'required|string',
            'type' => 'required|string|in:text,jpg,png,pdf,xlsx',
            'file_url' => 'nullable|url',
            'file_bc' => 'nullable|url',
            'count_process' => 'nullable|integer',
            'delay_process' => 'nullable|integer',
            'hour_start' => 'nullable|integer',
            'hour_end' => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
        ]);

        Log::info('Broadcast request:', $data);

        $broadcast = Broadcast::create([
            ...$data,
            'status' => 'pending',
        ]);

        return response()->json([
            'code' => 1,
            'msg' => 'valid',
            'id' => $broadcast->id,
            'result' => [
                'msg' => 'Success add broadcast'
            ]
        ]);
    } catch (ValidationException $e) {
        return response()->json([
            'code' => 0,
            'message' => 'Validation error',
            'errors' => $e->errors()
        ], 422);
    } catch (\Exception $e) {
        Log::error('Broadcast creation failed: ' . $e->getMessage());

        return response()->json([
            'code' => 0,
            'message' => 'Failed to add broadcast: ' . $e->getMessage(),
            'result' => []
        ], 500);
    }
}

   
public function cronBroadcast()
{
    $now = Carbon::now('Asia/Jakarta');
    $currentHour = $now->hour;

    // Log untuk debug jam sekarang
    Log::info('Cron run at hour: ' . $currentHour);

    $broadcasts = Broadcast::where('status', 'pending')
        ->where(function ($q) use ($now) {
            $q->whereNull('start_date')
              ->orWhereDate('start_date', '<=', $now->toDateString());
        })
        ->where(function ($q) use ($currentHour) {
            $q->whereNull('hour_start')
              ->orWhere(function ($q2) use ($currentHour) {
                  $q2->where(function ($q3) use ($currentHour) {
                      // Case normal: hour_start <= hour_end
                      $q3->whereColumn('hour_start', '<=', 'hour_end')
                          ->where('hour_start', '<=', $currentHour)
                          ->where('hour_end', '>=', $currentHour);
                  })->orWhere(function ($q3) use ($currentHour) {
                      // Case malam ke pagi: hour_start > hour_end
                      $q3->whereColumn('hour_start', '>', 'hour_end')
                          ->where(function ($q4) use ($currentHour) {
                              $q4->where('hour_start', '<=', $currentHour)
                                  ->orWhere('hour_end', '>=', $currentHour);
                          });
                  });
              });
        })
        // ->limit(3)
        ->get();

    Log::info('Broadcasts fetched: ' . $broadcasts->count());

    foreach ($broadcasts as $broadcast) {
        $broadcast->status = 'processing';
        $broadcast->save();
        
        if ($broadcast->file_bc) {
            $csv = @file_get_contents($broadcast->file_bc);

            if (!$csv) {
                Log::error('CSV file cannot be loaded', [
                    'file' => $broadcast->file_bc,
                    'broadcast_id' => $broadcast->id,
                ]);
                $broadcast->status = 'failed';
                $broadcast->save();
                continue;
            }

            $temp = storage_path('app/temp_' . $broadcast->id . '.csv');
            file_put_contents($temp, $csv);
            $rows = array_map('str_getcsv', file($temp));

            if (count($rows) <= 1) {
                Log::error('CSV has no data rows', [
                    'broadcast_id' => $broadcast->id,
                    'file' => $broadcast->file_bc
                ]);
                $broadcast->status = 'failed';
                $broadcast->save();
                continue;
            }

            unset($rows[0]); // hapus header

            $audience = count($rows);
            $success = 0;
            $fail = 0;
            $count = 0;
            DB::table('broadcasts')->where('id', $broadcast->id)->update([
                'count_process' => $audience,
            ]);
            
            $broadcast->refresh(); // UPDATE: refresh model agar $broadcast->count_process terbaru terbaca
            
            foreach ($rows as $row) {
                if ($count >= $audience) break; // UPDATE: gunakan $audience untuk batas proses
            
                if (!isset($row[0]) || !isset($row[2])) {
                    Log::warning('Invalid row format, skipping', ['row' => $row]);
                    $fail++;
                    continue;
                }

                $targetPhone = trim($row[0]);
                $message = trim($row[2]);

                try {
                    $response = Http::withHeaders([
                                'Content-Type' => 'application/json',
                            ])->withBody(json_encode([
                                'phone'     => $targetPhone,
                                'caption'   => $message, // langsung isi emoji tanpa encode
                                'phone_cs'  => $broadcast->phone_cs,
                                'file_url'  => $broadcast->file_url,
                                'type'      => $broadcast->type,
                                'act'       => $broadcast->act
                            ], JSON_UNESCAPED_UNICODE), 'application/json')
                            ->post('https://api.konekwa.com/api.html');

                    if ($response->successful()) {
                        $success++;
                        Log::info('Message sent successfully', [
                            'phone' => $targetPhone,
                            'response' => $response->body()
                        ]);
                    } else {
                        $fail++;
                        Log::error('Failed to send message', [
                            'phone' => $targetPhone,
                            'status' => $response->status(),
                            'body' => $response->body()
                        ]);
                    }
                } catch (\Exception $e) {
                    $fail++;
                    Log::error('Exception during broadcast send', [
                        'phone' => $targetPhone,
                        'message' => $e->getMessage()
                    ]);
                }

                $count++;
                sleep($broadcast->delay_process ?? 0);
            }

            $broadcast->status = 'done';
            $broadcast->audience = $audience;
            $broadcast->success = $success;
            $broadcast->fail = $fail;
            $broadcast->save();

            Log::info('Broadcast completed', [
                'broadcast_id' => $broadcast->id,
                'audience' => $audience,
                'success' => $success,
                'fail' => $fail
            ]);
        } else {
            Log::warning('No file_bc found for broadcast', [
                'broadcast_id' => $broadcast->id
            ]);
            $broadcast->status = 'failed';
            $broadcast->save();
        }
    }

    return response()->json(['status' => 'cron done']);
}


// public function cronBroadcast()
// {
//     $now = Carbon::now('Asia/Jakarta');
//     $currentHour = $now->hour;

//     $broadcasts = Broadcast::where('status', 'pending')
//         ->whereDate('created_at', $now->toDateString())
//         ->where(function ($q) use ($currentHour) {
//             $q->whereNull('hour_start')
//               ->orWhere(function ($q2) use ($currentHour) {
//                   $q2->where('hour_start', '<=', $currentHour)
//                      ->where('hour_end', '>=', $currentHour);
//               });
//         })
//         ->where(function ($q) use ($now) {
//             $q->whereNull('start_date')
//               ->orWhereDate('start_date', '<=', $now);
//         })
//         ->limit(3)
//         ->get();

//     foreach ($broadcasts as $broadcast) {
//         $broadcast->status = 'processing';
//         $broadcast->save();

//         if ($broadcast->file_bc) {
//             $csv = @file_get_contents($broadcast->file_bc);

//             if (!$csv) {
//                 Log::error('CSV file cannot be loaded', [
//                     'file' => $broadcast->file_bc,
//                     'broadcast_id' => $broadcast->id,
//                 ]);
//                 $broadcast->status = 'failed';
//                 $broadcast->save();
//                 continue;
//             }

//             $temp = storage_path('app/temp_' . $broadcast->id . '.csv');
//             file_put_contents($temp, $csv);
//             $rows = array_map('str_getcsv', file($temp));

//             if (count($rows) <= 1) {
//                 Log::error('CSV has no data rows', [
//                     'broadcast_id' => $broadcast->id,
//                     'file' => $broadcast->file_bc
//                 ]);
//                 $broadcast->status = 'failed';
//                 $broadcast->save();
//                 continue;
//             }

//             unset($rows[0]); // hapus header

//             $audience = count($rows);
//             $success = 0;
//             $fail = 0;
//             $count = 0;

//             foreach ($rows as $row) {
//                 if ($broadcast->count_process && $count >= $broadcast->count_process) break;

//                 if (!isset($row[0]) || !isset($row[2])) {
//                     Log::warning('Invalid row format, skipping', ['row' => $row]);
//                     $fail++;
//                     continue;
//                 }

//                 $targetPhone = trim($row[0]);
//                 $message = trim($row[2]);

//                 try {
//                     $response = Http::post('https://api.konekwa.com/api.html', [
//                         'phone' => $targetPhone,
//                         'caption' => $message,
//                         'phone_cs' => $broadcast->phone_cs,
//                         'file_url' => $broadcast->file_url,
//                         'type' => $broadcast->type,
//                         'act' => $broadcast->act
//                     ]);

//                     if ($response->successful()) {
//                         $success++;
//                         Log::info('Message sent successfully', [
//                             'phone' => $targetPhone,
//                             'response' => $response->body()
//                         ]);
//                     } else {
//                         $fail++;
//                         Log::error('Failed to send message', [
//                             'phone' => $targetPhone,
//                             'status' => $response->status(),
//                             'body' => $response->body()
//                         ]);
//                     }
//                 } catch (\Exception $e) {
//                     $fail++;
//                     Log::error('Exception during broadcast send', [
//                         'phone' => $targetPhone,
//                         'message' => $e->getMessage()
//                     ]);
//                 }

//                 $count++;
//                 sleep($broadcast->delay_process ?? 0);
//             }

//             $broadcast->status = 'done';
//             $broadcast->audience = $audience;
//             $broadcast->success = $success;
//             $broadcast->fail = $fail;
//             $broadcast->save();

//             Log::info('Broadcast completed', [
//                 'broadcast_id' => $broadcast->id,
//                 'audience' => $audience,
//                 'success' => $success,
//                 'fail' => $fail
//             ]);
//         } else {
//             Log::warning('No file_bc found for broadcast', [
//                 'broadcast_id' => $broadcast->id
//             ]);
//             $broadcast->status = 'failed';
//             $broadcast->save();
//         }
//     }

//     return response()->json(['status' => 'cron done']);
// }

   public function getBroadcastResult(Request $request)
{
    $data = $request->validate([
        'phone' => 'required|string'
    ]);

    $broadcasts = Broadcast::where('phone', $data['phone'])->get();

    $results = [];

    foreach ($broadcasts as $broadcast) {
        $results[] = [
            'broadcast_id' => $broadcast->id,
            'audience' => $broadcast->audience ?? '-', 
            'success' => $broadcast->success ?? '-',
            'fail' => $broadcast->fail ?? '-',
            'replay' =>$broadcast->reply ?? '-',
        ];
    }
    
    
    return response()->json($results);
}


}
