<?php
namespace App\Http\Controllers\customers;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\config\index as Config;
use App\customers as tblData;
use App\customer_tags as tblCustomerTags;
use App\customer_logs as tblCustomerLogs;
use App\orders as tblOrders;
use App\Http\Controllers\access\manage as Refresh;
use DB;

class table extends Controller
{
    //
    public function main(Request $request)
    {
        //default config
        $Config = new Config;

        //ceking refresh
        // $Refresh = new Refresh;
        // $Refresh = $Refresh->refresh();

        $account = new \App\Http\Controllers\account\index;
        $account = $account->viewtype([
            'type'      =>  'key',
            'token'     =>  $request->header('key')
        ]);

        //request
        $paging = trim($request->pg);
        $search = '%' . trim($request->src) . '%';
        $sort_name = trim($request->sort);

        $taging = trim($request->taging);
        $register = trim($request->reg);
        $progress = trim($request->progress);
        $source = trim($request->source);
        $companyid = trim($request->companyid);
        $csid = trim($request->csid);


        if( $register != '')
        {
            $register = explode(";", $register);
            $start_date = $register[0];
            $end_date = $register[1];

            $end_date = date('Y-m-d', strtotime($end_date . '+1 day') );
        }
        else
        {
            $register = '';
            $start_date = '';
            $end_date = '';
        }

        $taging = explode(',', $taging);

        //cekin table
        // $cektable = DB::table('customers');
        // foreach($taging as $tag)
        // {
        //     $cektable->orWhere('taging', 'like', '%' . $tag . '%');
        //     if( $progress != '-1')
        //     {
        //         $cektable->where(['progress'=>$progress]);
        //     }
        //     if( $source != '-1')
        //     {
        //         $cektable->where(['source'=>$source]);
        //     }
        //     if( $companyid != '-1')
        //     {
        //         $cektable->where(['company_id'=>$companyid]);
        //     }
        //     if( $csid != '-1')
        //     {
        //         $cektable->where(['user_id'=>$csid]);
        //     }
        //     if( $register != '')
        //     {
        //         $cektable->whereBetween('created_at', [$start_date, $end_date]);
        //     }
        //     $cektable->where('search','like',$search);

        // }
        // $cektable = $cektable->count();
        
        $getdata = tblData::from('customers as c')
        ->select(
            'c.id', 'c.token', 'c.name', 'c.phone', 'c.email', 'c.gender', 'c.taging', 'c.created_at as date',
            'cp.id as progress_id', 'cp.name as progress_name', 'cp.color as progress_color',
            'c.user_id as admin_id', 'u.name as admin_name',
            'cs.name as source',
            'uc.name as company_name'
        )
        ->leftJoin('customer_progresses as cp', function($join)
        {
            $join->on('cp.id', '=', 'c.progress')
            ->where('cp.status', '=', 1);
        })
        ->leftJoin('users as u', function($join)
        {
            $join->on('u.id', '=', 'c.user_id');
        })
        ->leftJoin('customer_sources as cs', function($join)
        {
            $join->on('cs.id', '=', 'c.source');
        })
        ->leftJoin('user_companies as uc', function($join)
        {
            $join->on('uc.id', '=', 'c.company_id');
        });
        foreach($taging as $tag)
        {
            $getdata->orWhere('c.taging', 'like', '%' . $tag . '%');
            if( $progress != '-1')
            {
                $getdata->where(['c.progress'=>$progress]);
            }
            if( $source != '-1')
            {
                $getdata->where(['c.source'=>$source]);
            }
            if( $companyid != '-1')
            {
                $getdata->where(['c.company_id'=>$companyid]);
            }
            if( $csid != '-1')
            {
                $getdata->where(['c.user_id'=>$csid]);
            }
            if( $register != '')
            {
                $getdata->whereBetween('c.created_at', [$start_date, $end_date]);
            }
            $getdata->where('c.search', 'like', $search);
        }

        $cektable = $getdata->count();


        if( $cektable > 0)
        {
            $status = 200;


            // //
            // $getlist = tblData::from('customers as c')
            // ->select(
            //     'c.id', 'c.token', 'c.name', 'c.phone', 'c.email', 'c.gender', 'c.taging', 'c.created_at as date',
            //     'cp.id as progress_id', 'cp.name as progress_name', 'cp.color as progress_color',
            //     'c.user_id as admin_id', 'u.name as admin_name',
            //     'cs.name as source',
            //     'uc.name as company_name'
            // )
            // ->leftJoin('customer_progresses as cp', function($join)
            // {
            //     $join->on('cp.id', '=', 'c.progress')
            //     ->where('cp.status', '=', 1);
            // })
            // ->leftJoin('users as u', function($join)
            // {
            //     $join->on('u.id', '=', 'c.user_id');
            // })
            // ->leftJoin('customer_sources as cs', function($join)
            // {
            //     $join->on('cs.id', '=', 'c.source');
            // })
            // ->leftJoin('user_companies as uc', function($join)
            // {
            //     $join->on('uc.id', '=', 'c.company_id');
            // });
            // foreach($taging as $tag)
            // {
            //     $getlist->orWhere('c.taging', 'like', '%' . $tag . '%');
            //     if( $progress != '-1')
            //     {
            //         $getlist->where(['c.progress'=>$progress]);
            //     }
            //     if( $source != '-1')
            //     {
            //         $getlist->where(['c.source'=>$source]);
            //     }
            //     if( $companyid != '-1')
            //     {
            //         $getlist->where(['c.company_id'=>$companyid]);
            //     }
            //     if( $csid != '-1')
            //     {
            //         $getlist->where(['c.user_id'=>$csid]);
            //     }
            //     if( $register != '')
            //     {
            //         $getlist->whereBetween('c.created_at', [$start_date, $end_date]);
            //     }
            //     $getlist->where('c.search', 'like', $search);
            // }
            
            $getlist = $getdata->orderBy('c.id', 'desc')
            ->orderBy('c.name', $sort_name)
            ->take($Config->table(['paging'=>$paging])['paging_item'])
            ->skip($Config->table(['paging'=>$paging])['paging_limit'])
            ->get();


            foreach($getlist as $row)
            {

                $gettag = $row->taging === '' ? '' : DB::table('customer_tags')->whereIn('id', json_decode($row->taging) )->get();


                $admin = explode(' ', $row->admin_name);

                $getnote = DB::table('customer_notes')
                ->where([
                    'customer_id'            =>  $row->id
                ])->orderBy('id', 'desc')->first();

                $getod = tblOrders::where([
                    'customer_id'           =>  $row->id,
                    'paid'                  =>  1,
                    'status'                =>  1   
                ])->count();

                $list[] = [
                    'id'                    =>  $row->id,
                    'url'                   =>  $row->token,
                    'customer_name'         =>  $row->name,
                    'customer_gender'       =>  $row->gender === 1 ? 'male' : 'female',
                    'customer_phone'        =>  $row->phone,
                    'customer_email'        =>  $row->email,
                    'progress_id'           =>  $row->progress_id,
                    'progress_name'         =>  $row->progress_name,
                    'progress_color'        =>  $row->progress_color,
                    'taging'                =>  $gettag,
                    'source'                =>  $row->source,
                    'note'                  =>  $getnote === null ? '' : $getnote->text,
                    'admin_name'            =>  $admin[0],
                    'admin_id'              =>  $row->admin_id,
                    'date'                  =>  $Config->timeago($row->date),
                    'orders'                =>  $getod,
                    'company_name'          =>  $row->company_name
                ];
            }

            $message = '';
            $response = [
                'list'          =>  $list,
                'paging'        =>  $paging,
                'total'         =>  $cektable,
                'countpage'     =>  ceil($cektable / $Config->table(['paging'=>$paging])['paging_item'] )
            ];

        }
        else
        {
            $status = 404;
            $message = 'Data tidak ditemukan';
        }

        //
        $data = [
            // 'refresh'           =>  $Refresh,
            'message'           =>  $message,
            'response'          =>  $status === 200 ? $response : '',
            'cektable'          =>  $cektable
        ];

        return response()->json($data, $status);
    }
}