<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Invoice</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      margin: 0;
      padding: 40px;
      color: #1a1a1a;
      font-size: 13px;
    }
    .header-bar {
      border-top: 4px solid #00B388;
      margin-bottom: 30px;
    }
    .logo {
      float: right;
      width: 100px;
      margin-top: -25px;
    }
    .clearfix::after {
      content: "";
      display: table;
      clear: both;
    }
    h2, h3 {
      margin: 0;
    }
    .section {
      margin-top: 25px;
    }
    .bold {
      font-weight: bold;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    table th, table td {
      padding: 6px 0;
      text-align: left;
    }
    table th {
      font-weight: 600;
      color: #666;
      border-bottom: 1px solid #ddd;
      font-size: 12px;
    }
    table td {
      border-bottom: 1px solid #eee;
      font-size: 13px;
    }
    .text-right {
      text-align: right;
    }
    .summary-table {
      margin-top: 20px;
      float: right;
      width: 300px;
      font-size: 13px;
    }
    .summary-table td {
      padding: 4px 0;
    }
    .footer-note {
      margin-top: 60px;
      font-size: 11px;
      color: #888;
    }
    a {
      color: #00B388;
      text-decoration: underline;
      font-size: 13px;
    }
  </style>
</head>
<body>

  <div class="header-bar"></div>

  <div class="clearfix">
@if($logo)
  <img src="{{ $logo }}" class="logo" width="100" />
@else
  <div class="logo" style="width:100px;height:100px;border:1px solid #ccc;">LOGO</div>
@endif

  </div>

  <div class="section">
    <h2>Invoice</h2>
    <table style="margin-top: 10px;">
      <tr>
        <td>
          <span class="bold">Invoice number </span>{{ $result['invoice_number'] }}
         <br/>
            <span class="bold">Date of issue </span>{{ \Carbon\Carbon::parse($result['issue_date'])->format('M d, Y') }}
         <br/>
            <span class="bold"> Date due </span>{{ \Carbon\Carbon::parse($result['due_date'])->format('M d, Y') }}<br />
        </td>
      </tr>
    </table>
  </div>

  <div class="section clearfix">
    <table>
      <tr>
        <td>
          <span class="bold">{{ $result['company']['name'] }}</span><br />
          {!! nl2br(e($result['company']['address'])) !!}<br />
          {{ $result['company']['email'] }}
        </td>
        <td>
          <span class="bold">Bill to</span><br />
          {{ $result['bill_to']['name'] }}<br />
          {{ $result['bill_to']['email'] }}
        </td>
      </tr>
    </table>
  </div>

  <div class="section">
    <h3 style="margin-bottom: 5px;">
      Rp. {{ number_format($result['total'], 2, ',', '.') }} IDR due {{ \Carbon\Carbon::parse($result['due_date'])->format('M d, Y') }}
    </h3>
    <a href="#">Pay online</a>
  </div>

  <div class="section">
    <table>
      <thead>
        <tr>
          <th>Description</th>
          <th class="text-right">Qty</th>
          <th class="text-right">Unit price</th>
          <th class="text-right">Amount</th>
        </tr>
      </thead>
      <tbody>
        @foreach($result['items'] as $item)
          <tr>
            <td>{!! nl2br(e($item['description'])) !!}</td>
            <td class="text-right">{{ $item['qty'] }}</td>
            <td class="text-right">Rp. {{ number_format($item['unit_price'], 2, ',', '.') }}</td>
            <td class="text-right">Rp. {{ number_format($item['amount'], 2, ',', '.') }}</td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>

  <table class="summary-table">
    <tr>
      <td class="text-right">Subtotal</td>
      <td class="text-right">Rp. {{ number_format($result['subtotal'], 2, ',', '.') }}</td>
    </tr>
    <tr>
      <td class="text-right">Discount</td>
      <td class="text-right">-Rp. {{ number_format($result['discount'], 2, ',', '.') }}</td>
    </tr>
    <tr>
      <td class="text-right bold">Total</td>
      <td class="text-right bold">Rp. {{ number_format($result['total'], 2, ',', '.') }}</td>
    </tr>
    <tr>
      <td class="text-right bold">Amount due</td>
      <td class="text-right bold">Rp. {{ number_format($result['total'], 2, ',', '.') }}</td>
    </tr>
  </table>

  <div class="footer-note">
  </div>

</body>
</html>
