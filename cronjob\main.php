<?php
$baseDestination = '/www/wwwroot/';

// Fungsi untuk mendapatkan nama direktori tujuan terbaru
function getLatestDestination($baseDestination) {
    $pattern = '/client\.ripit\.id(\d+)/';
    $directories = array_filter(glob($baseDestination . 'client.ripit.id*'), 'is_dir');
    $maxIndex = 1;
    foreach ($directories as $dir) {
        if (preg_match($pattern, basename($dir), $matches)) {
            $index = (int)$matches[1];
            if ($index > $maxIndex) {
                $maxIndex = $index;
            }
        }
    }
    
    return ($maxIndex + 1) . 'client.ripit.id';
}

$nextSiteName = getLatestDestination($baseDestination);
echo "Next site name: $nextSiteName\n";

$destination = $baseDestination . $nextSiteName;
$source = '/www/wwwroot/project_utama';

// Fungsi untuk copy paste kecuali file .env
function copyDirectory($source, $destination) {
    $directory = opendir($source);
    if (!$directory) {
        echo "Failed to open source directory: $source\n";
        return;
    }
    @mkdir($destination, 0755, true);
    
    while (($file = readdir($directory)) !== false) {
        if ($file == '.' || $file == '..' || $file == '.env') continue;
        
        $srcFilePath = $source . DIRECTORY_SEPARATOR . $file;
        $destFilePath = $destination . DIRECTORY_SEPARATOR . $file;
        
        if (is_dir($srcFilePath)) {
            copyDirectory($srcFilePath, $destFilePath);
        } else {
            if (!@copy($srcFilePath, $destFilePath)) {
                echo "Failed to copy $srcFilePath to $destFilePath\n";
                echo "Error: " . error_get_last()['message'] . "\n";
            }
        }
    }
    
    closedir($directory);
}

copyDirectory($source, $destination);

echo "Duplication completed to $destination!\n";
?>
